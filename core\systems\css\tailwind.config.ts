/**
 * 🎨 KilatCSS - Tailwind Configuration
 */

import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './apps/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './core/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'monospace'],
      },
      animation: {
        'kilat-glow': 'kilat-glow 2s ease-in-out infinite alternate',
        'kilat-float': 'kilat-float 3s ease-in-out infinite',
        'kilat-pulse': 'kilat-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'kilat-bounce': 'kilat-bounce 1s infinite',
        'kilat-spin': 'kilat-spin 1s linear infinite',
        'kilat-ping': 'kilat-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
        'kilat-slide-up': 'kilat-slide-up 0.3s ease-out',
        'kilat-slide-down': 'kilat-slide-down 0.3s ease-out',
        'kilat-fade-in': 'kilat-fade-in 0.3s ease-out',
        'kilat-fade-out': 'kilat-fade-out 0.3s ease-out',
      },
      keyframes: {
        'kilat-glow': {
          '0%': { boxShadow: '0 0 5px theme("colors.blue.500")' },
          '100%': { boxShadow: '0 0 20px theme("colors.blue.500"), 0 0 30px theme("colors.blue.500")' },
        },
        'kilat-float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'kilat-pulse': {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.5' },
        },
        'kilat-bounce': {
          '0%, 100%': { transform: 'translateY(-25%)', animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)' },
          '50%': { transform: 'translateY(0)', animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)' },
        },
        'kilat-spin': {
          'from': { transform: 'rotate(0deg)' },
          'to': { transform: 'rotate(360deg)' },
        },
        'kilat-ping': {
          '75%, 100%': { transform: 'scale(2)', opacity: '0' },
        },
        'kilat-slide-up': {
          'from': { transform: 'translateY(100%)', opacity: '0' },
          'to': { transform: 'translateY(0)', opacity: '1' },
        },
        'kilat-slide-down': {
          'from': { transform: 'translateY(-100%)', opacity: '0' },
          'to': { transform: 'translateY(0)', opacity: '1' },
        },
        'kilat-fade-in': {
          'from': { opacity: '0' },
          'to': { opacity: '1' },
        },
        'kilat-fade-out': {
          'from': { opacity: '1' },
          'to': { opacity: '0' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}

export default config
