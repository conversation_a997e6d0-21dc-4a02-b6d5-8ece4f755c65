/**
 * 📄 Static Site Generation (SSG) System
 */

import type { KilatContext, AppRoute } from '../../../types/config'
import { join, dirname } from 'path'
import { existsSync, mkdirSync, writeFileSync } from 'fs'
import { createStaticResponse } from '../../kernel/stream'
import { getCache } from '../cache/manager'

export interface SSGOptions {
  outDir: string
  routes: string[]
  fallback?: boolean
  concurrency?: number
  excludeRoutes?: string[]
  includeStaticAssets?: boolean
  generateSitemap?: boolean
  generateRobots?: boolean
}

export interface SSGResult {
  route: string
  status: 'success' | 'error'
  path: string
  error?: string
}

export interface SSGStats {
  totalRoutes: number
  successCount: number
  errorCount: number
  timeElapsed: number
  results: SSGResult[]
}

/**
 * Generate static site
 */
export async function generateStaticSite(
  context: KilatContext,
  options: SSGOptions
): Promise<SSGStats> {
  console.log('📄 Generating static site...')
  
  const startTime = Date.now()
  const results: SSGResult[] = []
  
  // Create output directory
  if (!existsSync(options.outDir)) {
    mkdirSync(options.outDir, { recursive: true })
  }
  
  // Get routes to generate
  const routesToGenerate = getRoutesToGenerate(context, options)
  
  console.log(`🔍 Found ${routesToGenerate.length} routes to generate`)
  
  // Generate routes with concurrency control
  const concurrency = options.concurrency || 5
  const chunks = chunkArray(routesToGenerate, concurrency)
  
  for (const chunk of chunks) {
    await Promise.all(chunk.map(route => generateRoute(route, context, options, results)))
  }
  
  // Generate additional files
  if (options.generateSitemap) {
    await generateSitemap(context, options, results)
  }
  
  if (options.generateRobots) {
    await generateRobots(context, options)
  }
  
  // Copy static assets
  if (options.includeStaticAssets) {
    await copyStaticAssets(context, options)
  }
  
  // Generate stats
  const successCount = results.filter(r => r.status === 'success').length
  const errorCount = results.filter(r => r.status === 'error').length
  
  const stats: SSGStats = {
    totalRoutes: routesToGenerate.length,
    successCount,
    errorCount,
    timeElapsed: Date.now() - startTime,
    results
  }
  
  console.log(`✅ Static site generation completed in ${stats.timeElapsed}ms`)
  console.log(`📊 Generated ${successCount} pages, ${errorCount} errors`)
  
  return stats
}

/**
 * Get routes to generate
 */
function getRoutesToGenerate(context: KilatContext, options: SSGOptions): AppRoute[] {
  // Get all routes
  const allRoutes = context.routes
  
  // Filter routes based on options
  return allRoutes.filter(route => {
    // Skip API routes
    if (route.path.startsWith('/api/')) {
      return false
    }
    
    // Skip excluded routes
    if (options.excludeRoutes && options.excludeRoutes.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp('^' + pattern.replace('*', '.*') + '$')
        return regex.test(route.path)
      }
      return route.path === pattern
    })) {
      return false
    }
    
    // Include specific routes if provided
    if (options.routes && options.routes.length > 0) {
      return options.routes.some(pattern => {
        if (pattern.includes('*')) {
          const regex = new RegExp('^' + pattern.replace('*', '.*') + '$')
          return regex.test(route.path)
        }
        return route.path === pattern
      })
    }
    
    // Include all routes if no specific routes provided
    return true
  })
}

/**
 * Generate a single route
 */
async function generateRoute(
  route: AppRoute,
  context: KilatContext,
  options: SSGOptions,
  results: SSGResult[]
): Promise<void> {
  try {
    console.log(`🔄 Generating: ${route.path}`)
    
    // Create mock request
    const request = new Request(`http://localhost:3000${route.path}`)
    
    // Create element for rendering
    const { createElement } = await import('react')
    const { default: App } = await import(route.component)
    const element = createElement(App)
    
    // Render to static HTML
    const response = await createStaticResponse(element, context)
    const html = await response.text()
    
    // Determine output path
    let outputPath: string
    if (route.path === '/') {
      outputPath = join(options.outDir, 'index.html')
    } else {
      // Handle dynamic routes
      const cleanPath = route.path.replace(/\[([^\]]+)\]/g, '_$1_')
      
      if (options.fallback) {
        // For fallback mode, create directory with index.html
        outputPath = join(options.outDir, cleanPath.substring(1), 'index.html')
      } else {
        // For non-fallback mode, create .html file
        outputPath = join(options.outDir, `${cleanPath.substring(1)}.html`)
      }
    }
    
    // Create directory if it doesn't exist
    const dir = dirname(outputPath)
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true })
    }
    
    // Write HTML to file
    writeFileSync(outputPath, html)
    
    // Add to results
    results.push({
      route: route.path,
      status: 'success',
      path: outputPath
    })
    
  } catch (error) {
    console.error(`❌ Failed to generate ${route.path}:`, error)
    
    // Add to results
    results.push({
      route: route.path,
      status: 'error',
      path: '',
      error: (error as Error).message
    })
  }
}

/**
 * Generate sitemap.xml
 */
async function generateSitemap(
  context: KilatContext,
  options: SSGOptions,
  results: SSGResult[]
): Promise<void> {
  console.log('🗺️ Generating sitemap.xml...')
  
  const successfulRoutes = results.filter(r => r.status === 'success')
  const baseUrl = context.config.deploy.siteUrl || 'https://example.com'
  
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`
  
  for (const result of successfulRoutes) {
    sitemap += `  <url>
    <loc>${baseUrl}${result.route}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
`
  }
  
  sitemap += `</urlset>`
  
  // Write sitemap
  writeFileSync(join(options.outDir, 'sitemap.xml'), sitemap)
}

/**
 * Generate robots.txt
 */
async function generateRobots(
  context: KilatContext,
  options: SSGOptions
): Promise<void> {
  console.log('🤖 Generating robots.txt...')
  
  const baseUrl = context.config.deploy.siteUrl || 'https://example.com'
  
  const robots = `# Kilat.js Generated Robots.txt
User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml
`
  
  // Write robots.txt
  writeFileSync(join(options.outDir, 'robots.txt'), robots)
}

/**
 * Copy static assets
 */
async function copyStaticAssets(
  context: KilatContext,
  options: SSGOptions
): Promise<void> {
  console.log('📦 Copying static assets...')
  
  // In a real implementation, this would copy files from public/ to the output directory
  // For now, we'll just create a placeholder
  
  const placeholder = `/* Kilat.js Static Assets */`
  
  // Create _kilat directory
  const kilatDir = join(options.outDir, '_kilat')
  if (!existsSync(kilatDir)) {
    mkdirSync(kilatDir, { recursive: true })
  }
  
  // Write placeholder files
  writeFileSync(join(kilatDir, 'runtime.js'), placeholder)
  writeFileSync(join(kilatDir, 'client.js'), placeholder)
  writeFileSync(join(kilatDir, 'styles.css'), placeholder)
}

/**
 * Split array into chunks
 */
function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}
