# 🚀 Kilat.js Advanced Features Examples

This document showcases the advanced features implemented in Kilat.js framework.

## 🖼️ Image Optimization

### Basic Usage

```tsx
import { Image } from '../components/ui/Image'

export default function Gallery() {
  return (
    <div className="grid grid-cols-3 gap-4">
      <Image
        src="/images/hero.jpg"
        alt="Hero image"
        width={400}
        height={300}
        quality={90}
        priority
      />
      
      <Image
        src="/images/gallery-1.jpg"
        alt="Gallery image 1"
        width={300}
        height={200}
        loading="lazy"
        placeholder="blur"
      />
      
      <Image
        src="https://example.com/remote-image.jpg"
        alt="Remote image"
        width={300}
        height={200}
        objectFit="cover"
      />
    </div>
  )
}
```

### Advanced Options

```tsx
<Image
  src="/images/product.jpg"
  alt="Product image"
  width={500}
  height={400}
  layout="responsive"
  quality={85}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
  onLoad={() => console.log('Image loaded')}
  onError={() => console.log('Image failed to load')}
/>
```

## ⚛️ Enhanced Server Components

### Server Component with Async Data

```tsx
// apps/products/page.server.tsx
import { Suspense } from 'react'

async function ProductList() {
  // This runs on the server
  const products = await fetch('https://api.example.com/products').then(r => r.json())
  
  return (
    <div className="grid grid-cols-2 gap-4">
      {products.map(product => (
        <div key={product.id} className="border p-4 rounded">
          <h3>{product.name}</h3>
          <p>{product.description}</p>
          <span>${product.price}</span>
        </div>
      ))}
    </div>
  )
}

export default function ProductsPage() {
  return (
    <div>
      <h1>Products</h1>
      <Suspense fallback={<div>Loading products...</div>}>
        <ProductList />
      </Suspense>
    </div>
  )
}
```

## 🛠️ Enhanced API Routes

### API with Validation and Rate Limiting

```tsx
// apps/api/users/route.ts
import { validateBody, createValidationMiddleware } from '../../../core/systems/api/validator'
import { createRateLimiter, RateLimitPresets } from '../../../core/systems/api/rate-limiter'
import { useMiddleware, corsMiddleware, authMiddleware } from '../../../core/systems/api/middleware'

// Setup middlewares
useMiddleware('cors', corsMiddleware({
  origin: ['http://localhost:3000', 'https://myapp.com'],
  methods: ['GET', 'POST', 'PUT', 'DELETE']
}), { order: 1 })

useMiddleware('rateLimit', createRateLimiter(RateLimitPresets.standard), { order: 2 })

useMiddleware('auth', authMiddleware({
  skipPaths: ['/api/public']
}), { order: 3 })

// Validation schema
const createUserSchema = {
  name: { type: 'string', required: true, min: 2, max: 50 },
  email: { type: 'email', required: true },
  age: { type: 'number', min: 18, max: 120 },
  role: { type: 'string', enum: ['user', 'admin'], required: true }
}

export async function GET(request: Request): Promise<Response> {
  // Get authenticated user from middleware
  const user = (request as any).user
  
  return new Response(JSON.stringify({
    message: 'Users retrieved successfully',
    user: user
  }), {
    headers: { 'Content-Type': 'application/json' }
  })
}

export async function POST(request: Request): Promise<Response> {
  // Validate request body
  const body = await request.json()
  const validation = validateBody(body, createUserSchema)
  
  if (!validation.valid) {
    return new Response(JSON.stringify({
      error: 'Validation failed',
      errors: validation.errors
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    })
  }
  
  // Create user with validated data
  const userData = validation.data
  
  return new Response(JSON.stringify({
    message: 'User created successfully',
    user: userData
  }), {
    status: 201,
    headers: { 'Content-Type': 'application/json' }
  })
}
```

## 🚀 Preloading and Performance

### Register Preload Resources

```tsx
// kilat.config.ts
import { registerPreload, registerCommonResources } from './core/systems/render/preload'

// Register common resources
registerCommonResources(context)

// Register route-specific resources
registerPreload(
  { type: 'script', href: '/js/analytics.js' },
  { priority: 'low' },
  ['/dashboard', '/admin']
)

registerPreload(
  { type: 'font', href: '/fonts/custom-font.woff2' },
  { priority: 'high', inHead: true }
)

// Preconnect to external services
registerPreload(
  { type: 'preconnect', href: 'https://api.example.com' },
  { priority: 'high' }
)
```

## 📄 Static Site Generation

### Generate Static Site

```bash
# Generate all routes
bun export

# Generate specific routes
bun export --routes="/,/about,/products/*"

# Generate with custom output directory
bun export --out ./build

# Generate with sitemap and robots.txt
bun export --sitemap --robots
```

### Programmatic SSG

```tsx
// scripts/generate-static.ts
import { generateStaticSite } from '../core/systems/render/ssg'
import { createKilatApp } from '../index'

async function main() {
  const app = await createKilatApp()
  
  const stats = await generateStaticSite(app.context, {
    outDir: './dist',
    routes: ['/', '/about', '/products/*'],
    fallback: true,
    concurrency: 5,
    generateSitemap: true,
    generateRobots: true,
    includeStaticAssets: true
  })
  
  console.log('Static site generated:', stats)
}

main().catch(console.error)
```

## 🔀 Advanced Routing

### Route with Metadata

```tsx
// apps/blog/[slug]/page.tsx
export const metadata = {
  title: 'Blog Post',
  description: 'Read our latest blog post',
  keywords: ['blog', 'article', 'news'],
  changefreq: 'weekly',
  priority: 0.8
}

export default function BlogPost({ params }: { params: { slug: string } }) {
  return (
    <article>
      <h1>Blog Post: {params.slug}</h1>
      <p>This is a blog post with SEO metadata.</p>
    </article>
  )
}
```

### Nested Layouts

```tsx
// apps/dashboard/layout.tsx
export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="dashboard-layout">
      <nav className="sidebar">
        <a href="/dashboard">Overview</a>
        <a href="/dashboard/users">Users</a>
        <a href="/dashboard/settings">Settings</a>
      </nav>
      <main className="content">
        {children}
      </main>
    </div>
  )
}

// apps/dashboard/users/layout.tsx
export default function UsersLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="users-layout">
      <header>
        <h1>User Management</h1>
      </header>
      {children}
    </div>
  )
}
```

## 🔌 Custom Plugins

### Create Custom Plugin

```tsx
// plugins/analytics.ts
import type { KilatPlugin } from '../types/config'

const analyticsPlugin: KilatPlugin = {
  name: 'custom-analytics',
  version: '1.0.0',
  description: 'Custom analytics tracking',
  
  config: {
    trackingId: process.env.ANALYTICS_ID,
    enableInDev: false
  },
  
  hooks: {
    'plugin:init': async (context) => {
      console.log('Analytics plugin initialized')
    },
    
    'request:before': async (context, data) => {
      if (data?.request) {
        const url = new URL(data.request.url)
        console.log('Page view:', url.pathname)
      }
    }
  }
}

export default analyticsPlugin
```

### Use Custom Plugin

```tsx
// kilat.config.ts
import analyticsPlugin from './plugins/analytics'

export default {
  plugins: [
    {
      ...analyticsPlugin,
      config: {
        trackingId: 'GA-XXXXXXXXX',
        enableInDev: true
      }
    }
  ]
}
```

## 💧 Hydration Strategies

### Selective Hydration

```tsx
// components/InteractiveWidget.tsx
import { registerForHydration } from '../core/systems/hydrate/manager'

export default function InteractiveWidget({ data }: { data: any }) {
  // Register for lazy hydration
  useEffect(() => {
    registerForHydration({
      id: 'interactive-widget',
      component: './components/InteractiveWidget',
      props: { data },
      options: {
        strategy: 'lazy',
        priority: 'medium'
      }
    })
  }, [])
  
  return (
    <div id="interactive-widget" data-interactive>
      <button onClick={() => alert('Interactive!')}>
        Click me
      </button>
    </div>
  )
}
```

These examples demonstrate the powerful features available in Kilat.js for building modern, performant web applications.
