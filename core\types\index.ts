/**
 * 🎯 Kilat.js Core Types
 */

export interface KilatContext {
  request: Request
  response?: Response
  params?: Record<string, string>
  query?: Record<string, string>
  headers?: Record<string, string>
  cookies?: Record<string, string>
  session?: any
  user?: any
  pathname?: string
  searchParams?: URLSearchParams
}

export type RenderMode = 'ssg' | 'ssr' | 'csr'
export type RouteType = 'page' | 'api' | 'layout' | 'loading' | 'error' | 'not-found' | 'middleware'
export type RouteParams = Record<string, string>

export interface RouteConfig {
  path: string
  component: string
  type: RouteType
  renderMode: RenderMode
  isDynamic: boolean
  params: string[]
  middleware?: string[]
  layouts?: string[]
  loading?: string
  error?: string
  notFound?: string
}

export interface StaticGenerationConfig {
  paths?: string[]
  fallback?: boolean | 'blocking'
  revalidate?: number | false
}

export interface MiddlewareConfig {
  matcher?: string | string[]
  runtime?: 'nodejs' | 'edge'
}

export interface PageProps {
  params?: RouteParams
  searchParams?: Record<string, string | string[]>
}

export interface LayoutProps {
  children: React.ReactNode
  params?: RouteParams
}

export interface LoadingProps {
  params?: RouteParams
}

export interface ErrorProps {
  error: Error
  reset: () => void
  params?: RouteParams
}

export interface GenerateStaticParamsResult {
  params: RouteParams
}

export interface GenerateMetadataResult {
  title?: string
  description?: string
  keywords?: string[]
  openGraph?: {
    title?: string
    description?: string
    images?: string[]
  }
}

export interface RenderResult {
  html: string
  css?: string
  js?: string
  metadata?: GenerateMetadataResult
  statusCode?: number
  headers?: Record<string, string>
}

export interface BuildConfig {
  outDir: string
  staticDir: string
  serverDir: string
  clientDir: string
  mode: 'development' | 'production'
  target: 'server' | 'static' | 'hybrid'
}

export interface SpeedRunConfig {
  runtime: 'bun' | 'node'
  port: number
  host: string
  cors: boolean
  compression: boolean
  staticFiles: boolean
  hotReload: boolean
}
