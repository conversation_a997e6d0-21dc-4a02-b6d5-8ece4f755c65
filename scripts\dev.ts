#!/usr/bin/env bun
/**
 * 🔥 Kilat.js Development Server with Advanced Routing
 */

import { createKilatServer } from '../core/server'

async function main() {
  console.log('🔥 Starting Kilat.js Development Server...')
  
  try {
    await createKilatServer({
      runtime: 'bun',
      port: 3000,
      host: 'localhost',
      cors: true,
      compression: true,
      staticFiles: true,
      hotReload: true
    })

    console.log('')
    console.log('📋 Available routes:')
    console.log('  🏠 /              - Homepage')
    console.log('  📄 /about         - About page')
    console.log('  🔑 /login         - Login page')
    console.log('  ✨ /register      - Register page')
    console.log('  📊 /dashboard     - Dashboard (auth required)')
    console.log('  📝 /blog          - Blog index')
    console.log('  📝 /blog/[slug]   - Dynamic blog posts')
    console.log('  🛠️ /api/users     - Users API')
    console.log('  🔐 /api/auth/*    - Authentication API')
    console.log('')
    console.log('🎯 Features enabled:')
    console.log('  ⚡ SSG - Static Site Generation')
    console.log('  🖥️ SSR - Server-Side Rendering')
    console.log('  🌐 CSR - Client-Side Rendering')
    console.log('  🧭 File-based Routing')
    console.log('  🎨 KilatCSS & KilatAnim')
    console.log('  🛡️ Middleware Support')
    console.log('  📱 Responsive Design')
    console.log('')
    
  } catch (error) {
    console.error('❌ Failed to start development server:', error)
    process.exit(1)
  }
}

main()
