/**
 * 🗄️ Cache Manager
 */

import type { KilatContext } from '../../../types/config'

export interface CacheEntry {
  value: any
  expires: number
  tags: string[]
}

class CacheManager {
  private cache = new Map<string, CacheEntry>()
  
  /**
   * Set cache entry
   */
  set(key: string, value: any, ttl = 3600, tags: string[] = []): void {
    const expires = Date.now() + (ttl * 1000)
    this.cache.set(key, { value, expires, tags })
  }
  
  /**
   * Get cache entry
   */
  get(key: string): any {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return undefined
    }
    
    if (Date.now() > entry.expires) {
      this.cache.delete(key)
      return undefined
    }
    
    return entry.value
  }
  
  /**
   * Delete cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }
  
  /**
   * Clear cache by tags
   */
  clearByTags(tags: string[]): void {
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key)
      }
    }
  }
  
  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
  }
  
  /**
   * Get cache stats
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

let cacheManager: CacheManager

/**
 * Initialize cache system
 */
export async function initializeCache(context: KilatContext): Promise<void> {
  cacheManager = new CacheManager()
  console.log('✅ Cache system initialized')
}

/**
 * Get cache manager instance
 */
export function getCache(): CacheManager {
  return cacheManager
}
