/**
 * 🗄️ Advanced Cache Manager with ISR Support
 */

import type { KilatContext } from '../../../types/config'

export interface CacheEntry {
  value: any
  expires: number
  tags: string[]
  staleWhileRevalidate?: number
  lastModified: number
  etag?: string
}

export interface ISRConfig {
  enabled: boolean
  revalidateInterval: number
  staleWhileRevalidate: number
  maxAge: number
}

export interface CacheConfig {
  maxSize: number
  defaultTTL: number
  enableISR: boolean
  isrConfig: ISRConfig
  persistToDisk: boolean
  diskCachePath: string
}

class AdvancedCacheManager {
  private cache = new Map<string, CacheEntry>()
  private config: CacheConfig
  private revalidationQueue = new Set<string>()
  private revalidationTimer?: any

  constructor(config: CacheConfig) {
    this.config = config
    this.setupRevalidationTimer()
  }

  /**
   * Set cache entry with ISR support
   */
  set(key: string, value: any, ttl?: number, tags: string[] = []): void {
    const actualTTL = ttl || this.config.defaultTTL
    const expires = Date.now() + (actualTTL * 1000)
    const staleWhileRevalidate = this.config.isrConfig.staleWhileRevalidate

    const entry: CacheEntry = {
      value,
      expires,
      tags,
      staleWhileRevalidate,
      lastModified: Date.now(),
      etag: this.generateETag(value)
    }

    this.cache.set(key, entry)
    this.enforceMaxSize()
  }

  /**
   * Get cache entry with ISR logic
   */
  get(key: string): { value: any, isStale: boolean } | undefined {
    const entry = this.cache.get(key)

    if (!entry) {
      return undefined
    }

    const now = Date.now()
    const isExpired = now > entry.expires
    const isStale = entry.staleWhileRevalidate &&
                   now > (entry.expires - entry.staleWhileRevalidate * 1000)

    // If expired and no stale-while-revalidate, return undefined
    if (isExpired && !entry.staleWhileRevalidate) {
      this.cache.delete(key)
      return undefined
    }

    // If stale, trigger revalidation
    if (isStale && this.config.enableISR) {
      this.scheduleRevalidation(key)
    }

    return {
      value: entry.value,
      isStale: isStale || isExpired
    }
  }

  /**
   * Delete cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * Clear cache by tags
   */
  clearByTags(tags: string[]): void {
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Schedule revalidation for ISR
   */
  private scheduleRevalidation(key: string): void {
    if (!this.revalidationQueue.has(key)) {
      this.revalidationQueue.add(key)
    }
  }

  /**
   * Setup revalidation timer
   */
  private setupRevalidationTimer(): void {
    if (!this.config.enableISR) return

    this.revalidationTimer = setInterval(() => {
      this.processRevalidationQueue()
    }, this.config.isrConfig.revalidateInterval * 1000)
  }

  /**
   * Process revalidation queue
   */
  private async processRevalidationQueue(): Promise<void> {
    const keysToRevalidate = Array.from(this.revalidationQueue)
    this.revalidationQueue.clear()

    for (const key of keysToRevalidate) {
      try {
        await this.revalidateEntry(key)
      } catch (error) {
        console.error(`❌ Revalidation error for ${key}:`, error)
      }
    }
  }

  /**
   * Revalidate cache entry
   */
  private async revalidateEntry(key: string): Promise<void> {
    // This would trigger the original data fetching logic
    // For now, we'll just extend the TTL
    const entry = this.cache.get(key)
    if (entry) {
      entry.expires = Date.now() + (this.config.defaultTTL * 1000)
      entry.lastModified = Date.now()
      console.log(`🔄 Revalidated cache entry: ${key}`)
    }
  }

  /**
   * Enforce maximum cache size
   */
  private enforceMaxSize(): void {
    if (this.cache.size <= this.config.maxSize) return

    // Remove oldest entries
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => a[1].lastModified - b[1].lastModified)

    const toRemove = entries.slice(0, this.cache.size - this.config.maxSize)
    for (const [key] of toRemove) {
      this.cache.delete(key)
    }
  }

  /**
   * Generate ETag for cache entry
   */
  private generateETag(value: any): string {
    const content = typeof value === 'string' ? value : JSON.stringify(value)
    // Simple hash function for ETag
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return `"${Math.abs(hash).toString(36)}"`
  }

  /**
   * Get cache stats
   */
  getStats() {
    const entries = Array.from(this.cache.values())
    const now = Date.now()

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      expired: entries.filter(e => now > e.expires).length,
      stale: entries.filter(e => e.staleWhileRevalidate &&
                                now > (e.expires - e.staleWhileRevalidate * 1000)).length,
      revalidationQueue: this.revalidationQueue.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * Cleanup and stop timers
   */
  destroy(): void {
    if (this.revalidationTimer) {
      clearInterval(this.revalidationTimer)
    }
    this.cache.clear()
    this.revalidationQueue.clear()
  }
}

let cacheManager: AdvancedCacheManager

/**
 * Initialize cache system
 */
export async function initializeCache(context: KilatContext): Promise<void> {
  const config: CacheConfig = {
    maxSize: 1000,
    defaultTTL: 3600,
    enableISR: context.config.render.mode === 'isr',
    isrConfig: {
      enabled: true,
      revalidateInterval: 60,
      staleWhileRevalidate: 300,
      maxAge: 86400
    },
    persistToDisk: context.mode === 'production',
    diskCachePath: './.kilat/cache'
  }

  cacheManager = new AdvancedCacheManager(config)
  console.log('✅ Advanced cache system initialized')
}

/**
 * Get cache manager instance
 */
export function getCache(): AdvancedCacheManager {
  return cacheManager
}
