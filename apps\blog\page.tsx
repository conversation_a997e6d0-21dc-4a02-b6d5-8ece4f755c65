/**
 * 📝 Blog Index Page
 * List all blog posts with SSG
 */

import React from 'react'
import type { GenerateMetadataResult } from '../../core/types'

interface BlogPost {
  slug: string
  title: string
  excerpt: string
  author: string
  date: string
  tags: string[]
  readTime: string
}

// Mock blog data
const blogPosts: BlogPost[] = [
  {
    slug: 'getting-started',
    title: 'Getting Started with Kilat.js',
    excerpt: 'Learn how to build lightning-fast applications with Kilat.js framework and SpeedRun™ Runtime.',
    author: 'Kilat Team',
    date: '2024-01-25',
    tags: ['tutorial', 'getting-started'],
    readTime: '5 min read'
  },
  {
    slug: 'advanced-routing',
    title: 'Advanced Routing in Kilat.js',
    excerpt: 'Explore the powerful routing system with SSG, SSR, and CSR support for modern web applications.',
    author: 'Kilat Team',
    date: '2024-01-20',
    tags: ['routing', 'advanced'],
    readTime: '8 min read'
  },
  {
    slug: 'performance-optimization',
    title: 'Performance Optimization with SpeedRun™',
    excerpt: 'Discover how SpeedRun™ Runtime delivers unmatched performance and developer experience.',
    author: 'Kilat Team',
    date: '2024-01-15',
    tags: ['performance', 'optimization'],
    readTime: '6 min read'
  }
]

export default function BlogPage() {
  return (
    <>
      {/* Blog Header */}
      <section className="py-20">
        <div className="kilat-container">
          <div className="text-center mb-16 kilat-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Kilat.js <span className="kilat-gradient-text">Blog</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Tutorials, guides, and insights about building lightning-fast web applications 
              with Kilat.js framework and SpeedRun™ Runtime.
            </p>
          </div>

          {/* Featured Post */}
          <div className="mb-16 kilat-fade-in" style={{ animationDelay: '0.2s' }}>
            <div className="kilat-card kilat-glass p-8 kilat-glow-hover">
              <div className="flex items-center space-x-2 mb-4">
                <span className="px-3 py-1 bg-blue-500/20 text-blue-400 text-sm rounded-full">Featured</span>
                <span className="text-gray-400 text-sm">{blogPosts[0].readTime}</span>
              </div>
              
              <h2 className="text-3xl font-bold text-white mb-4">
                <a href={`/blog/${blogPosts[0].slug}`} className="hover:kilat-gradient-text transition-all duration-300">
                  {blogPosts[0].title}
                </a>
              </h2>
              
              <p className="text-gray-300 text-lg mb-6 leading-relaxed">
                {blogPosts[0].excerpt}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-gray-400">
                  <span>By {blogPosts[0].author}</span>
                  <span>•</span>
                  <span>{new Date(blogPosts[0].date).toLocaleDateString()}</span>
                </div>
                
                <a 
                  href={`/blog/${blogPosts[0].slug}`}
                  className="kilat-btn kilat-btn-primary px-6 py-3 kilat-glow-hover"
                >
                  Read More →
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20">
        <div className="kilat-container">
          <h2 className="text-2xl font-bold text-white mb-8 kilat-fade-in">Latest Posts</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.slice(1).map((post, index) => (
              <article 
                key={post.slug}
                className="kilat-card kilat-glass p-6 kilat-glow-hover kilat-fade-in"
                style={{ animationDelay: `${0.4 + index * 0.1}s` }}
              >
                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {post.tags.map(tag => (
                    <span 
                      key={tag}
                      className="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Title */}
                <h3 className="text-xl font-bold text-white mb-3">
                  <a 
                    href={`/blog/${post.slug}`}
                    className="hover:kilat-gradient-text transition-all duration-300"
                  >
                    {post.title}
                  </a>
                </h3>

                {/* Excerpt */}
                <p className="text-gray-300 mb-4 leading-relaxed">
                  {post.excerpt}
                </p>

                {/* Meta */}
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <div className="flex items-center space-x-2">
                    <span>{post.author}</span>
                    <span>•</span>
                    <span>{new Date(post.date).toLocaleDateString()}</span>
                  </div>
                  <span>{post.readTime}</span>
                </div>

                {/* Read More */}
                <div className="mt-4 pt-4 border-t border-white/10">
                  <a 
                    href={`/blog/${post.slug}`}
                    className="text-blue-400 hover:text-blue-300 font-medium transition-colors"
                  >
                    Read more →
                  </a>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="kilat-card kilat-glass p-8 text-center kilat-fade-in">
            <h2 className="text-3xl font-bold text-white mb-4">
              Stay Updated
            </h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Get the latest tutorials, guides, and updates about Kilat.js framework 
              delivered straight to your inbox.
            </p>
            
            <form className="max-w-md mx-auto flex gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="submit"
                className="kilat-btn kilat-btn-primary px-6 py-3 kilat-glow-hover"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>
      </section>
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata(): Promise<GenerateMetadataResult> {
  return {
    title: 'Blog | Kilat.js Framework',
    description: 'Tutorials, guides, and insights about building lightning-fast web applications with Kilat.js framework and SpeedRun™ Runtime.',
    keywords: ['kilat.js', 'blog', 'tutorials', 'web development', 'performance'],
    openGraph: {
      title: 'Kilat.js Blog - Tutorials & Guides',
      description: 'Learn how to build lightning-fast web applications with Kilat.js framework.',
      images: ['/api/og?title=Kilat.js Blog']
    }
  }
}
