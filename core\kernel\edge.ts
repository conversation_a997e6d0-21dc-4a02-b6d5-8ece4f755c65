/**
 * ⚡ Edge Runtime Handler
 */

import type { KilatContext } from '../../types/config'

export interface EdgeRequest {
  url: string
  method: string
  headers: Headers
  body?: ReadableStream | null
  geo?: {
    country?: string
    region?: string
    city?: string
    latitude?: string
    longitude?: string
  }
}

export interface EdgeResponse {
  status: number
  headers: Headers
  body?: ReadableStream | string | null
}

/**
 * Check if running in edge environment
 */
export function isEdgeRuntime(): boolean {
  // Check for edge runtime indicators
  return (
    typeof EdgeRuntime !== 'undefined' ||
    typeof Deno !== 'undefined' ||
    typeof Cloudflare !== 'undefined' ||
    process.env.VERCEL_REGION !== undefined ||
    process.env.CF_PAGES !== undefined
  )
}

/**
 * Create edge-compatible request handler
 */
export function createEdgeHandler(context: KilatContext) {
  return async function handleEdgeRequest(request: Request): Promise<Response> {
    try {
      // Convert to edge request format
      const edgeRequest: EdgeRequest = {
        url: request.url,
        method: request.method,
        headers: request.headers,
        body: request.body,
        geo: extractGeoData(request),
      }
      
      // Process request with edge optimizations
      const response = await processEdgeRequest(edgeRequest, context)
      
      // Convert back to standard Response
      return new Response(response.body, {
        status: response.status,
        headers: response.headers,
      })
      
    } catch (error) {
      console.error('❌ Edge handler error:', error)
      
      return new Response('Internal Server Error', {
        status: 500,
        headers: {
          'Content-Type': 'text/plain',
        },
      })
    }
  }
}

/**
 * Process edge request with optimizations
 */
async function processEdgeRequest(
  request: EdgeRequest,
  context: KilatContext
): Promise<EdgeResponse> {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  // Edge-specific optimizations
  const headers = new Headers()
  
  // Add edge headers
  headers.set('X-Kilat-Edge', 'true')
  headers.set('X-Kilat-Runtime', 'edge')
  
  // Add geo headers if available
  if (request.geo) {
    if (request.geo.country) headers.set('X-Geo-Country', request.geo.country)
    if (request.geo.region) headers.set('X-Geo-Region', request.geo.region)
    if (request.geo.city) headers.set('X-Geo-City', request.geo.city)
  }
  
  // Handle static assets with edge caching
  if (isStaticAsset(pathname)) {
    return handleEdgeStatic(pathname, headers, context)
  }
  
  // Handle API routes with edge functions
  if (pathname.startsWith('/api/')) {
    return handleEdgeAPI(request, headers, context)
  }
  
  // Handle pages with edge SSR
  return handleEdgePage(request, headers, context)
}

/**
 * Handle static assets in edge
 */
async function handleEdgeStatic(
  pathname: string,
  headers: Headers,
  context: KilatContext
): Promise<EdgeResponse> {
  // Set aggressive caching for static assets
  headers.set('Cache-Control', 'public, max-age=31536000, immutable')
  headers.set('CDN-Cache-Control', 'public, max-age=31536000')
  
  // In a real implementation, this would fetch from edge storage
  return {
    status: 404,
    headers,
    body: 'Static asset not found',
  }
}

/**
 * Handle API routes in edge
 */
async function handleEdgeAPI(
  request: EdgeRequest,
  headers: Headers,
  context: KilatContext
): Promise<EdgeResponse> {
  // Set API response headers
  headers.set('Content-Type', 'application/json')
  headers.set('Cache-Control', 'no-store, must-revalidate')
  
  // Mock API response
  const response = {
    message: 'Edge API response',
    timestamp: new Date().toISOString(),
    geo: request.geo,
  }
  
  return {
    status: 200,
    headers,
    body: JSON.stringify(response),
  }
}

/**
 * Handle pages in edge
 */
async function handleEdgePage(
  request: EdgeRequest,
  headers: Headers,
  context: KilatContext
): Promise<EdgeResponse> {
  // Set page response headers
  headers.set('Content-Type', 'text/html; charset=utf-8')
  
  // Edge SSR with minimal HTML
  const html = generateEdgeHTML(request, context)
  
  return {
    status: 200,
    headers,
    body: html,
  }
}

/**
 * Generate minimal HTML for edge
 */
function generateEdgeHTML(request: EdgeRequest, context: KilatContext): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Kilat.js Edge</title>
  <style>
    body { font-family: system-ui, sans-serif; margin: 0; padding: 20px; background: #0a0a0f; color: #fff; }
    .container { max-width: 800px; margin: 0 auto; }
    .header { text-align: center; margin-bottom: 40px; }
    .info { background: #1a1a2e; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .code { background: #16213e; padding: 10px; border-radius: 4px; font-family: monospace; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>⚡ Kilat.js Edge Runtime</h1>
      <p>Running on the edge with minimal latency</p>
    </div>
    
    <div class="info">
      <h3>Request Info</h3>
      <div class="code">
        URL: ${request.url}<br>
        Method: ${request.method}<br>
        ${request.geo ? `Country: ${request.geo.country || 'Unknown'}<br>` : ''}
        ${request.geo ? `Region: ${request.geo.region || 'Unknown'}<br>` : ''}
        Runtime: Edge
      </div>
    </div>
    
    <div class="info">
      <h3>Edge Features</h3>
      <ul>
        <li>✅ Global distribution</li>
        <li>✅ Minimal cold start</li>
        <li>✅ Geo-aware routing</li>
        <li>✅ Edge caching</li>
        <li>✅ Streaming responses</li>
      </ul>
    </div>
  </div>
</body>
</html>`
}

/**
 * Extract geo data from request
 */
function extractGeoData(request: Request): EdgeRequest['geo'] {
  const headers = request.headers
  
  return {
    country: headers.get('cf-ipcountry') || headers.get('x-vercel-ip-country') || undefined,
    region: headers.get('cf-region') || headers.get('x-vercel-ip-country-region') || undefined,
    city: headers.get('cf-ipcity') || headers.get('x-vercel-ip-city') || undefined,
    latitude: headers.get('cf-iplatitude') || headers.get('x-vercel-ip-latitude') || undefined,
    longitude: headers.get('cf-iplongitude') || headers.get('x-vercel-ip-longitude') || undefined,
  }
}

/**
 * Check if path is a static asset
 */
function isStaticAsset(pathname: string): boolean {
  const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2']
  return staticExtensions.some(ext => pathname.endsWith(ext))
}
