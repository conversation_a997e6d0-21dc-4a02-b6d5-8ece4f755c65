/**
 * 📊 Analytics Plugin - Built-in Analytics System
 */

import type { KilatPlugin, KilatContext } from '../../../../types/config'

export interface AnalyticsEvent {
  event: string
  data: Record<string, any>
  timestamp: number
  sessionId: string
  userId?: string
}

class AnalyticsCollector {
  private events: AnalyticsEvent[] = []
  private sessionId: string
  
  constructor() {
    this.sessionId = this.generateSessionId()
  }
  
  track(event: string, data: Record<string, any> = {}, userId?: string): void {
    const analyticsEvent: AnalyticsEvent = {
      event,
      data,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId
    }
    
    this.events.push(analyticsEvent)
    
    // In development, log to console
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Analytics:', analyticsEvent)
    }
  }
  
  getEvents(): AnalyticsEvent[] {
    return [...this.events]
  }
  
  clearEvents(): void {
    this.events = []
  }
  
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }
}

let analyticsCollector: AnalyticsCollector

const analyticsPlugin: KilatPlugin = {
  name: 'analytics',
  version: '1.0.0',
  description: 'Built-in analytics system for tracking user interactions and performance',
  author: 'Kilat.js Team',
  builtin: true,
  
  config: {
    enabled: true,
    trackPageViews: true,
    trackPerformance: true,
    trackErrors: true,
    batchSize: 100,
    flushInterval: 30000, // 30 seconds
  },
  
  hooks: {
    'plugin:init': async (context: KilatContext) => {
      analyticsCollector = new AnalyticsCollector()
      console.log('📊 Analytics plugin initialized')
    },
    
    'server:start': async (context: KilatContext) => {
      analyticsCollector.track('server_start', {
        mode: context.mode,
        engine: context.config.runtime.engine,
        port: context.config.runtime.port
      })
    },
    
    'request:before': async (context: KilatContext, data: any) => {
      if (data?.request) {
        const url = new URL(data.request.url)
        
        // Track page views
        if (!url.pathname.startsWith('/api/') && !url.pathname.startsWith('/_kilat/')) {
          analyticsCollector.track('page_view', {
            path: url.pathname,
            search: url.search,
            referrer: data.request.headers.get('referer'),
            userAgent: data.request.headers.get('user-agent')
          })
        }
        
        // Track API calls
        if (url.pathname.startsWith('/api/')) {
          analyticsCollector.track('api_call', {
            path: url.pathname,
            method: data.request.method,
            userAgent: data.request.headers.get('user-agent')
          })
        }
      }
    },
    
    'render:before': async (context: KilatContext, data: any) => {
      const startTime = Date.now()
      data.renderStartTime = startTime
      
      analyticsCollector.track('render_start', {
        route: data.route?.path,
        component: data.route?.component
      })
    },
    
    'render:after': async (context: KilatContext, data: any) => {
      const endTime = Date.now()
      const renderTime = endTime - (data.renderStartTime || endTime)
      
      analyticsCollector.track('render_complete', {
        route: data.route?.path,
        component: data.route?.component,
        renderTime
      })
    },
    
    'build:start': async (context: KilatContext) => {
      analyticsCollector.track('build_start', {
        mode: context.mode,
        target: context.config.build.target
      })
    },
    
    'build:end': async (context: KilatContext, data: any) => {
      analyticsCollector.track('build_complete', {
        mode: context.mode,
        success: !data?.error,
        buildTime: data?.buildTime
      })
    }
  }
}

/**
 * Get analytics collector instance
 */
export function getAnalytics(): AnalyticsCollector | undefined {
  return analyticsCollector
}

/**
 * Track custom event
 */
export function track(event: string, data: Record<string, any> = {}, userId?: string): void {
  if (analyticsCollector) {
    analyticsCollector.track(event, data, userId)
  }
}

export default analyticsPlugin
