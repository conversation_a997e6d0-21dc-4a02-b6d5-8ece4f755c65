/**
 * 🧩 Generate Command - Scaffold Components, Pages, APIs
 */

import { existsSync, mkdirSync, writeFileSync } from 'fs'
import { join, dirname } from 'path'

type GenerateType = 'page' | 'api' | 'component' | 'layout' | 'middleware' | 'plugin'

export async function generateCommand(args: string[]): Promise<void> {
  if (args.length < 2) {
    console.error('❌ Missing arguments. Usage: kilat generate <type> <name> [options]')
    showGenerateHelp()
    process.exit(1)
  }
  
  const type = args[0] as GenerateType
  const name = args[1]
  
  console.log(`🧩 Generating ${type}: ${name}`)
  
  try {
    switch (type) {
      case 'page':
        await generatePage(name, args.slice(2))
        break
      case 'api':
        await generateApi(name, args.slice(2))
        break
      case 'component':
        await generateComponent(name, args.slice(2))
        break
      case 'layout':
        await generateLayout(name, args.slice(2))
        break
      case 'middleware':
        await generateMiddleware(name, args.slice(2))
        break
      case 'plugin':
        await generatePlugin(name, args.slice(2))
        break
      default:
        console.error(`❌ Unknown type: ${type}`)
        showGenerateHelp()
        process.exit(1)
    }
    
    console.log(`✅ Successfully generated ${type}: ${name}`)
    
  } catch (error) {
    console.error(`❌ Failed to generate ${type}:`, error)
    process.exit(1)
  }
}

/**
 * Generate page
 */
async function generatePage(name: string, args: string[]): Promise<void> {
  const path = join(process.cwd(), 'apps', ...name.split('/'))
  const filePath = join(path, 'page.tsx')
  
  // Create directory if it doesn't exist
  ensureDirectoryExists(path)
  
  // Check if file already exists
  if (existsSync(filePath)) {
    console.error(`❌ Page already exists: ${filePath}`)
    process.exit(1)
  }
  
  // Generate page content
  const content = `/**
 * Page: ${name}
 * Generated by Kilat.js CLI
 */

import React from 'react'

export default function ${toPascalCase(name.split('/').pop() || 'Page')}() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold">${name}</h1>
      <p className="mt-4">This is the ${name} page.</p>
    </div>
  )
}
`
  
  // Write file
  writeFileSync(filePath, content)
  console.log(`📄 Created page: ${filePath}`)
}

/**
 * Generate API route
 */
async function generateApi(name: string, args: string[]): Promise<void> {
  const path = join(process.cwd(), 'apps', 'api', ...name.split('/'))
  const filePath = join(path, 'route.ts')
  
  // Create directory if it doesn't exist
  ensureDirectoryExists(path)
  
  // Check if file already exists
  if (existsSync(filePath)) {
    console.error(`❌ API route already exists: ${filePath}`)
    process.exit(1)
  }
  
  // Generate API content
  const content = `/**
 * API Route: ${name}
 * Generated by Kilat.js CLI
 */

import { ApiResponse } from '${relativePath(path, join(process.cwd(), 'core', 'kernel', 'api-handler'))}'

/**
 * GET /api/${name}
 */
export async function GET(request: Request): Promise<Response> {
  return ApiResponse.json({
    message: 'Hello from ${name} API',
    timestamp: new Date().toISOString()
  })
}

/**
 * POST /api/${name}
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const body = await request.json()
    return ApiResponse.json({
      message: 'Data received',
      data: body
    })
  } catch (error) {
    return ApiResponse.error('Invalid JSON', 400)
  }
}
`
  
  // Write file
  writeFileSync(filePath, content)
  console.log(`📄 Created API route: ${filePath}`)
}

/**
 * Generate component
 */
async function generateComponent(name: string, args: string[]): Promise<void> {
  const type = args.includes('--ui') ? 'ui' : 
               args.includes('--layout') ? 'layout' : 'shared'
  
  const path = join(process.cwd(), 'components', type)
  const filePath = join(path, `${name}.tsx`)
  
  // Create directory if it doesn't exist
  ensureDirectoryExists(path)
  
  // Check if file already exists
  if (existsSync(filePath)) {
    console.error(`❌ Component already exists: ${filePath}`)
    process.exit(1)
  }
  
  // Generate component content
  const content = `/**
 * Component: ${name}
 * Type: ${type}
 * Generated by Kilat.js CLI
 */

import React from 'react'

export interface ${toPascalCase(name)}Props {
  children?: React.ReactNode
  className?: string
}

export function ${toPascalCase(name)}({ children, className = '' }: ${toPascalCase(name)}Props) {
  return (
    <div className={className}>
      {children}
    </div>
  )
}

export default ${toPascalCase(name)}
`
  
  // Write file
  writeFileSync(filePath, content)
  console.log(`📄 Created component: ${filePath}`)
}

/**
 * Generate layout
 */
async function generateLayout(name: string, args: string[]): Promise<void> {
  const path = join(process.cwd(), 'apps', ...name.split('/'))
  const filePath = join(path, 'layout.tsx')
  
  // Create directory if it doesn't exist
  ensureDirectoryExists(path)
  
  // Check if file already exists
  if (existsSync(filePath)) {
    console.error(`❌ Layout already exists: ${filePath}`)
    process.exit(1)
  }
  
  // Generate layout content
  const content = `/**
 * Layout: ${name}
 * Generated by Kilat.js CLI
 */

import React from 'react'

export interface ${toPascalCase(name.split('/').pop() || 'Layout')}Props {
  children: React.ReactNode
}

export default function ${toPascalCase(name.split('/').pop() || 'Layout')}({ children }: ${toPascalCase(name.split('/').pop() || 'Layout')}Props) {
  return (
    <div className="layout-${name.replace(/\//g, '-')}">
      <div className="layout-content">
        {children}
      </div>
    </div>
  )
}
`
  
  // Write file
  writeFileSync(filePath, content)
  console.log(`📄 Created layout: ${filePath}`)
}

/**
 * Generate middleware
 */
async function generateMiddleware(name: string, args: string[]): Promise<void> {
  const path = join(process.cwd(), 'apps', ...name.split('/'))
  const filePath = join(path, 'middleware.ts')
  
  // Create directory if it doesn't exist
  ensureDirectoryExists(path)
  
  // Check if file already exists
  if (existsSync(filePath)) {
    console.error(`❌ Middleware already exists: ${filePath}`)
    process.exit(1)
  }
  
  // Generate middleware content
  const content = `/**
 * Middleware: ${name}
 * Generated by Kilat.js CLI
 */

export async function middleware(request: Request): Promise<Response | null> {
  // Implement your middleware logic here
  // Return null to continue to the next middleware or route handler
  // Return a Response to short-circuit the request
  
  console.log(\`Middleware executed for \${request.url}\`)
  
  // Example: Check for authentication
  // const authHeader = request.headers.get('authorization')
  // if (!authHeader) {
  //   return new Response(JSON.stringify({ error: 'Authentication required' }), {
  //     status: 401,
  //     headers: { 'Content-Type': 'application/json' }
  //   })
  // }
  
  // Continue to next middleware or route handler
  return null
}
`
  
  // Write file
  writeFileSync(filePath, content)
  console.log(`📄 Created middleware: ${filePath}`)
}

/**
 * Generate plugin
 */
async function generatePlugin(name: string, args: string[]): Promise<void> {
  const path = join(process.cwd(), 'plugins')
  const filePath = join(path, `${name}.ts`)
  
  // Create directory if it doesn't exist
  ensureDirectoryExists(path)
  
  // Check if file already exists
  if (existsSync(filePath)) {
    console.error(`❌ Plugin already exists: ${filePath}`)
    process.exit(1)
  }
  
  // Generate plugin content
  const content = `/**
 * Plugin: ${name}
 * Generated by Kilat.js CLI
 */

import type { KilatPlugin, KilatContext } from '../types/config'

/**
 * ${toPascalCase(name)} Plugin
 */
const ${toCamelCase(name)}Plugin: KilatPlugin = {
  name: '${name}',
  version: '1.0.0',
  config: {
    // Plugin-specific configuration
  },
  hooks: {
    'build:start': async () => {
      console.log('🔌 ${name} plugin: build started')
    },
    'build:end': async () => {
      console.log('🔌 ${name} plugin: build completed')
    },
    'dev:start': async () => {
      console.log('🔌 ${name} plugin: dev server started')
    },
    'dev:reload': async () => {
      console.log('🔌 ${name} plugin: reloading')
    }
  }
}

export default ${toCamelCase(name)}Plugin
`
  
  // Write file
  writeFileSync(filePath, content)
  console.log(`📄 Created plugin: ${filePath}`)
}

/**
 * Show generate help
 */
function showGenerateHelp(): void {
  console.log(`
🧩 Kilat.js Generate Command

Usage:
  kilat generate <type> <name> [options]

Types:
  page        Generate a new page
  api         Generate a new API route
  component   Generate a new component
  layout      Generate a new layout
  middleware  Generate a new middleware
  plugin      Generate a new plugin

Examples:
  kilat generate page about
  kilat generate page dashboard/settings
  kilat generate api users
  kilat generate component Button --ui
  kilat generate layout dashboard
  kilat generate middleware auth
  kilat generate plugin analytics

Options:
  --ui        Generate UI component (for component type)
  --layout    Generate layout component (for component type)
`)
}

/**
 * Ensure directory exists
 */
function ensureDirectoryExists(path: string): void {
  if (!existsSync(path)) {
    mkdirSync(path, { recursive: true })
  }
}

/**
 * Convert string to PascalCase
 */
function toPascalCase(str: string): string {
  return str
    .split(/[-_\s./]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
}

/**
 * Convert string to camelCase
 */
function toCamelCase(str: string): string {
  const pascal = toPascalCase(str)
  return pascal.charAt(0).toLowerCase() + pascal.slice(1)
}

/**
 * Get relative path
 */
function relativePath(from: string, to: string): string {
  // Simplified relative path for imports
  return '../../../core/kernel/api-handler'
}
