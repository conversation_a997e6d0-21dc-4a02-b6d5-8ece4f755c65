# Contributing to <PERSON><PERSON>.js

Thank you for your interest in contributing to <PERSON><PERSON>.js! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- [Bun](https://bun.sh) >= 1.0.0
- Node.js >= 18.0.0 (for fallback compatibility)
- Git

### Development Setup

1. Fork and clone the repository:
```bash
git clone https://github.com/kangpcode/kilat.js.git
cd kilat.js
```

2. Install dependencies:
```bash
bun install
```

3. Start development server:
```bash
bun dev
```

## 📁 Project Structure

```
kilat.js/
├── apps/                  # Example application
├── components/            # UI components
├── core/                  # Framework core
│   ├── kernel/            # SpeedRun™ runtime
│   ├── systems/           # Internal subsystems
│   └── tools/             # CLI and dev tools
├── types/                 # TypeScript definitions
└── tests/                 # Test files
```

## 🧪 Testing

Run tests with:
```bash
# Unit tests
bun test

# E2E tests
bun test:e2e

# All tests
bun test:all
```

## 📝 Code Style

- Use TypeScript for all new code
- Follow existing code style and conventions
- Use meaningful variable and function names
- Add J<PERSON> comments for public APIs
- Keep functions small and focused

### Formatting

We use Prettier for code formatting:
```bash
bun format
```

### Linting

We use ESLint for code linting:
```bash
bun lint
```

## 🔌 Plugin Development

To create a new plugin:

1. Create plugin file in `core/systems/plugin/builtin/` or as external module
2. Follow the plugin interface:

```typescript
import type { KilatPlugin } from '../types/config'

const myPlugin: KilatPlugin = {
  name: 'my-plugin',
  version: '1.0.0',
  description: 'My awesome plugin',
  hooks: {
    'plugin:init': async (context) => {
      // Plugin initialization
    }
  }
}

export default myPlugin
```

3. Add tests for your plugin
4. Update documentation

## 📚 Documentation

- Update README.md for user-facing changes
- Add JSDoc comments for new APIs
- Update examples if needed
- Consider adding to the official docs

## 🐛 Bug Reports

When reporting bugs, please include:

- Kilat.js version
- Operating system and version
- Bun/Node.js version
- Steps to reproduce
- Expected vs actual behavior
- Error messages and stack traces

## ✨ Feature Requests

For feature requests:

- Check existing issues first
- Describe the use case
- Explain why it should be in core vs plugin
- Consider implementation complexity
- Provide examples if possible

## 🔄 Pull Request Process

1. Create a feature branch from `main`
2. Make your changes
3. Add tests for new functionality
4. Ensure all tests pass
5. Update documentation
6. Submit pull request

### PR Guidelines

- Use descriptive commit messages
- Keep PRs focused and small
- Include tests for new features
- Update documentation as needed
- Follow the existing code style

## 🏷️ Commit Convention

We use conventional commits:

- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test changes
- `chore:` Build/tooling changes

Example:
```
feat(core): add streaming SSR support
fix(cli): resolve config loading issue
docs(readme): update installation instructions
```

## 🎯 Areas for Contribution

We welcome contributions in these areas:

- **Core Framework**: Runtime improvements, performance optimizations
- **Plugins**: Built-in and community plugins
- **UI Components**: Reusable components for the UI system
- **Documentation**: Guides, examples, API docs
- **Testing**: Unit tests, integration tests, E2E tests
- **Tooling**: CLI improvements, dev tools
- **Examples**: Sample applications and use cases

## 📞 Getting Help

- Join our [Discord](https://discord.gg/kilat-js)
- Check [GitHub Discussions](https://github.com/kangpcode/kilat.js/discussions)
- Read the [documentation](https://kilat-js.pcode.my.id)

## 📄 License

By contributing to Kilat.js, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to Kilat.js! 🚀
