/**
 * 🔄 OTA Update Manager - Over-The-Air Updates
 */

import type { KilatContext } from '../../../types/config'

export interface UpdateInfo {
  version: string
  releaseDate: string
  changelog: string[]
  downloadUrl: string
  checksum: string
  size: number
  critical: boolean
}

export interface UpdateConfig {
  enabled: boolean
  checkInterval: number
  autoUpdate: boolean
  allowPrerelease: boolean
  updateChannel: 'stable' | 'beta' | 'alpha'
  registryUrl: string
}

class UpdateManager {
  private config: UpdateConfig
  private currentVersion: string
  private checkTimer?: any
  private isUpdating = false
  
  constructor(config: UpdateConfig, currentVersion: string) {
    this.config = config
    this.currentVersion = currentVersion
    this.setupUpdateTimer()
  }
  
  /**
   * Check for updates
   */
  async checkForUpdates(): Promise<UpdateInfo | null> {
    if (!this.config.enabled) return null
    
    try {
      const response = await fetch(`${this.config.registryUrl}/latest`, {
        headers: {
          'User-Agent': `Kilat.js/${this.currentVersion}`,
          'X-Current-Version': this.currentVersion,
          'X-Update-Channel': this.config.updateChannel
        }
      })
      
      if (!response.ok) {
        throw new Error(`Update check failed: ${response.status}`)
      }
      
      const updateInfo: UpdateInfo = await response.json()
      
      // Check if update is available
      if (this.isNewerVersion(updateInfo.version, this.currentVersion)) {
        console.log(`🔄 Update available: ${this.currentVersion} → ${updateInfo.version}`)
        return updateInfo
      }
      
      return null
      
    } catch (error) {
      console.error('❌ Update check failed:', error)
      return null
    }
  }
  
  /**
   * Download and install update
   */
  async installUpdate(updateInfo: UpdateInfo): Promise<boolean> {
    if (this.isUpdating) {
      console.warn('⚠️ Update already in progress')
      return false
    }
    
    this.isUpdating = true
    
    try {
      console.log(`📥 Downloading update: ${updateInfo.version}`)
      
      // Download update
      const updateData = await this.downloadUpdate(updateInfo)
      
      // Verify checksum
      if (!await this.verifyChecksum(updateData, updateInfo.checksum)) {
        throw new Error('Update checksum verification failed')
      }
      
      console.log('✅ Update downloaded and verified')
      
      // Backup current installation
      await this.createBackup()
      
      // Install update
      await this.applyUpdate(updateData)
      
      console.log(`✅ Update installed: ${updateInfo.version}`)
      
      // Update current version
      this.currentVersion = updateInfo.version
      
      return true
      
    } catch (error) {
      console.error('❌ Update installation failed:', error)
      
      // Restore backup if available
      await this.restoreBackup()
      
      return false
    } finally {
      this.isUpdating = false
    }
  }
  
  /**
   * Download update data
   */
  private async downloadUpdate(updateInfo: UpdateInfo): Promise<ArrayBuffer> {
    const response = await fetch(updateInfo.downloadUrl, {
      headers: {
        'User-Agent': `Kilat.js/${this.currentVersion}`
      }
    })
    
    if (!response.ok) {
      throw new Error(`Download failed: ${response.status}`)
    }
    
    return await response.arrayBuffer()
  }
  
  /**
   * Verify update checksum
   */
  private async verifyChecksum(data: ArrayBuffer, expectedChecksum: string): Promise<boolean> {
    try {
      // Use Web Crypto API if available
      if (typeof crypto !== 'undefined' && crypto.subtle) {
        const hashBuffer = await crypto.subtle.digest('SHA-256', data)
        const hashArray = Array.from(new Uint8Array(hashBuffer))
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
        return hashHex === expectedChecksum
      }
      
      // Fallback: skip verification
      console.warn('⚠️ Checksum verification skipped (crypto not available)')
      return true
      
    } catch (error) {
      console.error('❌ Checksum verification error:', error)
      return false
    }
  }
  
  /**
   * Create backup of current installation
   */
  private async createBackup(): Promise<void> {
    console.log('💾 Creating backup...')
    // Implementation would backup current files
    // For now, just log
  }
  
  /**
   * Apply update
   */
  private async applyUpdate(updateData: ArrayBuffer): Promise<void> {
    console.log('🔧 Applying update...')
    // Implementation would extract and apply update
    // For now, just simulate
    await new Promise(resolve => setTimeout(resolve, 2000))
  }
  
  /**
   * Restore backup
   */
  private async restoreBackup(): Promise<void> {
    console.log('🔄 Restoring backup...')
    // Implementation would restore backup
    // For now, just log
  }
  
  /**
   * Check if version is newer
   */
  private isNewerVersion(newVersion: string, currentVersion: string): boolean {
    const parseVersion = (version: string) => {
      return version.split('.').map(Number)
    }
    
    const newParts = parseVersion(newVersion)
    const currentParts = parseVersion(currentVersion)
    
    for (let i = 0; i < Math.max(newParts.length, currentParts.length); i++) {
      const newPart = newParts[i] || 0
      const currentPart = currentParts[i] || 0
      
      if (newPart > currentPart) return true
      if (newPart < currentPart) return false
    }
    
    return false
  }
  
  /**
   * Setup automatic update checking
   */
  private setupUpdateTimer(): void {
    if (!this.config.enabled || this.config.checkInterval <= 0) return
    
    this.checkTimer = setInterval(async () => {
      const updateInfo = await this.checkForUpdates()
      
      if (updateInfo && this.config.autoUpdate) {
        if (updateInfo.critical || this.config.updateChannel !== 'stable') {
          console.log('🚀 Auto-installing update...')
          await this.installUpdate(updateInfo)
        }
      }
    }, this.config.checkInterval * 1000)
  }
  
  /**
   * Get current version
   */
  getCurrentVersion(): string {
    return this.currentVersion
  }
  
  /**
   * Get update status
   */
  getStatus() {
    return {
      currentVersion: this.currentVersion,
      isUpdating: this.isUpdating,
      config: this.config
    }
  }
  
  /**
   * Cleanup and stop timers
   */
  destroy(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
    }
  }
}

let updateManager: UpdateManager

/**
 * Initialize update manager
 */
export async function initializeUpdater(context: KilatContext): Promise<void> {
  const config: UpdateConfig = {
    enabled: context.mode === 'production',
    checkInterval: 3600, // 1 hour
    autoUpdate: false,
    allowPrerelease: false,
    updateChannel: 'stable',
    registryUrl: 'https://registry.kilat-js.pcode.my.id'
  }
  
  // Get current version from package.json
  let currentVersion = '1.0.0'
  try {
    const pkg = require('../../../package.json')
    currentVersion = pkg.version
  } catch {
    // Use default version
  }
  
  updateManager = new UpdateManager(config, currentVersion)
  console.log('🔄 Update manager initialized')
}

/**
 * Get update manager instance
 */
export function getUpdateManager(): UpdateManager | undefined {
  return updateManager
}
