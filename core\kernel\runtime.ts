/**
 * ⚡ SpeedRun™ Runtime - Core Kilat.js Runtime Engine
 * Built on Bun.js with Node.js fallback
 */

import type { KilatConfig, KilatContext } from '../../types/config'
import { createServer } from './server'
import { loadAppsMapping } from './loader'
import { initializePlugins } from '../systems/plugin/manager'
import { setupErrorHandling } from '../systems/error/handler'
import { initializeCache } from '../systems/cache/manager'

export class SpeedRunRuntime {
  private config: KilatConfig
  private context: KilatContext
  private server: any
  private isRunning = false

  constructor(config: KilatConfig) {
    this.config = config
    this.context = {
      config,
      routes: [],
      plugins: [],
      mode: process.env.NODE_ENV === 'production' ? 'production' : 'development'
    }
  }

  /**
   * Initialize the runtime
   */
  async initialize(): Promise<void> {
    console.log('⚡ Initializing SpeedRun™ Runtime...')

    try {
      // Setup error handling first
      setupErrorHandling(this.context)

      // Initialize cache system
      await initializeCache(this.context)

      // Load apps mapping (file-based routes)
      this.context.routes = await loadAppsMapping(this.config.apps)

      // Initialize plugins
      this.context.plugins = await initializePlugins(this.config.plugins, this.context)

      // Create HTTP server
      this.server = await createServer(this.context)

      console.log('✅ SpeedRun™ Runtime initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize SpeedRun™ Runtime:', error)
      throw error
    }
  }

  /**
   * Start the runtime server
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.warn('⚠️ SpeedRun™ Runtime is already running')
      return
    }

    try {
      const { port, host } = this.config.runtime

      // Start server based on runtime engine
      if (this.config.runtime.engine === 'bun' && typeof Bun !== 'undefined') {
        console.log('🚀 Starting with Bun.js runtime...')
        this.server = Bun.serve({
          port,
          hostname: host,
          fetch: this.server.fetch,
          error: this.server.error,
        })
      } else {
        console.log('🚀 Starting with Node.js fallback...')
        await this.server.listen(port, host)
      }

      this.isRunning = true
      
      console.log(`✅ SpeedRun™ Runtime started successfully`)
      console.log(`🌐 Server running at http://${host}:${port}`)
      
      if (this.context.mode === 'development') {
        console.log('🛠️ Development mode enabled')
        console.log('📊 Analytics and HMR active')
      }

    } catch (error) {
      console.error('❌ Failed to start SpeedRun™ Runtime:', error)
      throw error
    }
  }

  /**
   * Stop the runtime server
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    try {
      if (this.server?.stop) {
        await this.server.stop()
      }
      
      this.isRunning = false
      console.log('🛑 SpeedRun™ Runtime stopped')
    } catch (error) {
      console.error('❌ Failed to stop SpeedRun™ Runtime:', error)
      throw error
    }
  }

  /**
   * Reload the runtime (for HMR)
   */
  async reload(): Promise<void> {
    console.log('🔄 Reloading SpeedRun™ Runtime...')
    
    try {
      // Reload apps mapping
      this.context.routes = await loadAppsMapping(this.config.apps)
      
      // Trigger plugin reload hooks
      for (const plugin of this.context.plugins) {
        if (plugin.hooks?.['dev:reload']) {
          await plugin.hooks['dev:reload']()
        }
      }
      
      console.log('✅ SpeedRun™ Runtime reloaded successfully')
    } catch (error) {
      console.error('❌ Failed to reload SpeedRun™ Runtime:', error)
      throw error
    }
  }

  /**
   * Get runtime status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      engine: this.config.runtime.engine,
      mode: this.context.mode,
      routes: this.context.routes.length,
      plugins: this.context.plugins.length,
      port: this.config.runtime.port,
      host: this.config.runtime.host,
    }
  }
}

/**
 * Create and initialize SpeedRun™ Runtime
 */
export async function createSpeedRunRuntime(config: KilatConfig): Promise<SpeedRunRuntime> {
  const runtime = new SpeedRunRuntime(config)
  await runtime.initialize()
  return runtime
}
