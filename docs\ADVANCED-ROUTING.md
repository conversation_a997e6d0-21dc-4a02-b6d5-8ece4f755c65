# 🧭 Kilat.js Advanced Routing & Rendering System

**Complete Next.js App Router-like system with SSG, SSR, CSR support**

## ✅ Advanced Features

### 🧭 File-based Routing

**Automatic Route Generation:**
- ✅ **Static Routes** - `/about` → `apps/about/page.tsx`
- ✅ **Dynamic Routes** - `/blog/[slug]` → `apps/blog/[slug]/page.tsx`
- ✅ **Catch-all Routes** - `/docs/[...slug]` → `apps/docs/[...slug]/page.tsx`
- ✅ **Optional Catch-all** - `/shop/[[...slug]]` → `apps/shop/[[...slug]]/page.tsx`
- ✅ **API Routes** - `/api/users` → `apps/api/users/route.ts`

**Special Files:**
```
apps/
├── page.tsx              # Page component
├── layout.tsx            # Layout wrapper
├── loading.tsx           # Loading UI
├── error.tsx             # Error boundary
├── not-found.tsx         # 404 page
├── middleware.ts         # Route middleware
└── generate.ts           # SSG configuration
```

### ⚡ Rendering Modes

**Static Site Generation (SSG):**
- ✅ **Build-time Generation** - Pre-render at build time
- ✅ **Dynamic SSG** - Generate dynamic routes with `generateStaticParams`
- ✅ **Incremental Static Regeneration (ISR)** - Revalidate on demand
- ✅ **Fallback Support** - Handle missing pages gracefully

**Server-Side Rendering (SSR):**
- ✅ **Request-time Rendering** - Render on each request
- ✅ **Dynamic Data** - Fetch fresh data on every request
- ✅ **SEO Optimized** - Full HTML with metadata
- ✅ **Streaming Support** - Progressive page loading

**Client-Side Rendering (CSR):**
- ✅ **SPA Mode** - Single Page Application behavior
- ✅ **Hydration** - Client-side React hydration
- ✅ **Code Splitting** - Automatic route-based splitting
- ✅ **Lazy Loading** - Load components on demand

### 🏗️ Build System

**Auto-build Features:**
- ✅ **Static Generation** - `bun run build` generates all static pages
- ✅ **Server Bundle** - Production-ready server code
- ✅ **Client Bundle** - Optimized client-side code
- ✅ **Asset Optimization** - CSS, JS, and image optimization
- ✅ **Manifest Generation** - Build metadata and sitemap

## 📁 File Structure

```
apps/
├── layout.tsx                    # Root layout
├── page.tsx                      # Homepage (SSR)
├── about/
│   └── page.tsx                  # About page (SSR)
├── blog/
│   ├── page.tsx                  # Blog index (SSG)
│   └── [slug]/
│       ├── page.tsx              # Blog post (SSG)
│       └── generate.ts           # Static params
├── dashboard/
│   ├── layout.tsx                # Dashboard layout
│   ├── middleware.ts             # Auth middleware
│   └── page.tsx                  # Dashboard (SSR)
├── login/
│   └── page.tsx                  # Login page (CSR)
└── api/
    ├── users/
    │   └── route.ts              # Users API
    └── auth/
        ├── login/route.ts        # Login API
        └── register/route.ts     # Register API

core/
├── router/
│   └── index.ts                  # Advanced router
├── renderer/
│   └── index.ts                  # Rendering engine
├── generator/
│   └── index.ts                  # Static generator
├── build/
│   └── index.ts                  # Build system
└── server/
    └── index.ts                  # Advanced server
```

## 🚀 Usage Examples

### Dynamic Routes with SSG

```tsx
// apps/blog/[slug]/page.tsx
export default function BlogPost({ params }: PageProps) {
  const { slug } = params || {}
  return <div>Blog post: {slug}</div>
}

// Generate metadata
export async function generateMetadata({ params }: PageProps) {
  return {
    title: `Blog Post: ${params?.slug}`,
    description: 'Amazing blog post content'
  }
}
```

```ts
// apps/blog/[slug]/generate.ts
export async function generateStaticParams() {
  return [
    { params: { slug: 'getting-started' } },
    { params: { slug: 'advanced-routing' } },
    { params: { slug: 'performance-tips' } }
  ]
}

export const config = {
  fallback: false,
  revalidate: 3600
}
```

### Middleware

```ts
// apps/dashboard/middleware.ts
export default async function middleware(context: KilatContext) {
  // Check authentication
  if (!context.user) {
    throw new Response(null, {
      status: 302,
      headers: { 'Location': '/login' }
    })
  }
}

export const config = {
  matcher: ['/dashboard/:path*']
}
```

### API Routes

```ts
// apps/api/users/route.ts
export async function GET(request: Request) {
  const users = await fetchUsers()
  return Response.json(users)
}

export async function POST(request: Request) {
  const body = await request.json()
  const user = await createUser(body)
  return Response.json(user, { status: 201 })
}
```

### Layouts

```tsx
// apps/dashboard/layout.tsx
export default function DashboardLayout({ children }: LayoutProps) {
  return (
    <div className="dashboard-layout">
      <Sidebar />
      <main>{children}</main>
    </div>
  )
}
```

## 🛠️ Build Commands

```bash
# Development server
bun run dev

# Production build
bun run build

# Preview production build
bun run preview

# Clean build directory
bun run build:clean

# Generate static pages only
bun run generate
```

## 🎯 Key Benefits

**Next.js App Router Compatibility:**
- 🧭 **File-based Routing** - Familiar folder structure
- ⚡ **Multiple Render Modes** - SSG, SSR, CSR in one framework
- 🏗️ **Automatic Code Splitting** - Optimal bundle sizes
- 🛡️ **Middleware Support** - Route-level middleware
- 📊 **Built-in Analytics** - Performance monitoring

**Kilat.js Advantages:**
- 🚀 **SpeedRun™ Runtime** - Bun.js for maximum performance
- 🎨 **Native KilatCSS** - No external dependencies
- 🎭 **KilatAnim Integration** - Smooth animations
- 🔧 **Zero Configuration** - Works out of the box
- 📱 **Mobile-first** - Responsive by default

**Developer Experience:**
- 🔥 **Hot Reload** - Instant feedback during development
- 🧪 **TypeScript First** - Full type safety
- 📋 **Auto-completion** - IntelliSense support
- 🐛 **Error Boundaries** - Graceful error handling
- 📊 **Build Analytics** - Performance insights

## 🌟 Production Features

**Performance Optimizations:**
- ⚡ **Static Generation** - Pre-rendered pages for speed
- 🗜️ **Compression** - Gzip/Brotli compression
- 📦 **Bundle Optimization** - Tree shaking and minification
- 🖼️ **Image Optimization** - Automatic image processing
- 🔄 **Caching** - Intelligent caching strategies

**SEO & Accessibility:**
- 🔍 **SEO Optimized** - Meta tags and structured data
- 🗺️ **Sitemap Generation** - Automatic sitemap.xml
- 🤖 **Robots.txt** - Search engine directives
- ♿ **Accessibility** - WCAG compliance
- 📱 **Mobile Optimization** - Core Web Vitals

**Deployment Ready:**
- 🐳 **Docker Support** - Containerized deployment
- ☁️ **Cloud Ready** - Deploy to any platform
- 🔒 **Security** - Built-in security best practices
- 📊 **Monitoring** - Health checks and metrics
- 🔄 **CI/CD** - Automated build and deploy

## 🎉 Final Result

**Kilat.js now features a complete Next.js App Router-like system:**
- 🧭 **Advanced File-based Routing** - Dynamic, catch-all, optional routes
- ⚡ **Multiple Rendering Modes** - SSG, SSR, CSR with automatic optimization
- 🏗️ **Production Build System** - Auto-build with optimization
- 🛡️ **Middleware Support** - Route-level authentication and logic
- 🎨 **Integrated UI System** - KilatCSS and KilatAnim
- 📱 **Mobile-first Design** - Responsive and accessible
- 🚀 **SpeedRun™ Performance** - Bun.js runtime for maximum speed

**Framework is now production-ready with enterprise-grade routing and rendering!** 🚀
