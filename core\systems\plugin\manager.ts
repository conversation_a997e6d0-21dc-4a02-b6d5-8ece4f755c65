/**
 * 🔌 Plugin Manager - Advanced Plugin System
 */

import type { KilatPlugin, KilatContext } from '../../../types/config'
import { loadModule } from '../../kernel/esm-loader'
import { join } from 'path'

export interface PluginRegistry {
  [name: string]: KilatPlugin
}

export interface PluginHookContext {
  context: KilatContext
  plugins: PluginRegistry
  data?: any
}

class PluginManager {
  private plugins: PluginRegistry = {}
  private hooks: Map<string, Array<(ctx: PluginHookContext) => Promise<any>>> = new Map()
  private context: KilatContext

  constructor(context: KilatContext) {
    this.context = context
  }

  /**
   * Initialize plugins
   */
  async initializePlugins(pluginConfigs: KilatPlugin[]): Promise<KilatPlugin[]> {
    console.log('🔌 Initializing plugins...')

    const loadedPlugins: KilatPlugin[] = []

    for (const config of pluginConfigs) {
      try {
        const plugin = await this.loadPlugin(config)
        this.plugins[plugin.name] = plugin
        loadedPlugins.push(plugin)

        // Register plugin hooks
        this.registerPluginHooks(plugin)

        console.log(`✅ Plugin loaded: ${plugin.name} v${plugin.version || '1.0.0'}`)
      } catch (error) {
        console.error(`❌ Failed to load plugin ${config.name}:`, error)
      }
    }

    // Execute initialization hooks
    await this.executeHook('plugin:init', { plugins: loadedPlugins })

    console.log(`✅ Initialized ${loadedPlugins.length} plugins`)
    return loadedPlugins
  }

  /**
   * Load individual plugin
   */
  private async loadPlugin(config: KilatPlugin): Promise<KilatPlugin> {
    // If config has a module path, load it
    if (config.module) {
      const pluginModule = await loadModule(config.module)
      const plugin = pluginModule.default || pluginModule

      // Merge with config
      return {
        ...plugin,
        ...config,
        config: { ...plugin.config, ...config.config }
      }
    }

    // If it's a built-in plugin, load from plugins directory
    if (config.builtin) {
      const pluginPath = join(process.cwd(), 'core', 'systems', 'plugin', 'builtin', `${config.name}.ts`)
      const pluginModule = await loadModule(pluginPath)
      const plugin = pluginModule.default || pluginModule

      return {
        ...plugin,
        ...config,
        config: { ...plugin.config, ...config.config }
      }
    }

    // Return config as-is for simple plugins
    return config
  }

  /**
   * Register plugin hooks
   */
  private registerPluginHooks(plugin: KilatPlugin): void {
    if (!plugin.hooks) return

    for (const [hookName, hookFn] of Object.entries(plugin.hooks)) {
      if (!this.hooks.has(hookName)) {
        this.hooks.set(hookName, [])
      }

      this.hooks.get(hookName)!.push(async (ctx) => {
        return await hookFn(ctx.context, ctx.data)
      })
    }
  }

  /**
   * Execute plugin hooks
   */
  async executeHook(hookName: string, data?: any): Promise<any[]> {
    const hookFunctions = this.hooks.get(hookName) || []
    const results: any[] = []

    const hookContext: PluginHookContext = {
      context: this.context,
      plugins: this.plugins,
      data
    }

    for (const hookFn of hookFunctions) {
      try {
        const result = await hookFn(hookContext)
        results.push(result)
      } catch (error) {
        console.error(`❌ Plugin hook error (${hookName}):`, error)
      }
    }

    return results
  }

  /**
   * Get plugin by name
   */
  getPlugin(name: string): KilatPlugin | undefined {
    return this.plugins[name]
  }

  /**
   * Get all plugins
   */
  getAllPlugins(): PluginRegistry {
    return { ...this.plugins }
  }

  /**
   * Check if plugin is loaded
   */
  hasPlugin(name: string): boolean {
    return name in this.plugins
  }

  /**
   * Unload plugin
   */
  async unloadPlugin(name: string): Promise<void> {
    const plugin = this.plugins[name]
    if (!plugin) return

    // Execute cleanup hooks
    if (plugin.hooks?.['plugin:cleanup']) {
      await plugin.hooks['plugin:cleanup'](this.context)
    }

    // Remove from registry
    delete this.plugins[name]

    // Remove hooks
    for (const [hookName, hookFunctions] of this.hooks.entries()) {
      this.hooks.set(hookName, hookFunctions.filter(fn =>
        !fn.toString().includes(plugin.name)
      ))
    }

    console.log(`🔌 Plugin unloaded: ${name}`)
  }
}

let pluginManager: PluginManager

/**
 * Initialize plugins
 */
export async function initializePlugins(
  pluginConfigs: KilatPlugin[],
  context: KilatContext
): Promise<KilatPlugin[]> {
  pluginManager = new PluginManager(context)
  return await pluginManager.initializePlugins(pluginConfigs)
}

/**
 * Execute plugin hooks
 */
export async function executeHook(
  hookName: string,
  plugins: KilatPlugin[],
  ...args: any[]
): Promise<void> {
  if (pluginManager) {
    await pluginManager.executeHook(hookName, args[0])
  }
}

/**
 * Get plugin manager instance
 */
export function getPluginManager(): PluginManager | undefined {
  return pluginManager
}
