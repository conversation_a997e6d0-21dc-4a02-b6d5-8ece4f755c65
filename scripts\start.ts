#!/usr/bin/env bun
/**
 * 🚀 Kilat.js Startup Script
 * Quick start script for development
 */

import { createKilatApp } from '../index'

async function main() {
  console.log('⚡ Starting Kilat.js application...')
  
  try {
    // Create and start the app
    const app = await createKilatApp()
    await app.start()
    
    const status = app.getStatus()
    
    console.log('✅ Kilat.js application started successfully!')
    console.log(`🌐 Server: http://${status.host}:${status.port}`)
    console.log(`⚡ Engine: ${status.engine}`)
    console.log(`📊 Mode: ${app.config.mode || 'development'}`)
    console.log('')
    console.log('Available routes:')
    console.log('  🏠 /          - Homepage')
    console.log('  📄 /about     - About page')
    console.log('  📊 /dashboard - Dashboard')
    console.log('  🛠️ /api/users - Users API')
    console.log('')
    console.log('Press Ctrl+C to stop the server')
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down Kilat.js application...')
      await app.stop()
      process.exit(0)
    })
    
    process.on('SIGTERM', async () => {
      console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
      await app.stop()
      process.exit(0)
    })
    
  } catch (error) {
    console.error('❌ Failed to start Kilat.js application:', error)
    process.exit(1)
  }
}

// Run the application
main().catch(error => {
  console.error('❌ Unexpected error:', error)
  process.exit(1)
})
