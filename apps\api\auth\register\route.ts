/**
 * ✨ Register API Endpoint
 */

export async function POST(request: Request): Promise<Response> {
  try {
    const body = await request.json()
    const { name, email, password, confirmPassword, terms } = body

    // Validate input
    if (!name || !email || !password || !confirmPassword) {
      return new Response(JSON.stringify({
        success: false,
        error: 'All fields are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Validate password match
    if (password !== confirmPassword) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Passwords do not match'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Validate password strength
    if (password.length < 8) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Password must be at least 8 characters long'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Validate terms acceptance
    if (!terms) {
      return new Response(JSON.stringify({
        success: false,
        error: 'You must accept the terms and conditions'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Please enter a valid email address'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Check if user already exists (in real app, check database)
    if (email === '<EMAIL>') {
      return new Response(JSON.stringify({
        success: false,
        error: 'User with this email already exists'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Create user (in real app, save to database with hashed password)
    const userId = 'user-' + Date.now()
    const sessionToken = 'session-' + Date.now()
    
    const user = {
      id: userId,
      name,
      email,
      avatar: name.split(' ').map(n => n[0]).join('').toUpperCase()
    }

    // Set session cookie
    const headers = new Headers({
      'Content-Type': 'application/json',
      'Set-Cookie': `kilat-session=${sessionToken}; HttpOnly; Path=/; SameSite=Strict`
    })

    return new Response(JSON.stringify({
      success: true,
      message: 'Registration successful',
      user,
      redirectTo: '/dashboard'
    }), {
      status: 201,
      headers
    })

  } catch (error) {
    console.error('Registration error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

// Handle form submission
export async function handleFormSubmission(formData: FormData): Promise<Response> {
  const name = formData.get('name') as string
  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string
  const terms = formData.get('terms') === 'on'

  return POST(new Request('http://localhost/api/auth/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name, email, password, confirmPassword, terms })
  }))
}
