/**
 * ⚡ Kilat.js - Modern Fullstack Framework
 * Entry point for the framework
 */

// Core exports
export { createSpeedRunRuntime } from './core/kernel/runtime'
export { loadAppsMapping } from './core/kernel/loader'
export { matchRoute, generateUrl } from './core/kernel/router'
export { ApiResponse } from './core/kernel/api-handler'

// Streaming & RSC
export { createStreamingResponse, createStaticResponse } from './core/kernel/stream'
export { renderServerComponent, isServerComponent } from './core/kernel/rsc'
export { isEdgeRuntime, createEdgeHandler } from './core/kernel/edge'

// UI Systems
export { ScrollAnimation, useScrollTrigger, StaggerAnimation, ParallaxScroll } from './core/systems/anim/scroll'
export { getAllAnimationPresets, getAnimationPreset } from './core/systems/anim/preset'
export { Button } from './components/ui/Button'

// Plugin System
export { initializePlugins, executeHook, getPluginManager } from './core/systems/plugin/manager'

// Cache & ISR
export { initializeCache, getCache } from './core/systems/cache/manager'

// Hydration
export { initializeHydration, getHydrationManager, registerForHydration } from './core/systems/hydrate/manager'

// Updates
export { initializeUpdater, getUpdateManager } from './core/systems/updater/manager'

// Utilities
export { cn, formatDate, formatNumber, debounce, throttle } from './core/shared/utils'

// Types
export type { 
  KilatConfig, 
  KilatPlugin, 
  KilatContext, 
  AppRoute 
} from './types/config'

// Built-in plugins
export { default as analyticsPlugin } from './core/systems/plugin/builtin/analytics'
export { default as hmrPlugin } from './core/systems/plugin/builtin/hmr'
export { default as compressionPlugin } from './core/systems/plugin/builtin/compression'
export { default as seoPlugin } from './core/systems/plugin/builtin/seo'

/**
 * Create Kilat.js application
 */
export async function createKilatApp(config?: any) {
  const { loadConfig } = await import('./core/tools/config-loader')
  const { createSpeedRunRuntime } = await import('./core/kernel/runtime')

  const fullConfig = config ? { ...await loadConfig(), ...config } : await loadConfig()

  const runtime = await createSpeedRunRuntime(fullConfig)
  
  return {
    runtime,
    config: fullConfig,
    
    // Start the application
    async start() {
      await runtime.start()
      return this
    },
    
    // Stop the application
    async stop() {
      await runtime.stop()
      return this
    },
    
    // Get runtime status
    getStatus() {
      return runtime.getStatus()
    }
  }
}

/**
 * Default export for convenience
 */
export default {
  createApp: createKilatApp
}
