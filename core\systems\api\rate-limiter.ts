/**
 * 🚦 Rate Limiter - API Rate Limiting System
 */

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (request: Request) => string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  message?: string
  headers?: boolean
}

export interface RateLimitInfo {
  totalHits: number
  totalHitsPerWindow: number
  resetTime: Date
  remaining: number
}

export interface RateLimitStore {
  get(key: string): Promise<RateLimitInfo | null>
  set(key: string, info: RateLimitInfo): Promise<void>
  increment(key: string): Promise<RateLimitInfo>
  reset(key: string): Promise<void>
}

class MemoryStore implements RateLimitStore {
  private store = new Map<string, { info: RateLimitInfo, timer: any }>()
  
  async get(key: string): Promise<RateLimitInfo | null> {
    const entry = this.store.get(key)
    return entry ? entry.info : null
  }
  
  async set(key: string, info: RateLimitInfo): Promise<void> {
    // Clear existing timer
    const existing = this.store.get(key)
    if (existing?.timer) {
      clearTimeout(existing.timer)
    }
    
    // Set new entry with auto-cleanup timer
    const timer = setTimeout(() => {
      this.store.delete(key)
    }, info.resetTime.getTime() - Date.now())
    
    this.store.set(key, { info, timer })
  }
  
  async increment(key: string): Promise<RateLimitInfo> {
    const existing = await this.get(key)
    
    if (existing) {
      // Check if window has expired
      if (Date.now() >= existing.resetTime.getTime()) {
        // Reset window
        const newInfo: RateLimitInfo = {
          totalHits: 1,
          totalHitsPerWindow: 1,
          resetTime: new Date(Date.now() + (existing.resetTime.getTime() - (existing.resetTime.getTime() - 60000))), // Maintain window size
          remaining: Math.max(0, existing.totalHitsPerWindow - 1)
        }
        
        await this.set(key, newInfo)
        return newInfo
      } else {
        // Increment within window
        const newInfo: RateLimitInfo = {
          ...existing,
          totalHits: existing.totalHits + 1,
          remaining: Math.max(0, existing.remaining - 1)
        }
        
        await this.set(key, newInfo)
        return newInfo
      }
    } else {
      // First request
      const windowMs = 60000 // Default 1 minute window
      const maxRequests = 100 // Default limit
      
      const newInfo: RateLimitInfo = {
        totalHits: 1,
        totalHitsPerWindow: maxRequests,
        resetTime: new Date(Date.now() + windowMs),
        remaining: maxRequests - 1
      }
      
      await this.set(key, newInfo)
      return newInfo
    }
  }
  
  async reset(key: string): Promise<void> {
    const entry = this.store.get(key)
    if (entry?.timer) {
      clearTimeout(entry.timer)
    }
    this.store.delete(key)
  }
}

class RateLimiter {
  private store: RateLimitStore
  private config: RateLimitConfig
  
  constructor(config: RateLimitConfig, store?: RateLimitStore) {
    this.config = {
      keyGenerator: this.defaultKeyGenerator,
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      message: 'Too many requests, please try again later.',
      headers: true,
      ...config
    }
    this.store = store || new MemoryStore()
  }
  
  /**
   * Check if request should be rate limited
   */
  async checkLimit(request: Request): Promise<{ allowed: boolean, info: RateLimitInfo, response?: Response }> {
    const key = this.config.keyGenerator!(request)
    
    // Get current rate limit info
    let info = await this.store.get(key)
    
    if (!info) {
      // First request for this key
      info = {
        totalHits: 0,
        totalHitsPerWindow: this.config.maxRequests,
        resetTime: new Date(Date.now() + this.config.windowMs),
        remaining: this.config.maxRequests
      }
    }
    
    // Check if window has expired
    if (Date.now() >= info.resetTime.getTime()) {
      // Reset window
      info = {
        totalHits: 0,
        totalHitsPerWindow: this.config.maxRequests,
        resetTime: new Date(Date.now() + this.config.windowMs),
        remaining: this.config.maxRequests
      }
    }
    
    // Check if limit exceeded
    if (info.totalHits >= this.config.maxRequests) {
      const response = this.createLimitExceededResponse(info)
      return { allowed: false, info, response }
    }
    
    // Increment counter
    info.totalHits++
    info.remaining = Math.max(0, this.config.maxRequests - info.totalHits)
    
    // Save updated info
    await this.store.set(key, info)
    
    return { allowed: true, info }
  }
  
  /**
   * Create middleware function
   */
  middleware() {
    return async (request: Request): Promise<Response | null> => {
      const result = await this.checkLimit(request)
      
      if (!result.allowed) {
        return result.response!
      }
      
      // Add rate limit headers to request for later use
      if (this.config.headers) {
        ;(request as any).rateLimitInfo = result.info
      }
      
      return null // Continue to next middleware/handler
    }
  }
  
  /**
   * Default key generator (IP-based)
   */
  private defaultKeyGenerator(request: Request): string {
    // Try to get real IP from headers
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const cfConnectingIp = request.headers.get('cf-connecting-ip')
    
    const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown'
    
    return `rate_limit:${ip}`
  }
  
  /**
   * Create rate limit exceeded response
   */
  private createLimitExceededResponse(info: RateLimitInfo): Response {
    const headers = new Headers({
      'Content-Type': 'application/json',
      'Retry-After': Math.ceil((info.resetTime.getTime() - Date.now()) / 1000).toString()
    })
    
    if (this.config.headers) {
      headers.set('X-RateLimit-Limit', info.totalHitsPerWindow.toString())
      headers.set('X-RateLimit-Remaining', info.remaining.toString())
      headers.set('X-RateLimit-Reset', Math.ceil(info.resetTime.getTime() / 1000).toString())
    }
    
    return new Response(JSON.stringify({
      error: 'Rate limit exceeded',
      message: this.config.message,
      retryAfter: Math.ceil((info.resetTime.getTime() - Date.now()) / 1000)
    }), {
      status: 429,
      headers
    })
  }
}

/**
 * Create rate limiter middleware
 */
export function createRateLimiter(config: RateLimitConfig, store?: RateLimitStore) {
  const limiter = new RateLimiter(config, store)
  return limiter.middleware()
}

/**
 * Common rate limit presets
 */
export const RateLimitPresets = {
  // Very strict - for sensitive operations
  strict: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    message: 'Too many requests for this operation. Please try again in 15 minutes.'
  },
  
  // Standard API rate limit
  standard: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    message: 'Too many requests. Please try again later.'
  },
  
  // Generous - for public APIs
  generous: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000,
    message: 'Rate limit exceeded. Please try again later.'
  },
  
  // Per second limit - for real-time operations
  perSecond: {
    windowMs: 1000, // 1 second
    maxRequests: 10,
    message: 'Too many requests per second.'
  }
}

export { RateLimiter, MemoryStore }
