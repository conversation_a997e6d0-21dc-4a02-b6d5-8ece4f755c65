/**
 * 🗜️ Compression Plugin - Response Compression
 */

import type { KilatPlugin, KilatContext } from '../../../../types/config'

export interface CompressionOptions {
  enabled: boolean
  algorithms: ('gzip' | 'deflate' | 'br')[]
  threshold: number
  level: number
  mimeTypes: string[]
}

class CompressionHandler {
  private options: CompressionOptions
  
  constructor(options: CompressionOptions) {
    this.options = options
  }
  
  async compressResponse(response: Response, acceptEncoding: string): Promise<Response> {
    if (!this.options.enabled) {
      return response
    }
    
    // Check if response should be compressed
    if (!this.shouldCompress(response)) {
      return response
    }
    
    // Get preferred encoding
    const encoding = this.getPreferredEncoding(acceptEncoding)
    if (!encoding) {
      return response
    }
    
    try {
      const body = await response.arrayBuffer()
      
      // Check size threshold
      if (body.byteLength < this.options.threshold) {
        return response
      }
      
      // Compress the body
      const compressedBody = await this.compress(body, encoding)
      
      // Create new response with compressed body
      const headers = new Headers(response.headers)
      headers.set('Content-Encoding', encoding)
      headers.set('Content-Length', compressedBody.byteLength.toString())
      headers.set('Vary', 'Accept-Encoding')
      
      return new Response(compressedBody, {
        status: response.status,
        statusText: response.statusText,
        headers
      })
      
    } catch (error) {
      console.error('❌ Compression error:', error)
      return response
    }
  }
  
  private shouldCompress(response: Response): boolean {
    // Don't compress if already compressed
    if (response.headers.get('Content-Encoding')) {
      return false
    }
    
    // Check MIME type
    const contentType = response.headers.get('Content-Type') || ''
    return this.options.mimeTypes.some(type => contentType.includes(type))
  }
  
  private getPreferredEncoding(acceptEncoding: string): string | null {
    if (!acceptEncoding) return null
    
    const encodings = acceptEncoding.toLowerCase().split(',').map(e => e.trim())
    
    for (const algorithm of this.options.algorithms) {
      if (encodings.some(e => e.includes(algorithm))) {
        return algorithm
      }
    }
    
    return null
  }
  
  private async compress(data: ArrayBuffer, encoding: string): Promise<ArrayBuffer> {
    const uint8Array = new Uint8Array(data)
    
    switch (encoding) {
      case 'gzip':
        return await this.gzipCompress(uint8Array)
      case 'deflate':
        return await this.deflateCompress(uint8Array)
      case 'br':
        return await this.brotliCompress(uint8Array)
      default:
        throw new Error(`Unsupported encoding: ${encoding}`)
    }
  }
  
  private async gzipCompress(data: Uint8Array): Promise<ArrayBuffer> {
    // Use native compression if available
    if (typeof CompressionStream !== 'undefined') {
      const stream = new CompressionStream('gzip')
      const writer = stream.writable.getWriter()
      const reader = stream.readable.getReader()
      
      writer.write(data)
      writer.close()
      
      const chunks: Uint8Array[] = []
      let done = false
      
      while (!done) {
        const { value, done: readerDone } = await reader.read()
        done = readerDone
        if (value) chunks.push(value)
      }
      
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0)
      const result = new Uint8Array(totalLength)
      let offset = 0
      
      for (const chunk of chunks) {
        result.set(chunk, offset)
        offset += chunk.length
      }
      
      return result.buffer
    }
    
    // Fallback: return original data
    return data.buffer
  }
  
  private async deflateCompress(data: Uint8Array): Promise<ArrayBuffer> {
    // Similar implementation to gzip but with 'deflate'
    if (typeof CompressionStream !== 'undefined') {
      const stream = new CompressionStream('deflate')
      // ... similar implementation
    }
    
    return data.buffer
  }
  
  private async brotliCompress(data: Uint8Array): Promise<ArrayBuffer> {
    // Brotli compression would require a library
    // For now, fallback to original data
    return data.buffer
  }
}

let compressionHandler: CompressionHandler

const compressionPlugin: KilatPlugin = {
  name: 'compression',
  version: '1.0.0',
  description: 'Response compression for better performance',
  author: 'Kilat.js Team',
  builtin: true,
  
  config: {
    enabled: true,
    algorithms: ['gzip', 'deflate', 'br'],
    threshold: 1024, // 1KB
    level: 6,
    mimeTypes: [
      'text/html',
      'text/css',
      'text/javascript',
      'application/javascript',
      'application/json',
      'text/xml',
      'application/xml',
      'text/plain'
    ]
  },
  
  hooks: {
    'plugin:init': async (context: KilatContext) => {
      const config = context.config.plugins.find(p => p.name === 'compression')?.config || {}
      compressionHandler = new CompressionHandler(config as CompressionOptions)
      console.log('🗜️ Compression plugin initialized')
    },
    
    'request:after': async (context: KilatContext, data: any) => {
      if (context.mode === 'production' && data?.response && data?.request) {
        const acceptEncoding = data.request.headers.get('Accept-Encoding') || ''
        data.response = await compressionHandler.compressResponse(data.response, acceptEncoding)
      }
    }
  }
}

/**
 * Get compression handler instance
 */
export function getCompressionHandler(): CompressionHandler | undefined {
  return compressionHandler
}

export default compressionPlugin
