/**
 * 🦶 Footer Component - Global Footer
 */

import React from 'react'

export default function Footer() {
  return (
    <footer className="relative z-10 mt-20 border-t border-white/10 kilat-glass">
      <div className="kilat-container py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">K</span>
              </div>
              <span className="text-xl font-bold kilat-gradient-text">Kilat.js</span>
            </div>
            <p className="text-gray-400 max-w-md mb-6">
              Modern fullstack framework with SpeedRun™ Runtime. Built for speed, 
              developer experience, and production-ready applications.
            </p>
            
            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors">
                <span className="text-gray-300">📧</span>
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors">
                <span className="text-gray-300">🐙</span>
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors">
                <span className="text-gray-300">🐦</span>
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors">
                <span className="text-gray-300">💬</span>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><a href="/" className="text-gray-400 hover:text-white transition-colors">🏠 Home</a></li>
              <li><a href="/about" className="text-gray-400 hover:text-white transition-colors">📄 About</a></li>
              <li><a href="/dashboard" className="text-gray-400 hover:text-white transition-colors">📊 Dashboard</a></li>
              <li><a href="/api/users" className="text-gray-400 hover:text-white transition-colors">🛠️ API</a></li>
              <li><a href="/login" className="text-gray-400 hover:text-white transition-colors">🔑 Login</a></li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-white font-semibold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">📚 Documentation</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">🚀 Getting Started</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">💡 Examples</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">🎓 Tutorials</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">👥 Community</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row items-center justify-between">
          <p className="text-gray-400 text-sm">
            © 2024 Kilat.js. Built with ❤️ using SpeedRun™ Runtime.
          </p>
          <div className="flex items-center space-x-6 mt-4 md:mt-0">
            <span className="text-gray-400 text-sm">Powered by</span>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-yellow-400">🟡</span>
                <span className="text-gray-300 text-sm font-medium">Bun.js</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-blue-400">⚛️</span>
                <span className="text-gray-300 text-sm font-medium">React 18</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-purple-400">⚡</span>
                <span className="text-gray-300 text-sm font-medium">TypeScript</span>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Badge */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center px-4 py-2 kilat-glass rounded-full text-sm text-gray-300">
            <span className="mr-2">⚡</span>
            SpeedRun™ Runtime - Lightning Fast Performance
            <span className="ml-2 text-green-400">●</span>
          </div>
        </div>
      </div>
    </footer>
  )
}
