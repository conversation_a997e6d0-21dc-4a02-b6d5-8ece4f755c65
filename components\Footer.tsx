/**
 * 🦶 Footer Component - Minimalist & Precise Footer
 */

import React from 'react'

export default function Footer() {
  return (
    <footer className="relative z-10 mt-24">
      {/* Main Footer */}
      <div className="kilat-glass border-t border-white/5 backdrop-blur-xl">
        <div className="kilat-container py-8">
          {/* Top Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Brand */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-9 h-9 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-sm">K</span>
                </div>
                <div>
                  <span className="text-lg font-bold kilat-gradient-text">Kilat.js</span>
                  <p className="text-xs text-gray-500 leading-none">SpeedRun™ Framework</p>
                </div>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed max-w-xs">
                Lightning-fast fullstack framework for modern web development.
              </p>
            </div>

            {/* Navigation */}
            <div className="lg:col-span-1">
              <h3 className="text-white font-semibold mb-3 text-sm">Navigation</h3>
              <div className="grid grid-cols-2 gap-1">
                <a href="/" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">Home</a>
                <a href="/about" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">About</a>
                <a href="/blog" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">Blog</a>
                <a href="/dashboard" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">Dashboard</a>
              </div>
            </div>

            {/* Resources */}
            <div className="lg:col-span-1">
              <h3 className="text-white font-semibold mb-3 text-sm">Resources</h3>
              <div className="grid grid-cols-1 gap-1">
                <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">Documentation</a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">Examples</a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">Community</a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm py-1 hover:translate-x-0.5">Support</a>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="pt-4 border-t border-white/5">
            <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0">
              {/* Copyright */}
              <div className="flex flex-col sm:flex-row items-center space-y-1 sm:space-y-0 sm:space-x-3">
                <p className="text-gray-500 text-xs">
                  © 2024 Kilat.js. All rights reserved.
                </p>
                <div className="flex items-center space-x-2 text-xs">
                  <a href="#" className="text-gray-500 hover:text-gray-300 transition-colors">Privacy</a>
                  <span className="text-gray-600">•</span>
                  <a href="#" className="text-gray-500 hover:text-gray-300 transition-colors">Terms</a>
                  <span className="text-gray-600">•</span>
                  <a href="#" className="text-gray-500 hover:text-gray-300 transition-colors">License</a>
                </div>
              </div>

              {/* Tech Stack & Social */}
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <span className="w-1.5 h-1.5 bg-yellow-400 rounded-full"></span>
                    <span>Bun</span>
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <span className="w-1.5 h-1.5 bg-blue-400 rounded-full"></span>
                    <span>React</span>
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <span className="w-1.5 h-1.5 bg-purple-400 rounded-full"></span>
                    <span>TS</span>
                  </div>
                </div>

                {/* Social Links */}
                <div className="flex items-center space-x-1 ml-3 pl-3 border-l border-white/5">
                  <a href="#" className="w-7 h-7 kilat-glass rounded-lg flex items-center justify-center hover:bg-white/10 transition-all duration-200">
                    <span className="text-gray-400 text-xs">📧</span>
                  </a>
                  <a href="#" className="w-7 h-7 kilat-glass rounded-lg flex items-center justify-center hover:bg-white/10 transition-all duration-200">
                    <span className="text-gray-400 text-xs">🐙</span>
                  </a>
                  <a href="#" className="w-7 h-7 kilat-glass rounded-lg flex items-center justify-center hover:bg-white/10 transition-all duration-200">
                    <span className="text-gray-400 text-xs">💬</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Indicator */}
      <div className="text-center py-2 border-t border-white/5">
        <div className="kilat-container">
          <div className="inline-flex items-center space-x-1.5 text-xs text-gray-500">
            <span className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></span>
            <span>SpeedRun™ Active</span>
          </div>
        </div>
      </div>
    </footer>
  )
}
