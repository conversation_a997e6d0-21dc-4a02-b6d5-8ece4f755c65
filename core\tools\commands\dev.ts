/**
 * 🚀 Dev Command - Advanced Development Server
 */

import { createKilatServer } from '../../server'
import config from '../../../kilat.config'

export async function devCommand(args: string[]): Promise<void> {
  console.log('🚀 Starting Kilat.js Advanced Development Server...')

  try {
    // Parse arguments
    const port = getArgValue(args, '--port') || config.port?.toString() || '3000'
    const host = getArgValue(args, '--host') || config.host || 'localhost'

    // Create and start advanced server
    await createKilatServer({
      runtime: 'bun',
      port: parseInt(port),
      host,
      cors: true,
      compression: true,
      staticFiles: true,
      hotReload: true
    })

    console.log('')
    console.log('📋 Available routes:')
    console.log('  🏠 /              - Homepage')
    console.log('  📄 /about         - About page')
    console.log('  🔑 /login         - Login page')
    console.log('  ✨ /register      - Register page')
    console.log('  📊 /dashboard     - Dashboard (auth required)')
    console.log('  📝 /blog          - Blog index')
    console.log('  📝 /blog/[slug]   - Dynamic blog posts')
    console.log('  🛠️ /api/users     - Users API')
    console.log('  🔐 /api/auth/*    - Authentication API')
    console.log('')
    console.log('🎯 Features enabled:')
    console.log('  ⚡ SSG - Static Site Generation')
    console.log('  🖥️ SSR - Server-Side Rendering')
    console.log('  🌐 CSR - Client-Side Rendering')
    console.log('  🧭 File-based Routing')
    console.log('  🎨 KilatCSS & KilatAnim')
    console.log('  🛡️ Middleware Support')
    console.log('  📱 Responsive Design')
    console.log('')
    
    console.log('✅ Development server started successfully!')
    console.log(`🌐 Local:    http://${host}:${port}`)
    console.log(`🔥 HMR:      Enabled`)
    console.log(`📊 Analytics: Enabled`)
    console.log('')
    console.log('Press Ctrl+C to stop the server')
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down development server...')
      process.exit(0)
    })
    
  } catch (error) {
    console.error('❌ Failed to start development server:', error)
    process.exit(1)
  }
}

/**
 * Get argument value
 */
function getArgValue(args: string[], flag: string): string | undefined {
  const index = args.indexOf(flag)
  return index !== -1 && index + 1 < args.length ? args[index + 1] : undefined
}
