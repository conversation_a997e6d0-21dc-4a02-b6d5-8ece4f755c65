/**
 * 🧪 Test Command - Unit & E2E Testing
 */

export async function testCommand(args: string[]): Promise<void> {
  console.log('🧪 Running Kilat.js tests...')
  
  try {
    const watch = args.includes('--watch')
    const coverage = args.includes('--coverage')
    const e2e = args.includes('--e2e')
    const unit = args.includes('--unit') || (!e2e && !args.includes('--e2e'))
    
    if (unit) {
      await runUnitTests(watch, coverage)
    }
    
    if (e2e) {
      await runE2ETests(watch)
    }
    
    console.log('✅ All tests completed!')
    
  } catch (error) {
    console.error('❌ Tests failed:', error)
    process.exit(1)
  }
}

/**
 * Run unit tests with Bun test
 */
async function runUnitTests(watch: boolean, coverage: boolean): Promise<void> {
  console.log('🔬 Running unit tests...')
  
  const args = ['test']
  
  if (watch) {
    args.push('--watch')
  }
  
  if (coverage) {
    args.push('--coverage')
  }
  
  // Run Bun test
  try {
    const { spawn } = await import('child_process')
    
    const testProcess = spawn('bun', args, {
      stdio: 'inherit',
      cwd: process.cwd()
    })
    
    await new Promise((resolve, reject) => {
      testProcess.on('close', (code) => {
        if (code === 0) {
          resolve(code)
        } else {
          reject(new Error(`Unit tests failed with code ${code}`))
        }
      })
    })
    
    console.log('✅ Unit tests passed!')
    
  } catch (error) {
    console.error('❌ Unit tests failed:', error)
    throw error
  }
}

/**
 * Run E2E tests with Playwright
 */
async function runE2ETests(watch: boolean): Promise<void> {
  console.log('🎭 Running E2E tests...')
  
  const args = ['test']
  
  if (watch) {
    args.push('--ui')
  }
  
  // Run Playwright
  try {
    const { spawn } = await import('child_process')
    
    const testProcess = spawn('npx', ['playwright', ...args], {
      stdio: 'inherit',
      cwd: process.cwd()
    })
    
    await new Promise((resolve, reject) => {
      testProcess.on('close', (code) => {
        if (code === 0) {
          resolve(code)
        } else {
          reject(new Error(`E2E tests failed with code ${code}`))
        }
      })
    })
    
    console.log('✅ E2E tests passed!')
    
  } catch (error) {
    console.error('❌ E2E tests failed:', error)
    throw error
  }
}
