/**
 * 🚀 Kilat.js Core Framework - Advanced Version
 * Complete Next.js App Router-like system
 */

// Core Types
export * from './types'

// Advanced Routing System
export * from './router'
export { router } from './router'

// Rendering Engine
export * from './renderer'
export { renderer } from './renderer'

// Static Generator
export * from './generator'
export { generator } from './generator'

// Build System
export * from './build'
export { builder } from './build'

// Advanced Server
export * from './server'
export { server, createKilatServer } from './server'

// Static File Handler
export * from './static'
export { staticHandler } from './static'

// UI Systems
export * from './systems/css'
export * from './systems/anim'

// Version
export const version = '2.0.0'
export const codename = 'SpeedRun™ Advanced'
