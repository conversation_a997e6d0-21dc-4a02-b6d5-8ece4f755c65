/**
 * 🔄 ESM + CJS Interop Loader
 * Seamless module loading for both ESM and CommonJS
 */

import { pathToFileURL } from 'url'
import { existsSync } from 'fs'
import { join, extname } from 'path'

export interface ModuleCache {
  [key: string]: any
}

// Module cache for hot reloading
const moduleCache: ModuleCache = {}

/**
 * Universal module loader with ESM/CJS interop
 */
export async function loadModule(modulePath: string): Promise<any> {
  try {
    // Check cache first (for development HMR)
    if (process.env.NODE_ENV === 'development' && moduleCache[modulePath]) {
      return moduleCache[modulePath]
    }

    let module: any

    // Determine if we're in Bun or Node.js
    if (typeof Bun !== 'undefined') {
      // Bun native import
      module = await import(modulePath)
    } else {
      // Node.js with ESM/CJS detection
      const ext = extname(modulePath)
      
      if (ext === '.mjs' || (ext === '.js' && isESMModule(modulePath))) {
        // ESM import
        const fileUrl = pathToFileURL(modulePath).href
        module = await import(fileUrl)
      } else {
        // CommonJS require (with dynamic import fallback)
        try {
          module = require(modulePath)
        } catch (error) {
          // Fallback to dynamic import
          const fileUrl = pathToFileURL(modulePath).href
          module = await import(fileUrl)
        }
      }
    }

    // Normalize module exports
    const normalizedModule = normalizeModuleExports(module)

    // Cache for development
    if (process.env.NODE_ENV === 'development') {
      moduleCache[modulePath] = normalizedModule
    }

    return normalizedModule
  } catch (error) {
    console.error(`❌ Failed to load module ${modulePath}:`, error)
    throw error
  }
}

/**
 * Load React component with proper error handling
 */
export async function loadComponent(componentPath: string): Promise<React.ComponentType<any>> {
  try {
    const module = await loadModule(componentPath)
    
    // Extract component from module
    const Component = module.default || module
    
    if (typeof Component !== 'function') {
      throw new Error(`Invalid component: ${componentPath} does not export a React component`)
    }
    
    return Component
  } catch (error) {
    console.error(`❌ Failed to load component ${componentPath}:`, error)
    
    // Return error boundary component
    return function ErrorComponent({ error: componentError }: { error?: Error }) {
      return React.createElement('div', {
        style: {
          padding: '20px',
          border: '2px solid #ff6b6b',
          borderRadius: '8px',
          backgroundColor: '#ffe6e6',
          color: '#d63031',
          fontFamily: 'monospace'
        }
      }, [
        React.createElement('h3', { key: 'title' }, '⚠️ Component Error'),
        React.createElement('p', { key: 'message' }, `Failed to load: ${componentPath}`),
        React.createElement('pre', { key: 'error' }, (componentError || error)?.message)
      ])
    }
  }
}

/**
 * Check if module is ESM
 */
function isESMModule(modulePath: string): boolean {
  try {
    // Check package.json for type: "module"
    const packageJsonPath = findPackageJson(modulePath)
    if (packageJsonPath && existsSync(packageJsonPath)) {
      const packageJson = require(packageJsonPath)
      return packageJson.type === 'module'
    }
    
    return false
  } catch {
    return false
  }
}

/**
 * Find nearest package.json
 */
function findPackageJson(startPath: string): string | null {
  let currentPath = startPath
  
  while (currentPath !== '/') {
    const packageJsonPath = join(currentPath, 'package.json')
    if (existsSync(packageJsonPath)) {
      return packageJsonPath
    }
    
    currentPath = join(currentPath, '..')
  }
  
  return null
}

/**
 * Normalize module exports for consistent interface
 */
function normalizeModuleExports(module: any): any {
  // Handle different export patterns
  if (module && typeof module === 'object') {
    // ESM with default export
    if (module.default) {
      return {
        ...module,
        default: module.default
      }
    }
    
    // CommonJS or named exports only
    return module
  }
  
  // Direct export (function, class, etc.)
  return { default: module }
}

/**
 * Clear module cache (for HMR)
 */
export function clearModuleCache(modulePath?: string): void {
  if (modulePath) {
    delete moduleCache[modulePath]
    
    // Also clear from Node.js require cache
    if (typeof require !== 'undefined' && require.cache) {
      delete require.cache[modulePath]
    }
  } else {
    // Clear all cache
    Object.keys(moduleCache).forEach(key => {
      delete moduleCache[key]
    })
    
    if (typeof require !== 'undefined' && require.cache) {
      Object.keys(require.cache).forEach(key => {
        delete require.cache[key]
      })
    }
  }
}

/**
 * Preload modules for better performance
 */
export async function preloadModules(modulePaths: string[]): Promise<void> {
  const promises = modulePaths.map(path => 
    loadModule(path).catch(error => {
      console.warn(`⚠️ Failed to preload module ${path}:`, error)
    })
  )
  
  await Promise.all(promises)
}
