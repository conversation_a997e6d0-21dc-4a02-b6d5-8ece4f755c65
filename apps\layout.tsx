/**
 * 🏗️ Root Layout - Enhanced Global Layout with KilatCSS & KilatAnim
 */

import React from 'react'

export interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-x-hidden">
      {/* Global Background Effects - Fixed Position */}
      <div className="fixed inset-0 pointer-events-none z-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl kilat-hero-float" />
        <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl kilat-hero-pulse" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl" />
      </div>

      {/* Navigation */}
      <nav className="relative z-50 kilat-glass border-b border-white/10">
        <div className="kilat-container">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <a href="/" className="flex items-center space-x-2 kilat-glow-hover transition-all duration-300">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">K</span>
              </div>
              <span className="text-xl font-bold kilat-gradient-text">Kilat.js</span>
            </a>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
                🏠 Home
              </a>
              <a href="/about" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
                📄 About
              </a>
              <a href="/dashboard" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
                📊 Dashboard
              </a>
              <a href="/api/users" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
                🛠️ API
              </a>
            </div>

            {/* Mobile Menu Button */}
            <button className="md:hidden kilat-btn kilat-btn-outline px-3 py-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="relative z-10">
        {children}
      </main>

      {/* Footer */}
      <footer className="relative z-10 mt-20 border-t border-white/10 kilat-glass">
        <div className="kilat-container py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">K</span>
                </div>
                <span className="text-xl font-bold kilat-gradient-text">Kilat.js</span>
              </div>
              <p className="text-gray-400 max-w-md">
                Modern fullstack framework with SpeedRun™ Runtime. Built for speed,
                developer experience, and production-ready applications.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><a href="/" className="text-gray-400 hover:text-white transition-colors">🏠 Home</a></li>
                <li><a href="/about" className="text-gray-400 hover:text-white transition-colors">📄 About</a></li>
                <li><a href="/dashboard" className="text-gray-400 hover:text-white transition-colors">📊 Dashboard</a></li>
                <li><a href="/api/users" className="text-gray-400 hover:text-white transition-colors">🛠️ API</a></li>
              </ul>
            </div>

            {/* Resources */}
            <div>
              <h3 className="text-white font-semibold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">📚 Documentation</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">🐙 GitHub</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">💡 Examples</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">👥 Community</a></li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row items-center justify-between">
            <p className="text-gray-400 text-sm">
              © 2024 Kilat.js. Built with ❤️ using SpeedRun™ Runtime.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Powered by</span>
              <div className="flex items-center space-x-2">
                <span className="text-yellow-400">🟡</span>
                <span className="text-gray-300 text-sm font-medium">Bun.js</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-blue-400">⚛️</span>
                <span className="text-gray-300 text-sm font-medium">React 18</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
