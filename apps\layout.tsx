/**
 * 🏗️ Root Layout - Enhanced Global Layout with KilatCSS & KilatAnim
 */

import React from 'react'
import Header from '../components/Header'
import Footer from '../components/Footer'

export interface RootLayoutProps {
  children: React.ReactNode
  isAuthenticated?: boolean
  user?: {
    name: string
    email: string
    avatar?: string
  }
}

export default function RootLayout({ children, isAuthenticated = false, user }: RootLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-x-hidden">
      {/* Global Background Effects - Fixed Position */}
      <div className="fixed inset-0 pointer-events-none z-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl kilat-hero-float" />
        <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl kilat-hero-pulse" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl" />
      </div>

      {/* Header */}
      <Header isAuthenticated={isAuthenticated} user={user} />

      {/* Main Content */}
      <main className="relative z-10">
        {children}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
}
