/**
 * 🏗️ Root Layout - Global layout for all pages
 */

import React from 'react'

export interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Kilat.js App</title>
        <link rel="stylesheet" href="/_kilat/styles.css" />
      </head>
      <body className="bg-gray-900 text-white min-h-screen">
        {/* Navigation */}
        <nav className="bg-gray-800 border-b border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-xl font-bold text-blue-400">⚡ Kilat.js</span>
                </div>
                <div className="hidden md:block">
                  <div className="ml-10 flex items-baseline space-x-4">
                    <a href="/" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                      Home
                    </a>
                    <a href="/about" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                      About
                    </a>
                    <a href="/dashboard" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                      Dashboard
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1">
          {children}
        </main>

        {/* Footer */}
        <footer className="bg-gray-800 border-t border-gray-700 mt-auto">
          <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div className="text-center text-gray-400 text-sm">
              <p>&copy; 2025 Kilat.js Framework. Built with SpeedRun™ Runtime.</p>
            </div>
          </div>
        </footer>

        {/* Kilat.js Runtime Scripts */}
        <script src="/_kilat/runtime.js"></script>
        <script src="/_kilat/client.js"></script>
      </body>
    </html>
  )
}
