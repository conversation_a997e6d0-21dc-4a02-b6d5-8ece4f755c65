/**
 * 🔑 Login Page - User Authentication
 */

import React from 'react'

export default function LoginPage() {
  return (
    <>
      {/* Login Section */}
      <section className="py-20 sm:py-32">
        <div className="kilat-container">
          <div className="max-w-md mx-auto">
            {/* Header */}
            <div className="text-center mb-8 kilat-fade-in">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🔑</span>
              </div>
              <h1 className="text-3xl font-bold text-white mb-2">Welcome Back</h1>
              <p className="text-gray-400">Sign in to access your Kilat.js dashboard</p>
            </div>

            {/* Login Form */}
            <div className="kilat-card kilat-glass p-8 kilat-fade-in" style={{ animationDelay: '0.2s' }}>
              <form className="space-y-6" data-kilat-form action="/api/auth/login" method="POST">
                {/* Email Field */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="Enter your email"
                  />
                </div>

                {/* Password Field */}
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="Enter your password"
                  />
                </div>

                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="remember"
                      className="w-4 h-4 text-blue-500 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className="ml-2 text-sm text-gray-300">Remember me</span>
                  </label>
                  <a href="/forgot-password" className="text-sm text-blue-400 hover:text-blue-300 transition-colors">
                    Forgot password?
                  </a>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  data-original-text="Sign In"
                  className="w-full kilat-btn kilat-btn-primary py-3 text-lg font-semibold kilat-glow-hover transition-all duration-300"
                >
                  <span className="mr-2">🚀</span>
                  Sign In
                </button>

                {/* Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-white/20"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-gray-900 text-gray-400">Or continue with</span>
                  </div>
                </div>

                {/* Social Login */}
                <div className="grid grid-cols-2 gap-4">
                  <button
                    type="button"
                    className="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"
                  >
                    <span>🐙</span>
                    <span>GitHub</span>
                  </button>
                  <button
                    type="button"
                    className="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"
                  >
                    <span>🌐</span>
                    <span>Google</span>
                  </button>
                </div>
              </form>
            </div>

            {/* Register Link */}
            <div className="text-center mt-8 kilat-fade-in" style={{ animationDelay: '0.4s' }}>
              <p className="text-gray-400">
                Don't have an account?{' '}
                <a href="/register" className="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                  Create one here
                </a>
              </p>
            </div>

            {/* Demo Credentials */}
            <div className="mt-8 kilat-card kilat-glass p-4 kilat-fade-in" style={{ animationDelay: '0.6s' }}>
              <h3 className="text-white font-medium mb-2">Demo Credentials</h3>
              <div className="text-sm text-gray-300 space-y-1">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> demo123</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Why Choose Kilat.js?</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Experience the power of modern web development with our lightning-fast framework
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="kilat-card kilat-glass p-6 text-center kilat-fade-in" style={{ animationDelay: '0.8s' }}>
              <div className="text-3xl mb-4">⚡</div>
              <h3 className="text-lg font-bold text-white mb-2">Lightning Fast</h3>
              <p className="text-gray-300 text-sm">SpeedRun™ Runtime delivers unmatched performance</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-fade-in" style={{ animationDelay: '1.0s' }}>
              <div className="text-3xl mb-4">🛠️</div>
              <h3 className="text-lg font-bold text-white mb-2">Developer Friendly</h3>
              <p className="text-gray-300 text-sm">Zero configuration, maximum productivity</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-fade-in" style={{ animationDelay: '1.2s' }}>
              <div className="text-3xl mb-4">🚀</div>
              <h3 className="text-lg font-bold text-white mb-2">Production Ready</h3>
              <p className="text-gray-300 text-sm">Built-in optimizations and best practices</p>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
