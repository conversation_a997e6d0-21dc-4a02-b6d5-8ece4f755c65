/**
 * 🚀 Kilat.js Runtime - Core JavaScript Runtime
 */

(function() {
  'use strict';

  // Kilat.js Runtime
  window.Kilat = {
    version: '1.0.0',
    mode: 'production',
    initialized: false,
    
    // Core modules
    modules: {},
    
    // Event system
    events: {},
    
    // Initialize runtime
    init: function() {
      if (this.initialized) return;
      
      console.log('🚀 Kilat.js Runtime v' + this.version + ' initializing...');
      
      // Set mode
      this.mode = window.__KILAT_DEV__ ? 'development' : 'production';
      
      // Initialize modules
      this.initModules();
      
      // Setup error handling
      this.setupErrorHandling();
      
      // Mark as initialized
      this.initialized = true;
      
      console.log('✅ Kilat.js Runtime initialized');
      
      // Emit ready event
      this.emit('ready');
    },
    
    // Initialize modules
    initModules: function() {
      // Theme system
      this.modules.theme = {
        current: 'default',
        set: function(theme) {
          document.documentElement.setAttribute('data-theme', theme);
          localStorage.setItem('kilat-theme', theme);
          this.current = theme;
          window.Kilat.emit('theme:changed', theme);
        },
        get: function() {
          return this.current;
        },
        load: function() {
          const saved = localStorage.getItem('kilat-theme');
          if (saved) {
            this.set(saved);
          }
        }
      };
      
      // Performance monitoring
      this.modules.perf = {
        marks: {},
        measures: {},
        mark: function(name) {
          if (window.performance && window.performance.mark) {
            window.performance.mark(name);
            this.marks[name] = Date.now();
          }
        },
        measure: function(name, start, end) {
          if (window.performance && window.performance.measure) {
            window.performance.measure(name, start, end);
            const measure = window.performance.getEntriesByName(name)[0];
            this.measures[name] = measure ? measure.duration : 0;
            return this.measures[name];
          }
        }
      };
      
      // Load theme
      this.modules.theme.load();
    },
    
    // Setup error handling
    setupErrorHandling: function() {
      window.addEventListener('error', function(event) {
        console.error('Kilat.js Runtime Error:', event.error);
        window.Kilat.emit('error', event.error);
      });
      
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Kilat.js Unhandled Promise Rejection:', event.reason);
        window.Kilat.emit('error', event.reason);
      });
    },
    
    // Event system
    on: function(event, callback) {
      if (!this.events[event]) {
        this.events[event] = [];
      }
      this.events[event].push(callback);
    },
    
    off: function(event, callback) {
      if (this.events[event]) {
        this.events[event] = this.events[event].filter(cb => cb !== callback);
      }
    },
    
    emit: function(event, data) {
      if (this.events[event]) {
        this.events[event].forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error('Event callback error:', error);
          }
        });
      }
    },
    
    // Utility functions
    utils: {
      // Debounce function
      debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
          const later = function() {
            clearTimeout(timeout);
            func(...args);
          };
          clearTimeout(timeout);
          timeout = setTimeout(later, wait);
        };
      },
      
      // Throttle function
      throttle: function(func, limit) {
        let inThrottle;
        return function() {
          const args = arguments;
          const context = this;
          if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
          }
        };
      },
      
      // Check if element is in viewport
      isInViewport: function(element) {
        const rect = element.getBoundingClientRect();
        return (
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
      }
    }
  };

  // Auto-initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      window.Kilat.init();
    });
  } else {
    window.Kilat.init();
  }

})();
