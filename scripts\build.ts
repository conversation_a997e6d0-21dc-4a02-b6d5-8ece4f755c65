#!/usr/bin/env bun
/**
 * 🏗️ Kilat.js Build Command
 * Auto-build with SSG, SSR, CSR optimization
 */

import { builder } from '../core/build'

async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'build'

  switch (command) {
    case 'build':
      await builder.build()
      break
      
    case 'dev':
      await builder.dev()
      break
      
    case 'clean':
      await builder.clean()
      console.log('✅ Build directory cleaned')
      break
      
    case 'help':
      showHelp()
      break
      
    default:
      console.error(`Unknown command: ${command}`)
      showHelp()
      process.exit(1)
  }
}

function showHelp() {
  console.log(`
🚀 Kilat.js Build Commands

Usage:
  bun run build [command]

Commands:
  build     Build for production (default)
  dev       Start development server
  clean     Clean build directory
  help      Show this help

Examples:
  bun run build
  bun run build dev
  bun run build clean
`)
}

// Run if called directly
if (import.meta.main) {
  main().catch(error => {
    console.error('Build failed:', error)
    process.exit(1)
  })
}
