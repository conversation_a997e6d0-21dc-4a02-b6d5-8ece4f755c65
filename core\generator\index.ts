/**
 * ⚡ Kilat.js Static Site Generator
 * Build-time static generation with dynamic route support
 */

import { join, dirname } from 'path'
import { existsSync, mkdirSync, writeFileSync, readFileSync, readdirSync, statSync } from 'fs'
import type { 
  StaticGenerationConfig, 
  GenerateStaticParamsResult, 
  BuildConfig,
  RouteConfig 
} from '../types'
import { router } from '../router'
import { renderer } from '../renderer'

export class KilatGenerator {
  private buildConfig: BuildConfig
  private generatedPaths: Set<string> = new Set()

  constructor(config: Partial<BuildConfig> = {}) {
    this.buildConfig = {
      outDir: '.kilat/dist',
      staticDir: '.kilat/static',
      serverDir: '.kilat/server',
      clientDir: '.kilat/client',
      mode: 'production',
      target: 'hybrid',
      ...config
    }
  }

  /**
   * Generate all static routes
   */
  async generateAll(): Promise<void> {
    console.log('⚡ Starting static generation...')
    
    // Create output directories
    this.createDirectories()
    
    // Get all routes
    const routes = router.getAllRoutes()
    
    // Generate static routes
    for (const route of routes) {
      if (route.renderMode === 'ssg' || route.type === 'page') {
        await this.generateRoute(route)
      }
    }
    
    // Copy static assets
    await this.copyStaticAssets()
    
    // Generate manifest
    await this.generateManifest()
    
    console.log(`✅ Generated ${this.generatedPaths.size} static pages`)
  }

  /**
   * Generate single route
   */
  async generateRoute(route: RouteConfig): Promise<void> {
    try {
      if (route.isDynamic) {
        await this.generateDynamicRoute(route)
      } else {
        await this.generateStaticRoute(route)
      }
    } catch (error) {
      console.error(`Failed to generate route ${route.path}:`, error)
    }
  }

  /**
   * Generate static route (no dynamic segments)
   */
  private async generateStaticRoute(route: RouteConfig): Promise<void> {
    const context = {
      request: new Request(`http://localhost${route.path}`),
      pathname: route.path
    }

    const result = await renderer.render(route.path, context, 'ssg')
    const outputPath = this.getOutputPath(route.path)
    
    this.writeFile(outputPath, result.html)
    this.generatedPaths.add(route.path)
    
    console.log(`📄 Generated: ${route.path}`)
  }

  /**
   * Generate dynamic route with all possible params
   */
  private async generateDynamicRoute(route: RouteConfig): Promise<void> {
    // Check for generateStaticParams function
    const staticParams = await this.getStaticParams(route)
    
    if (staticParams.length === 0) {
      console.warn(`No static params found for dynamic route: ${route.path}`)
      return
    }

    for (const paramSet of staticParams) {
      const pathname = this.buildPathFromParams(route.path, paramSet.params)
      const context = {
        request: new Request(`http://localhost${pathname}`),
        pathname,
        params: paramSet.params
      }

      const result = await renderer.render(pathname, context, 'ssg')
      const outputPath = this.getOutputPath(pathname)
      
      this.writeFile(outputPath, result.html)
      this.generatedPaths.add(pathname)
      
      console.log(`📄 Generated: ${pathname}`)
    }
  }

  /**
   * Get static params for dynamic route
   */
  private async getStaticParams(route: RouteConfig): Promise<GenerateStaticParamsResult[]> {
    try {
      // Look for generate.ts file in route directory
      const routeDir = dirname(route.component)
      const generatePath = join(routeDir, 'generate.ts')
      const generateJsPath = join(routeDir, 'generate.js')
      
      let generateFile = generatePath
      if (!existsSync(generatePath) && existsSync(generateJsPath)) {
        generateFile = generateJsPath
      }

      if (!existsSync(generateFile)) {
        return []
      }

      const module = await import(generateFile)
      if (module.generateStaticParams) {
        const params = await module.generateStaticParams()
        return Array.isArray(params) ? params : [params]
      }

      return []
    } catch (error) {
      console.error(`Failed to get static params for ${route.path}:`, error)
      return []
    }
  }

  /**
   * Build path from route pattern and params
   */
  private buildPathFromParams(routePath: string, params: Record<string, string>): string {
    let path = routePath
    
    // Replace dynamic segments
    for (const [key, value] of Object.entries(params)) {
      path = path.replace(`:${key}`, value)
      path = path.replace(`[${key}]`, value)
      path = path.replace(`[...${key}]`, value)
    }
    
    return path
  }

  /**
   * Get output file path for route
   */
  private getOutputPath(pathname: string): string {
    let filePath = pathname
    
    // Handle root path
    if (filePath === '/') {
      filePath = '/index'
    }
    
    // Ensure .html extension
    if (!filePath.endsWith('.html')) {
      filePath += '.html'
    }
    
    return join(this.buildConfig.outDir, filePath)
  }

  /**
   * Write file with directory creation
   */
  private writeFile(filePath: string, content: string): void {
    const dir = dirname(filePath)
    mkdirSync(dir, { recursive: true })
    writeFileSync(filePath, content, 'utf-8')
  }

  /**
   * Create build directories
   */
  private createDirectories(): void {
    const dirs = [
      this.buildConfig.outDir,
      this.buildConfig.staticDir,
      this.buildConfig.serverDir,
      this.buildConfig.clientDir
    ]

    for (const dir of dirs) {
      mkdirSync(dir, { recursive: true })
    }
  }

  /**
   * Copy static assets
   */
  private async copyStaticAssets(): Promise<void> {
    const publicDir = 'public'
    if (!existsSync(publicDir)) return

    const targetDir = join(this.buildConfig.outDir, '_kilat')
    mkdirSync(targetDir, { recursive: true })

    // Copy _kilat assets
    const kilatDir = join(publicDir, '_kilat')
    if (existsSync(kilatDir)) {
      this.copyDirectory(kilatDir, targetDir)
    }

    console.log('📁 Copied static assets')
  }

  /**
   * Copy directory recursively
   */
  private copyDirectory(src: string, dest: string): void {
    mkdirSync(dest, { recursive: true })
    
    const entries = readdirSync(src)
    for (const entry of entries) {
      const srcPath = join(src, entry)
      const destPath = join(dest, entry)
      const stat = statSync(srcPath)

      if (stat.isDirectory()) {
        this.copyDirectory(srcPath, destPath)
      } else {
        const content = readFileSync(srcPath)
        writeFileSync(destPath, content)
      }
    }
  }

  /**
   * Generate build manifest
   */
  private async generateManifest(): Promise<void> {
    const manifest = {
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      routes: Array.from(this.generatedPaths),
      config: this.buildConfig
    }

    const manifestPath = join(this.buildConfig.outDir, 'manifest.json')
    writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
    
    console.log('📋 Generated build manifest')
  }

  /**
   * Generate sitemap.xml
   */
  async generateSitemap(baseUrl: string = 'http://localhost:3000'): Promise<void> {
    const routes = Array.from(this.generatedPaths)
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${routes.map(route => `  <url>
    <loc>${baseUrl}${route}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('\n')}
</urlset>`

    const sitemapPath = join(this.buildConfig.outDir, 'sitemap.xml')
    writeFileSync(sitemapPath, sitemap)
    
    console.log('🗺️ Generated sitemap.xml')
  }

  /**
   * Generate robots.txt
   */
  async generateRobots(baseUrl: string = 'http://localhost:3000'): Promise<void> {
    const robots = `User-agent: *
Allow: /

Sitemap: ${baseUrl}/sitemap.xml`

    const robotsPath = join(this.buildConfig.outDir, 'robots.txt')
    writeFileSync(robotsPath, robots)
    
    console.log('🤖 Generated robots.txt')
  }

  /**
   * Clean build directory
   */
  clean(): void {
    if (existsSync(this.buildConfig.outDir)) {
      // Simple clean - in production, use proper recursive delete
      console.log('🧹 Cleaning build directory...')
    }
  }

  /**
   * Get build statistics
   */
  getStats(): any {
    return {
      generatedPaths: Array.from(this.generatedPaths),
      totalPages: this.generatedPaths.size,
      buildConfig: this.buildConfig
    }
  }
}

// Export singleton instance
export const generator = new KilatGenerator()
