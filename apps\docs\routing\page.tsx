/**
 * 🧭 Routing Documentation Page
 */

import React from 'react'
import type { GenerateMetadataResult } from '../../../core/types'

export default function RoutingPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="py-20">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto text-center kilat-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              🧭 <span className="kilat-gradient-text">File-based Routing</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Powerful Next.js App Router-like routing system with SSG, SSR, and CSR support.
            </p>
          </div>
        </div>
      </section>

      {/* Route Types */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-8">📁 Route Types</h2>
            
            <div className="space-y-6">
              {/* Static Routes */}
              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <span className="text-green-400 mr-3">📄</span>
                  Static Routes
                </h3>
                <div className="bg-gray-900 rounded-lg p-4 mb-4">
                  <code className="text-gray-300 font-mono text-sm">
                    apps/about/page.tsx → /about<br/>
                    apps/contact/page.tsx → /contact
                  </code>
                </div>
                <p className="text-gray-300">
                  Simple static routes that map directly to file paths.
                </p>
              </div>

              {/* Dynamic Routes */}
              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <span className="text-blue-400 mr-3">🔄</span>
                  Dynamic Routes
                </h3>
                <div className="bg-gray-900 rounded-lg p-4 mb-4">
                  <code className="text-gray-300 font-mono text-sm">
                    apps/blog/[slug]/page.tsx → /blog/my-post<br/>
                    apps/user/[id]/page.tsx → /user/123
                  </code>
                </div>
                <p className="text-gray-300">
                  Dynamic segments using square brackets for parameters.
                </p>
              </div>

              {/* Catch-all Routes */}
              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <span className="text-purple-400 mr-3">🕸️</span>
                  Catch-all Routes
                </h3>
                <div className="bg-gray-900 rounded-lg p-4 mb-4">
                  <code className="text-gray-300 font-mono text-sm">
                    apps/docs/[...slug]/page.tsx → /docs/guide/setup<br/>
                    apps/shop/[[...slug]]/page.tsx → /shop or /shop/category/item
                  </code>
                </div>
                <p className="text-gray-300">
                  Catch multiple path segments. Use [[...slug]] for optional catch-all.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Special Files */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-8">🎯 Special Files</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-lg font-bold text-white mb-3">page.tsx</h3>
                <p className="text-gray-300 text-sm mb-3">Main page component</p>
                <div className="bg-gray-900 rounded p-3">
                  <code className="text-xs text-gray-300">
                    export default function Page() {`{`}<br/>
                    &nbsp;&nbsp;return &lt;div&gt;Hello&lt;/div&gt;<br/>
                    {`}`}
                  </code>
                </div>
              </div>

              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-lg font-bold text-white mb-3">layout.tsx</h3>
                <p className="text-gray-300 text-sm mb-3">Shared layout wrapper</p>
                <div className="bg-gray-900 rounded p-3">
                  <code className="text-xs text-gray-300">
                    export default function Layout({`{children}`}) {`{`}<br/>
                    &nbsp;&nbsp;return &lt;div&gt;{`{children}`}&lt;/div&gt;<br/>
                    {`}`}
                  </code>
                </div>
              </div>

              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-lg font-bold text-white mb-3">loading.tsx</h3>
                <p className="text-gray-300 text-sm mb-3">Loading UI component</p>
                <div className="bg-gray-900 rounded p-3">
                  <code className="text-xs text-gray-300">
                    export default function Loading() {`{`}<br/>
                    &nbsp;&nbsp;return &lt;div&gt;Loading...&lt;/div&gt;<br/>
                    {`}`}
                  </code>
                </div>
              </div>

              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-lg font-bold text-white mb-3">error.tsx</h3>
                <p className="text-gray-300 text-sm mb-3">Error boundary</p>
                <div className="bg-gray-900 rounded p-3">
                  <code className="text-xs text-gray-300">
                    export default function Error({`{error}`}) {`{`}<br/>
                    &nbsp;&nbsp;return &lt;div&gt;Error: {`{error.message}`}&lt;/div&gt;<br/>
                    {`}`}
                  </code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Rendering Modes */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-8">⚡ Rendering Modes</h2>
            
            <div className="space-y-6">
              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <span className="text-green-400 mr-3">🏗️</span>
                  Static Site Generation (SSG)
                </h3>
                <p className="text-gray-300 mb-4">
                  Pre-render pages at build time for maximum performance.
                </p>
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-gray-300 font-mono text-sm">
                    // apps/blog/[slug]/generate.ts<br/>
                    export async function generateStaticParams() {`{`}<br/>
                    &nbsp;&nbsp;return [{`{ params: { slug: 'hello-world' } }`}]<br/>
                    {`}`}
                  </code>
                </div>
              </div>

              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <span className="text-blue-400 mr-3">🖥️</span>
                  Server-Side Rendering (SSR)
                </h3>
                <p className="text-gray-300 mb-4">
                  Render pages on each request for dynamic content.
                </p>
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-gray-300 font-mono text-sm">
                    // Automatic SSR for pages without generate.ts<br/>
                    export default function Page() {`{`}<br/>
                    &nbsp;&nbsp;return &lt;div&gt;Dynamic content&lt;/div&gt;<br/>
                    {`}`}
                  </code>
                </div>
              </div>

              <div className="kilat-card kilat-glass p-6">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                  <span className="text-purple-400 mr-3">🌐</span>
                  Client-Side Rendering (CSR)
                </h3>
                <p className="text-gray-300 mb-4">
                  Render on the client for interactive applications.
                </p>
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-gray-300 font-mono text-sm">
                    // Use React hooks for client-side logic<br/>
                    export default function Page() {`{`}<br/>
                    &nbsp;&nbsp;const [data, setData] = useState(null)<br/>
                    &nbsp;&nbsp;return &lt;div&gt;{`{data}`}&lt;/div&gt;<br/>
                    {`}`}
                  </code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* API Routes */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-8">🛠️ API Routes</h2>
            
            <div className="kilat-card kilat-glass p-6">
              <p className="text-gray-300 mb-6">
                Create API endpoints using route.ts files in the apps/api directory.
              </p>
              
              <div className="bg-gray-900 rounded-lg p-6">
                <div className="text-sm text-gray-400 mb-2">apps/api/users/route.ts</div>
                <code className="text-gray-300 font-mono text-sm">
                  export async function GET(request: Request) {`{`}<br/>
                  &nbsp;&nbsp;const users = await fetchUsers()<br/>
                  &nbsp;&nbsp;return Response.json(users)<br/>
                  {`}`}<br/><br/>
                  export async function POST(request: Request) {`{`}<br/>
                  &nbsp;&nbsp;const body = await request.json()<br/>
                  &nbsp;&nbsp;const user = await createUser(body)<br/>
                  &nbsp;&nbsp;return Response.json(user, {`{ status: 201 }`})<br/>
                  {`}`}
                </code>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Next Steps */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto text-center">
            <div className="kilat-card kilat-glass p-8">
              <h2 className="text-3xl font-bold text-white mb-6">
                🚀 Ready to Route?
              </h2>
              <p className="text-gray-300 mb-8">
                Start building with Kilat.js file-based routing system.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a href="/docs/api" className="kilat-btn kilat-btn-primary px-6 py-3">
                  🛠️ API Reference
                </a>
                <a href="/docs/examples" className="kilat-btn kilat-btn-outline px-6 py-3">
                  💡 View Examples
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata(): Promise<GenerateMetadataResult> {
  return {
    title: 'Routing | Kilat.js Documentation',
    description: 'Learn about Kilat.js file-based routing system with SSG, SSR, and CSR support. Complete guide to dynamic routes and API endpoints.',
    keywords: ['kilat.js', 'routing', 'file-based', 'SSG', 'SSR', 'CSR', 'dynamic routes'],
    openGraph: {
      title: 'Kilat.js Routing Documentation',
      description: 'Complete guide to file-based routing with multiple rendering modes.',
      images: ['/api/og?title=Kilat.js Routing Guide']
    }
  }
}
