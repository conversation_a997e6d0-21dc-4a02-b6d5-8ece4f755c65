/**
 * 🌐 Native HTTP Server for SpeedRun™ Runtime
 * No Express dependency - pure Bun/Node.js implementation
 */

import type { KilatContext } from '../../types/config'
import { matchRoute } from './router'
import { renderPage } from './renderer'
import { handleApiRoute } from './api-handler'
import { serveStatic } from './static'

export interface ServerRequest {
  url: string
  method: string
  headers: Record<string, string>
  body?: any
}

export interface ServerResponse {
  status: number
  headers: Record<string, string>
  body: string | ReadableStream
}

/**
 * Create HTTP server instance
 */
export async function createServer(context: KilatContext) {
  const { config } = context

  /**
   * Main request handler
   */
  async function handleRequest(request: Request): Promise<Response> {
    const url = new URL(request.url)
    const pathname = url.pathname
    const method = request.method

    try {
      // Handle static files first
      if (pathname.startsWith('/public/') || pathname.startsWith('/_kilat/')) {
        return await serveStatic(pathname, context)
      }

      // Handle API routes
      if (pathname.startsWith('/api/')) {
        return await handleApiRoute(request, context)
      }

      // Handle page routes
      const route = matchRoute(pathname, context.routes)
      if (route) {
        return await renderPage(request, route, context)
      }

      // 404 Not Found
      return new Response('Not Found', { 
        status: 404,
        headers: { 'Content-Type': 'text/plain' }
      })

    } catch (error) {
      console.error('❌ Server error:', error)
      
      // Development error overlay
      if (context.mode === 'development') {
        return createErrorResponse(error, context)
      }
      
      // Production error page
      return new Response('Internal Server Error', { 
        status: 500,
        headers: { 'Content-Type': 'text/plain' }
      })
    }
  }

  /**
   * Error handler
   */
  function handleError(error: Error): Response {
    console.error('❌ Server error:', error)
    
    if (context.mode === 'development') {
      return createErrorResponse(error, context)
    }
    
    return new Response('Internal Server Error', { 
      status: 500,
      headers: { 'Content-Type': 'text/plain' }
    })
  }

  // Return server configuration for Bun
  if (typeof Bun !== 'undefined') {
    return {
      fetch: handleRequest,
      error: handleError,
    }
  }

  // Return server configuration for Node.js
  return {
    async listen(port: number, host: string) {
      const { createServer } = await import('http')
      
      const server = createServer(async (req, res) => {
        try {
          const request = new Request(`http://${host}:${port}${req.url}`, {
            method: req.method,
            headers: req.headers as any,
            body: req.method !== 'GET' && req.method !== 'HEAD' ? req : undefined,
          })
          
          const response = await handleRequest(request)
          
          res.statusCode = response.status
          
          response.headers.forEach((value, key) => {
            res.setHeader(key, value)
          })
          
          if (response.body) {
            if (typeof response.body === 'string') {
              res.end(response.body)
            } else {
              // Handle ReadableStream
              const reader = response.body.getReader()
              const pump = async () => {
                const { done, value } = await reader.read()
                if (done) {
                  res.end()
                } else {
                  res.write(value)
                  pump()
                }
              }
              pump()
            }
          } else {
            res.end()
          }
        } catch (error) {
          console.error('❌ Node.js server error:', error)
          res.statusCode = 500
          res.end('Internal Server Error')
        }
      })
      
      return new Promise((resolve, reject) => {
        server.listen(port, host, (error?: Error) => {
          if (error) reject(error)
          else resolve(server)
        })
      })
    }
  }
}

/**
 * Create development error response with overlay
 */
function createErrorResponse(error: Error, context: KilatContext): Response {
  const errorHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Kilat.js Error</title>
      <style>
        body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #ff6b6b; }
        .error { background: #2a2a2a; padding: 20px; border-radius: 8px; border-left: 4px solid #ff6b6b; }
        .stack { background: #1a1a1a; padding: 15px; margin-top: 10px; border-radius: 4px; overflow-x: auto; }
        h1 { color: #ff6b6b; margin: 0 0 10px 0; }
        pre { margin: 0; white-space: pre-wrap; }
      </style>
    </head>
    <body>
      <div class="error">
        <h1>⚡ Kilat.js Development Error</h1>
        <p><strong>Message:</strong> ${error.message}</p>
        <div class="stack">
          <pre>${error.stack}</pre>
        </div>
      </div>
    </body>
    </html>
  `
  
  return new Response(errorHtml, {
    status: 500,
    headers: { 'Content-Type': 'text/html' }
  })
}
