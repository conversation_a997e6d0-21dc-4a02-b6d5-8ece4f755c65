<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js SpeedRun™ Runtime" />
  
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><section class="py-20 sm:py-32"><div class="kilat-container"><div class="max-w-md mx-auto"><div class="text-center mb-8 kilat-fade-in"><div class="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4"><span class="text-white text-2xl">✨</span></div><h1 class="text-3xl font-bold text-white mb-2">Join Kilat.js</h1><p class="text-gray-400">Create your account and start building amazing apps</p></div><div class="kilat-card kilat-glass p-8 kilat-fade-in" style="animation-delay:0.2s"><form class="space-y-6" data-kilat-form="true" action="/api/auth/register" method="POST"><div><label for="name" class="block text-sm font-medium text-white mb-2">Full Name</label><input type="text" id="name" name="name" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300" placeholder="Enter your full name"/></div><div><label for="email" class="block text-sm font-medium text-white mb-2">Email Address</label><input type="email" id="email" name="email" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300" placeholder="Enter your email"/></div><div><label for="password" class="block text-sm font-medium text-white mb-2">Password</label><input type="password" id="password" name="password" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300" placeholder="Create a strong password"/><p class="text-xs text-gray-400 mt-1">Must be at least 8 characters with letters and numbers</p></div><div><label for="confirmPassword" class="block text-sm font-medium text-white mb-2">Confirm Password</label><input type="password" id="confirmPassword" name="confirmPassword" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300" placeholder="Confirm your password"/></div><div class="flex items-start"><input type="checkbox" id="terms" name="terms" required="" class="w-4 h-4 text-green-500 bg-white/10 border-white/20 rounded focus:ring-green-500 focus:ring-2 mt-1"/><label for="terms" class="ml-2 text-sm text-gray-300">I agree to the<!-- --> <a href="/terms" class="text-green-400 hover:text-green-300 transition-colors">Terms of Service</a> <!-- -->and<!-- --> <a href="/privacy" class="text-green-400 hover:text-green-300 transition-colors">Privacy Policy</a></label></div><button type="submit" data-original-text="Create Account" class="w-full kilat-btn kilat-btn-primary py-3 text-lg font-semibold kilat-glow-hover transition-all duration-300"><span class="mr-2">🚀</span>Create Account</button><div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-white/20"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-gray-900 text-gray-400">Or sign up with</span></div></div><div class="grid grid-cols-2 gap-4"><button type="button" class="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"><span>🐙</span><span>GitHub</span></button><button type="button" class="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"><span>🌐</span><span>Google</span></button></div></form></div><div class="text-center mt-8 kilat-fade-in" style="animation-delay:0.4s"><p class="text-gray-400">Already have an account?<!-- --> <a href="/login" class="text-green-400 hover:text-green-300 font-medium transition-colors">Sign in here</a></p></div></div></div></section><section class="py-16"><div class="kilat-container"><div class="text-center mb-12"><h2 class="text-3xl font-bold text-white mb-4">Start Building Today</h2><p class="text-gray-400 max-w-2xl mx-auto">Join thousands of developers who are already building amazing applications with Kilat.js</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:0.6s"><div class="text-2xl mb-3">🎯</div><h3 class="text-lg font-bold text-white mb-2">Zero Config</h3><p class="text-gray-300 text-sm">Start coding immediately without setup</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:0.8s"><div class="text-2xl mb-3">📱</div><h3 class="text-lg font-bold text-white mb-2">Responsive</h3><p class="text-gray-300 text-sm">Mobile-first design out of the box</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:1.0s"><div class="text-2xl mb-3">🔒</div><h3 class="text-lg font-bold text-white mb-2">Secure</h3><p class="text-gray-300 text-sm">Built-in security best practices</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:1.2s"><div class="text-2xl mb-3">🌟</div><h3 class="text-lg font-bold text-white mb-2">Modern</h3><p class="text-gray-300 text-sm">Latest web technologies and standards</p></div></div></div></section></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>