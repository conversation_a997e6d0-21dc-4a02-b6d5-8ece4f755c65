/**
 * ✨ Register Page - User Registration
 */

import React from 'react'

export default function RegisterPage() {
  return (
    <>
      {/* Register Section */}
      <section className="py-20 sm:py-32">
        <div className="kilat-container">
          <div className="max-w-md mx-auto">
            {/* Header */}
            <div className="text-center mb-8 kilat-fade-in">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">✨</span>
              </div>
              <h1 className="text-3xl font-bold text-white mb-2">Join <PERSON>.js</h1>
              <p className="text-gray-400">Create your account and start building amazing apps</p>
            </div>

            {/* Register Form */}
            <div className="kilat-card kilat-glass p-8 kilat-fade-in" style={{ animationDelay: '0.2s' }}>
              <form className="space-y-6" data-kilat-form action="/api/auth/register" method="POST">
                {/* Name Field */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-white mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    placeholder="Enter your full name"
                  />
                </div>

                {/* Email Field */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    placeholder="Enter your email"
                  />
                </div>

                {/* Password Field */}
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    placeholder="Create a strong password"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Must be at least 8 characters with letters and numbers
                  </p>
                </div>

                {/* Confirm Password Field */}
                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-white mb-2">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    placeholder="Confirm your password"
                  />
                </div>

                {/* Terms & Privacy */}
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="terms"
                    name="terms"
                    required
                    className="w-4 h-4 text-green-500 bg-white/10 border-white/20 rounded focus:ring-green-500 focus:ring-2 mt-1"
                  />
                  <label htmlFor="terms" className="ml-2 text-sm text-gray-300">
                    I agree to the{' '}
                    <a href="/terms" className="text-green-400 hover:text-green-300 transition-colors">
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="/privacy" className="text-green-400 hover:text-green-300 transition-colors">
                      Privacy Policy
                    </a>
                  </label>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  data-original-text="Create Account"
                  className="w-full kilat-btn kilat-btn-primary py-3 text-lg font-semibold kilat-glow-hover transition-all duration-300"
                >
                  <span className="mr-2">🚀</span>
                  Create Account
                </button>

                {/* Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-white/20"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-gray-900 text-gray-400">Or sign up with</span>
                  </div>
                </div>

                {/* Social Register */}
                <div className="grid grid-cols-2 gap-4">
                  <button
                    type="button"
                    className="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"
                  >
                    <span>🐙</span>
                    <span>GitHub</span>
                  </button>
                  <button
                    type="button"
                    className="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"
                  >
                    <span>🌐</span>
                    <span>Google</span>
                  </button>
                </div>
              </form>
            </div>

            {/* Login Link */}
            <div className="text-center mt-8 kilat-fade-in" style={{ animationDelay: '0.4s' }}>
              <p className="text-gray-400">
                Already have an account?{' '}
                <a href="/login" className="text-green-400 hover:text-green-300 font-medium transition-colors">
                  Sign in here
                </a>
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Start Building Today</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Join thousands of developers who are already building amazing applications with Kilat.js
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="kilat-card kilat-glass p-6 text-center kilat-fade-in" style={{ animationDelay: '0.6s' }}>
              <div className="text-2xl mb-3">🎯</div>
              <h3 className="text-lg font-bold text-white mb-2">Zero Config</h3>
              <p className="text-gray-300 text-sm">Start coding immediately without setup</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-fade-in" style={{ animationDelay: '0.8s' }}>
              <div className="text-2xl mb-3">📱</div>
              <h3 className="text-lg font-bold text-white mb-2">Responsive</h3>
              <p className="text-gray-300 text-sm">Mobile-first design out of the box</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-fade-in" style={{ animationDelay: '1.0s' }}>
              <div className="text-2xl mb-3">🔒</div>
              <h3 className="text-lg font-bold text-white mb-2">Secure</h3>
              <p className="text-gray-300 text-sm">Built-in security best practices</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-fade-in" style={{ animationDelay: '1.2s' }}>
              <div className="text-2xl mb-3">🌟</div>
              <h3 className="text-lg font-bold text-white mb-2">Modern</h3>
              <p className="text-gray-300 text-sm">Latest web technologies and standards</p>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
