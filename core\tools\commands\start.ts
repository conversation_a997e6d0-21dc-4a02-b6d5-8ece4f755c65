/**
 * 🚀 Start Command - Production Server
 */

import { createSpeedRunRuntime } from '../../kernel/runtime'
import { loadConfig } from '../config-loader'

export async function startCommand(args: string[]): Promise<void> {
  console.log('🚀 Starting Kilat.js production server...')
  
  try {
    // Parse arguments
    const port = getArgValue(args, '--port') || '3000'
    const host = getArgValue(args, '--host') || '0.0.0.0'
    
    // Load configuration
    const config = await loadConfig()
    
    // Override with CLI arguments and production settings
    config.runtime.port = parseInt(port)
    config.runtime.host = host
    
    // Force production mode
    process.env.NODE_ENV = 'production'
    
    // Create and start runtime
    const runtime = await createSpeedRunRuntime(config)
    await runtime.start()
    
    console.log('✅ Production server started successfully!')
    console.log(`🌐 Server:   http://${host}:${port}`)
    console.log(`🏗️ Mode:     Production`)
    console.log(`⚡ Engine:   ${config.runtime.engine}`)
    console.log('')
    console.log('Press Ctrl+C to stop the server')
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down production server...')
      await runtime.stop()
      process.exit(0)
    })
    
    process.on('SIGTERM', async () => {
      console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
      await runtime.stop()
      process.exit(0)
    })
    
    // Keep process alive
    await new Promise(() => {})
    
  } catch (error) {
    console.error('❌ Failed to start production server:', error)
    process.exit(1)
  }
}

/**
 * Get argument value
 */
function getArgValue(args: string[], flag: string): string | undefined {
  const index = args.indexOf(flag)
  return index !== -1 && index + 1 < args.length ? args[index + 1] : undefined
}
