/**
 * 🛡️ Kilat.js Advanced Error Handling System
 * Comprehensive error tracking, reporting, and recovery
 */

export interface ErrorInfo {
  id: string
  message: string
  stack?: string
  type: 'render' | 'network' | 'validation' | 'system' | 'user'
  severity: 'low' | 'medium' | 'high' | 'critical'
  context: Record<string, any>
  timestamp: number
  resolved: boolean
  userId?: string
  sessionId?: string
}

export interface ErrorHandlerConfig {
  enabled: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
  maxErrors: number
  reportToConsole: boolean
  reportToServer: boolean
  serverEndpoint?: string
  enableRecovery: boolean
}

export class KilatErrorHandler {
  private errors: ErrorInfo[] = []
  private config: ErrorHandlerConfig
  private errorCount: Map<string, number> = new Map()

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enabled: true,
      logLevel: 'error',
      maxErrors: 100,
      reportToConsole: true,
      reportToServer: false,
      enableRecovery: true,
      ...config
    }

    this.setupGlobalHandlers()
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalHandlers(): void {
    if (!this.config.enabled) return

    // Handle unhandled promise rejections
    if (typeof process !== 'undefined') {
      process.on('unhandledRejection', (reason, promise) => {
        this.captureError(new Error(`Unhandled Promise Rejection: ${reason}`), {
          type: 'system',
          severity: 'high',
          context: { promise: promise.toString() }
        })
      })

      process.on('uncaughtException', (error) => {
        this.captureError(error, {
          type: 'system',
          severity: 'critical',
          context: { uncaught: true }
        })
      })
    }

    // Handle client-side errors
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.captureError(event.error || new Error(event.message), {
          type: 'system',
          severity: 'high',
          context: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        })
      })

      window.addEventListener('unhandledrejection', (event) => {
        this.captureError(new Error(`Unhandled Promise: ${event.reason}`), {
          type: 'system',
          severity: 'high',
          context: { promise: true }
        })
      })
    }
  }

  /**
   * Capture and process error
   */
  captureError(
    error: Error | string,
    options: Partial<Omit<ErrorInfo, 'id' | 'message' | 'timestamp' | 'resolved'>> = {}
  ): string {
    const errorMessage = typeof error === 'string' ? error : error.message
    const errorStack = typeof error === 'object' ? error.stack : undefined

    const errorInfo: ErrorInfo = {
      id: this.generateErrorId(),
      message: errorMessage,
      stack: errorStack,
      type: options.type || 'system',
      severity: options.severity || 'medium',
      context: options.context || {},
      timestamp: Date.now(),
      resolved: false,
      userId: options.userId,
      sessionId: options.sessionId
    }

    // Add to errors list
    this.errors.push(errorInfo)

    // Keep only recent errors
    if (this.errors.length > this.config.maxErrors) {
      this.errors = this.errors.slice(-this.config.maxErrors)
    }

    // Update error count
    const errorKey = `${errorInfo.type}:${errorMessage}`
    this.errorCount.set(errorKey, (this.errorCount.get(errorKey) || 0) + 1)

    // Report error
    this.reportError(errorInfo)

    // Attempt recovery
    if (this.config.enableRecovery) {
      this.attemptRecovery(errorInfo)
    }

    return errorInfo.id
  }

  /**
   * Report error based on configuration
   */
  private reportError(errorInfo: ErrorInfo): void {
    if (this.config.reportToConsole) {
      this.logToConsole(errorInfo)
    }

    if (this.config.reportToServer && this.config.serverEndpoint) {
      this.reportToServer(errorInfo)
    }
  }

  /**
   * Log error to console
   */
  private logToConsole(errorInfo: ErrorInfo): void {
    const logMethod = this.getLogMethod(errorInfo.severity)
    const prefix = this.getErrorPrefix(errorInfo.type, errorInfo.severity)
    
    logMethod(`${prefix} ${errorInfo.message}`)
    
    if (errorInfo.stack && this.config.logLevel === 'debug') {
      console.log('Stack trace:', errorInfo.stack)
    }
    
    if (Object.keys(errorInfo.context).length > 0) {
      console.log('Context:', errorInfo.context)
    }
  }

  /**
   * Get appropriate console method
   */
  private getLogMethod(severity: ErrorInfo['severity']): typeof console.log {
    switch (severity) {
      case 'critical': return console.error
      case 'high': return console.error
      case 'medium': return console.warn
      case 'low': return console.info
      default: return console.log
    }
  }

  /**
   * Get error prefix for logging
   */
  private getErrorPrefix(type: ErrorInfo['type'], severity: ErrorInfo['severity']): string {
    const typeEmojis = {
      render: '🎨',
      network: '🌐',
      validation: '✅',
      system: '⚙️',
      user: '👤'
    }

    const severityEmojis = {
      critical: '🚨',
      high: '⚠️',
      medium: '⚡',
      low: 'ℹ️'
    }

    return `${severityEmojis[severity]} ${typeEmojis[type]} Kilat.js`
  }

  /**
   * Report error to server
   */
  private async reportToServer(errorInfo: ErrorInfo): Promise<void> {
    if (!this.config.serverEndpoint) return

    try {
      await fetch(this.config.serverEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: errorInfo,
          userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Server',
          url: typeof window !== 'undefined' ? window.location.href : 'Server'
        })
      })
    } catch (reportError) {
      console.warn('Failed to report error to server:', reportError)
    }
  }

  /**
   * Attempt error recovery
   */
  private attemptRecovery(errorInfo: ErrorInfo): void {
    switch (errorInfo.type) {
      case 'render':
        this.recoverFromRenderError(errorInfo)
        break
      case 'network':
        this.recoverFromNetworkError(errorInfo)
        break
      default:
        // Generic recovery
        break
    }
  }

  /**
   * Recover from render errors
   */
  private recoverFromRenderError(errorInfo: ErrorInfo): void {
    // Attempt to re-render component or fallback to error boundary
    console.log('🔄 Attempting render recovery...')
    
    // Mark as resolved if recovery successful
    errorInfo.resolved = true
  }

  /**
   * Recover from network errors
   */
  private recoverFromNetworkError(errorInfo: ErrorInfo): void {
    // Implement retry logic or offline fallback
    console.log('🔄 Attempting network recovery...')
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number
    byType: Record<string, number>
    bySeverity: Record<string, number>
    resolved: number
    recent: number
  } {
    const now = Date.now()
    const oneHour = 60 * 60 * 1000

    const byType: Record<string, number> = {}
    const bySeverity: Record<string, number> = {}
    let resolved = 0
    let recent = 0

    this.errors.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1
      
      if (error.resolved) resolved++
      if (now - error.timestamp < oneHour) recent++
    })

    return {
      total: this.errors.length,
      byType,
      bySeverity,
      resolved,
      recent
    }
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 10): ErrorInfo[] {
    return this.errors
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit)
  }

  /**
   * Clear resolved errors
   */
  clearResolvedErrors(): void {
    this.errors = this.errors.filter(error => !error.resolved)
  }

  /**
   * Export error report
   */
  exportErrorReport(): string {
    return JSON.stringify({
      stats: this.getErrorStats(),
      errors: this.errors,
      config: this.config,
      exportedAt: new Date().toISOString()
    }, null, 2)
  }
}

// Global error handler instance
export const errorHandler = new KilatErrorHandler()

// Error boundary for React components
export function createErrorBoundary(fallbackComponent?: React.ComponentType<{ error: Error }>) {
  return class KilatErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: Error }
  > {
    constructor(props: { children: React.ReactNode }) {
      super(props)
      this.state = { hasError: false }
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error }
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      errorHandler.captureError(error, {
        type: 'render',
        severity: 'high',
        context: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true
        }
      })
    }

    render() {
      if (this.state.hasError) {
        if (fallbackComponent) {
          return React.createElement(fallbackComponent, { error: this.state.error! })
        }
        
        return React.createElement('div', {
          className: 'kilat-error-boundary p-8 text-center'
        }, [
          React.createElement('h2', { key: 'title' }, '⚠️ Something went wrong'),
          React.createElement('p', { key: 'message' }, 'An error occurred while rendering this component.'),
          React.createElement('button', {
            key: 'retry',
            onClick: () => this.setState({ hasError: false, error: undefined }),
            className: 'kilat-btn kilat-btn-primary mt-4'
          }, 'Try Again')
        ])
      }

      return this.props.children
    }
  }
}
