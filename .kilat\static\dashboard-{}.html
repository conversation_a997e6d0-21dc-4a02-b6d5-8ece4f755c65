<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js SpeedRun™ Runtime" />
  
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><div class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-x-hidden"><div class="fixed inset-0 pointer-events-none z-0"><div class="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl kilat-hero-float"></div><div class="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl kilat-hero-pulse"></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"></div></div><header class="relative z-50 kilat-glass border-b border-white/10"><div class="kilat-container"><div class="flex items-center justify-between h-16"><a href="/" class="flex items-center space-x-2 kilat-glow-hover transition-all duration-300"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></a><nav class="hidden md:flex items-center space-x-8"><a href="/" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🛠️ API</a></nav><div class="flex items-center space-x-4"><div class="flex items-center space-x-3"><a href="/login" class="kilat-btn kilat-btn-outline px-4 py-2 text-sm">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary px-4 py-2 text-sm">✨ Register</a></div><button class="md:hidden kilat-btn kilat-btn-outline px-3 py-2"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></div><div class="md:hidden border-t border-white/10 py-4 hidden" id="mobile-menu"><div class="flex flex-col space-y-2"><a href="/" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🛠️ API</a><div class="flex flex-col space-y-2 pt-4 border-t border-white/10"><a href="/login" class="kilat-btn kilat-btn-outline text-center">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary text-center">✨ Register</a></div></div></div></div></header><main class="relative z-10"><div class="min-h-screen bg-gray-900"><div class="flex"><div class="hidden md:flex md:w-64 md:flex-col"><div class="flex flex-col flex-grow pt-5 bg-gray-800 overflow-y-auto"><div class="flex items-center flex-shrink-0 px-4"><span class="text-lg font-semibold text-white">Dashboard</span></div><div class="mt-5 flex-1 flex flex-col"><nav class="flex-1 px-2 pb-4 space-y-1"><a href="/dashboard" class="text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center px-2 py-2 text-sm font-medium rounded-md">📊 Overview</a><a href="/dashboard/analytics" class="text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center px-2 py-2 text-sm font-medium rounded-md">📈 Analytics</a><a href="/dashboard/settings" class="text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center px-2 py-2 text-sm font-medium rounded-md">⚙️ Settings</a></nav></div></div></div><div class="flex flex-col flex-1"><div class="sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow"><div class="flex-1 px-4 flex justify-between"><div class="flex-1 flex"><div class="w-full flex md:ml-0"><div class="relative w-full text-gray-400 focus-within:text-gray-600"><div class="absolute inset-y-0 left-0 flex items-center pointer-events-none"><span class="h-5 w-5">🔍</span></div><input class="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-300 placeholder-gray-400 bg-gray-700 focus:outline-none focus:placeholder-gray-500 focus:ring-0 focus:border-transparent sm:text-sm" placeholder="Search..." type="search"/></div></div></div><div class="ml-4 flex items-center md:ml-6"><div class="ml-3 relative"><div class="max-w-xs bg-gray-800 rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"><span class="sr-only">Open user menu</span><div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center"><span class="text-white text-sm font-medium">U</span></div></div></div></div></div></div><main class="flex-1"><div class="py-6"><div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8"><section class="py-8 border-b border-white/10"><div class="kilat-container"><div class="flex items-center justify-between kilat-fade-in"><div><h1 class="text-3xl md:text-4xl font-bold text-white mb-2">Welcome back, <span class="kilat-gradient-text">John Doe</span>! 👋</h1><p class="text-gray-400">Here&#x27;s what&#x27;s happening with your Kilat.js applications today.</p></div><div class="hidden md:flex items-center space-x-4"><div class="kilat-glass px-4 py-2 rounded-lg"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div><span class="text-sm text-gray-300">All systems operational</span></div></div></div></div></div></section><section class="py-6 border-b border-white/10"><div class="kilat-container"><div class="flex flex-wrap items-center gap-4 kilat-fade-in" style="animation-delay:0.1s"><button class="kilat-btn kilat-btn-primary px-4 py-2 text-sm"><span class="mr-2">➕</span>New Project</button><button class="kilat-btn kilat-btn-outline px-4 py-2 text-sm"><span class="mr-2">📊</span>Analytics</button><button class="kilat-btn kilat-btn-outline px-4 py-2 text-sm"><span class="mr-2">⚙️</span>Settings</button><button class="kilat-btn kilat-btn-outline px-4 py-2 text-sm"><span class="mr-2">📤</span>Export Data</button><div class="ml-auto hidden md:block"><div class="flex items-center space-x-2 text-sm text-gray-400"><span>Last updated:</span><span class="text-white">2 minutes ago</span></div></div></div></div></section><section class="py-8"><div class="kilat-container"><div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-12"><div class="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style="animation-delay:0.1s"><div class="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -mr-10 -mt-10"></div><div class="relative"><div class="flex items-center justify-between mb-4"><div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center"><span class="text-2xl">👥</span></div><div class="text-right"><div class="text-xs text-green-400 font-medium">+12.5%</div></div></div><div><p class="text-sm font-medium text-gray-400 mb-1">Total Users</p><p class="text-3xl font-bold text-white">1,234</p><p class="text-xs text-gray-500 mt-1">vs last month</p></div></div></div><div class="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style="animation-delay:0.2s"><div class="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -mr-10 -mt-10"></div><div class="relative"><div class="flex items-center justify-between mb-4"><div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center"><span class="text-2xl">💰</span></div><div class="text-right"><div class="text-xs text-green-400 font-medium">+23.1%</div></div></div><div><p class="text-sm font-medium text-gray-400 mb-1">Revenue</p><p class="text-3xl font-bold text-white">$12,345</p><p class="text-xs text-gray-500 mt-1">vs last month</p></div></div></div><div class="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style="animation-delay:0.3s"><div class="absolute top-0 right-0 w-20 h-20 bg-yellow-500/10 rounded-full -mr-10 -mt-10"></div><div class="relative"><div class="flex items-center justify-between mb-4"><div class="w-12 h-12 bg-yellow-500/20 rounded-xl flex items-center justify-center"><span class="text-2xl">⚡</span></div><div class="text-right"><div class="text-xs text-green-400 font-medium">+2.1%</div></div></div><div><p class="text-sm font-medium text-gray-400 mb-1">Performance</p><p class="text-3xl font-bold text-white">98.5%</p><p class="text-xs text-gray-500 mt-1">Lighthouse score</p></div></div></div><div class="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style="animation-delay:0.4s"><div class="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -mr-10 -mt-10"></div><div class="relative"><div class="flex items-center justify-between mb-4"><div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center"><span class="text-2xl">🚀</span></div><div class="text-right"><div class="text-xs text-blue-400 font-medium">+3 new</div></div></div><div><p class="text-sm font-medium text-gray-400 mb-1">Active Projects</p><p class="text-3xl font-bold text-white">24</p><p class="text-xs text-gray-500 mt-1">across all teams</p></div></div></div></div><div class="kilat-fade-in" style="animation-delay:0.5s"><div class="kilat-card kilat-glass p-8"><div class="mb-6"><h3 class="text-2xl font-bold text-white mb-2">Recent Activity</h3><p class="text-gray-400">Latest events and updates from your Kilat.js application.</p></div><div class="space-y-4"><div class="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300"><div class="flex-shrink-0"><div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center"><span class="text-green-400">✅</span></div></div><div class="flex-1 min-w-0"><div class="flex items-center justify-between"><p class="text-sm font-medium text-white">New user registration</p><span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-500/20 text-green-400">Success</span></div><p class="text-sm text-gray-400 mt-1">User <EMAIL> registered successfully</p><p class="text-xs text-gray-500 mt-2">2 minutes ago</p></div></div><div class="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300"><div class="flex-shrink-0"><div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center"><span class="text-blue-400">🚀</span></div></div><div class="flex-1 min-w-0"><div class="flex items-center justify-between"><p class="text-sm font-medium text-white">System update deployed</p><span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-500/20 text-blue-400">Info</span></div><p class="text-sm text-gray-400 mt-1">Kilat.js v1.0.0 deployed to production</p><p class="text-xs text-gray-500 mt-2">1 hour ago</p></div></div><div class="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300"><div class="flex-shrink-0"><div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center"><span class="text-purple-400">⚡</span></div></div><div class="flex-1 min-w-0"><div class="flex items-center justify-between"><p class="text-sm font-medium text-white">Performance optimization</p><span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-500/20 text-purple-400">Enhancement</span></div><p class="text-sm text-gray-400 mt-1">SpeedRun™ Runtime optimizations applied</p><p class="text-xs text-gray-500 mt-2">3 hours ago</p></div></div></div></div></div><div class="mt-12 kilat-fade-in" style="animation-delay:0.6s"><h3 class="text-2xl font-bold text-white mb-6">Quick Actions</h3><div class="grid grid-cols-1 md:grid-cols-3 gap-6"><a href="/about" class="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"><div class="text-3xl mb-4">📄</div><h4 class="text-lg font-bold text-white mb-2">Learn More</h4><p class="text-gray-400">Discover Kilat.js features</p></a><a href="/" class="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"><div class="text-3xl mb-4">🏠</div><h4 class="text-lg font-bold text-white mb-2">Homepage</h4><p class="text-gray-400">Back to main page</p></a><a href="/api/users" class="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"><div class="text-3xl mb-4">🛠️</div><h4 class="text-lg font-bold text-white mb-2">API Test</h4><p class="text-gray-400">Test API endpoints</p></a></div></div></div></section></div></div></main></div></div></div></main><footer class="relative z-10 mt-20 border-t border-white/10 kilat-glass"><div class="kilat-container py-12"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="col-span-1 md:col-span-2"><div class="flex items-center space-x-2 mb-4"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></div><p class="text-gray-400 max-w-md mb-6">Modern fullstack framework with SpeedRun™ Runtime. Built for speed, developer experience, and production-ready applications.</p><div class="flex items-center space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">📧</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐙</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐦</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">💬</span></a></div></div><div><h3 class="text-white font-semibold mb-4">Quick Links</h3><ul class="space-y-2"><li><a href="/" class="text-gray-400 hover:text-white transition-colors">🏠 Home</a></li><li><a href="/about" class="text-gray-400 hover:text-white transition-colors">📄 About</a></li><li><a href="/dashboard" class="text-gray-400 hover:text-white transition-colors">📊 Dashboard</a></li><li><a href="/api/users" class="text-gray-400 hover:text-white transition-colors">🛠️ API</a></li><li><a href="/login" class="text-gray-400 hover:text-white transition-colors">🔑 Login</a></li></ul></div><div><h3 class="text-white font-semibold mb-4">Resources</h3><ul class="space-y-2"><li><a href="#" class="text-gray-400 hover:text-white transition-colors">📚 Documentation</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🚀 Getting Started</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">💡 Examples</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🎓 Tutorials</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">👥 Community</a></li></ul></div></div><div class="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row items-center justify-between"><p class="text-gray-400 text-sm">© 2024 Kilat.js. Built with ❤️ using SpeedRun™ Runtime.</p><div class="flex items-center space-x-6 mt-4 md:mt-0"><span class="text-gray-400 text-sm">Powered by</span><div class="flex items-center space-x-4"><div class="flex items-center space-x-2"><span class="text-yellow-400">🟡</span><span class="text-gray-300 text-sm font-medium">Bun.js</span></div><div class="flex items-center space-x-2"><span class="text-blue-400">⚛️</span><span class="text-gray-300 text-sm font-medium">React 18</span></div><div class="flex items-center space-x-2"><span class="text-purple-400">⚡</span><span class="text-gray-300 text-sm font-medium">TypeScript</span></div></div></div></div><div class="mt-8 text-center"><div class="inline-flex items-center px-4 py-2 kilat-glass rounded-full text-sm text-gray-300"><span class="mr-2">⚡</span>SpeedRun™ Runtime - Lightning Fast Performance<span class="ml-2 text-green-400">●</span></div></div></div></footer></div></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>