#!/usr/bin/env bun
/**
 * ⚡ Kilat.js CLI - Command Line Interface
 * Complete DX tooling for Kilat.js framework
 */

import { parseArgs } from 'util'
import { existsSync } from 'fs'
import { join } from 'path'

// CLI Commands
import { devCommand } from './core/tools/commands/dev'
import { buildCommand } from './core/tools/commands/build'
import { startCommand } from './core/tools/commands/start'
import { generateCommand } from './core/tools/commands/generate'
import { graphCommand } from './core/tools/commands/graph'
import { upgradeCommand } from './core/tools/commands/upgrade'
import { exportCommand } from './core/tools/commands/export'
import { testCommand } from './core/tools/commands/test'

const COMMANDS = {
  dev: devCommand,
  build: buildCommand,
  start: startCommand,
  generate: generateCommand,
  graph: graphCommand,
  upgrade: upgradeCommand,
  export: exportCommand,
  test: testCommand,
} as const

type CommandName = keyof typeof COMMANDS

/**
 * Main CLI entry point
 */
async function main() {
  try {
    const args = process.argv.slice(2)
    
    if (args.length === 0) {
      showHelp()
      return
    }
    
    const commandName = args[0] as CommandName
    const commandArgs = args.slice(1)
    
    // Check if command exists
    if (!COMMANDS[commandName]) {
      console.error(`❌ Unknown command: ${commandName}`)
      showHelp()
      process.exit(1)
    }
    
    // Check if we're in a Kilat.js project
    if (!isKilatProject() && commandName !== 'generate') {
      console.error('❌ Not a Kilat.js project. Run this command in a Kilat.js project directory.')
      process.exit(1)
    }
    
    // Execute command
    const command = COMMANDS[commandName]
    await command(commandArgs)
    
  } catch (error) {
    console.error('❌ CLI Error:', error)
    process.exit(1)
  }
}

/**
 * Show CLI help
 */
function showHelp() {
  console.log(`
⚡ Kilat.js CLI - Modern Fullstack Framework

Usage:
  kilat <command> [options]

Commands:
  dev         Start development server with HMR
  build       Build for production
  start       Start production server
  generate    Generate pages, components, APIs
  graph       Show dependency graph visualization
  upgrade     Update Kilat.js framework (OTA)
  export      Export static site (SSG)
  test        Run tests (unit + E2E)

Examples:
  kilat dev                    # Start dev server
  kilat dev --port 4000        # Start dev server on port 4000
  kilat build                  # Build for production
  kilat generate page about    # Generate about page
  kilat generate api users     # Generate users API
  kilat graph                  # Show dependency graph
  kilat upgrade                # Update framework
  kilat test                   # Run all tests
  kilat test --watch           # Run tests in watch mode

Options:
  --help, -h    Show help
  --version, -v Show version

Documentation: https://kilat-js.pcode.my.id
`)
}

/**
 * Check if current directory is a Kilat.js project
 */
function isKilatProject(): boolean {
  const indicators = [
    'kilat.config.ts',
    'kilat.config.js',
    'package.json'
  ]
  
  for (const indicator of indicators) {
    if (existsSync(join(process.cwd(), indicator))) {
      // Additional check for package.json
      if (indicator === 'package.json') {
        try {
          const pkg = require(join(process.cwd(), 'package.json'))
          return pkg.dependencies?.['kilat.js'] || pkg.name === 'kilat.js'
        } catch {
          continue
        }
      }
      return true
    }
  }
  
  return false
}

/**
 * Show version
 */
function showVersion() {
  try {
    const pkg = require('./package.json')
    console.log(`⚡ Kilat.js v${pkg.version}`)
  } catch {
    console.log('⚡ Kilat.js (development)')
  }
}

// Handle version flag
if (process.argv.includes('--version') || process.argv.includes('-v')) {
  showVersion()
  process.exit(0)
}

// Handle help flag
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp()
  process.exit(0)
}

// Run CLI
main().catch(error => {
  console.error('❌ Unexpected error:', error)
  process.exit(1)
})
