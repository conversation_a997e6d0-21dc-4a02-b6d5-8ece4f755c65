{"version": "1.0.0", "buildTime": "2025-07-10T09:46:30.778Z", "routes": ["/about", "/blog", "/dashboard", "/login", "/", "/register", "/blog/getting-started", "/blog/advanced-routing", "/blog/performance-optimization", "/blog/building-with-kilat", "/blog/deployment-guide", "/blog/best-practices"], "config": {"outDir": ".kilat/dist", "staticDir": ".kilat/static", "serverDir": ".kilat/server", "clientDir": ".kilat/client", "mode": "production", "target": "hybrid"}}