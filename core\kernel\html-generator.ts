/**
 * 📄 HTML Document Generator
 */

import type { KilatContext } from '../../types/config'

/**
 * Generate preload tags for current route
 */
function generatePreloadTags(context: KilatContext): string {
  try {
    // Try to import preload system
    const { generatePreloadTags } = require('../systems/render/preload')
    const currentRoute = context.currentRoute || '/'
    return generatePreloadTags(currentRoute)
  } catch (error) {
    // Fallback if preload system is not available
    return ''
  }
}

/**
 * Generate HTML document parts
 */
export function generateHtmlDocument(
  context: KilatContext,
  part: 'start' | 'end'
): string {
  if (part === 'start') {
    return generateDocumentStart(context)
  } else {
    return generateDocumentEnd(context)
  }
}

/**
 * Generate document start (head + opening body)
 */
function generateDocumentStart(context: KilatContext): string {
  const { config, mode } = context
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="generator" content="Kilat.js">
  
  <!-- Kilat.js Meta -->
  <meta name="kilat:version" content="1.0.0">
  <meta name="kilat:mode" content="${mode}">
  <meta name="kilat:engine" content="${config.runtime.engine}">
  
  <!-- SEO Meta -->
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js - Modern Fullstack Framework">
  
  <!-- Open Graph -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="Kilat.js App">
  <meta property="og:description" content="Built with Kilat.js - Modern Fullstack Framework">
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Kilat.js App">
  <meta name="twitter:description" content="Built with Kilat.js - Modern Fullstack Framework">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  
  <!-- Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css">
  <link rel="stylesheet" href="/_kilat/kilat.css">
  ${mode === 'development' ? '<link rel="stylesheet" href="/_kilat/dev.css">' : ''}

  <!-- Critical CSS -->
  <style>
    /* KilatCSS Critical Styles */
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }

    .kilat-fade-in { opacity: 0; animation: fadeIn 0.6s ease-out forwards; }
    .kilat-glow-hover:hover { box-shadow: 0 0 30px rgba(59, 130, 246, 0.3); }
    .kilat-gradient-text {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    .kilat-glass {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
  </style>
  
  <!-- Preload Critical Resources -->
  ${generatePreloadTags(context)}
  <link rel="preload" href="/_kilat/runtime.js" as="script">
  <link rel="preload" href="/_kilat/client.js" as="script">
  
  <!-- Theme -->
  <script>
    // Theme detection and application
    (function() {
      const theme = localStorage.getItem('kilat-theme') || '${config.ui.css.defaultTheme}';
      document.documentElement.setAttribute('data-theme', theme);
      document.documentElement.classList.add('theme-' + theme);
    })();
  </script>
  
  ${mode === 'development' ? generateDevScripts() : ''}
</head>
<body>
  <div id="__kilat">`
}

/**
 * Generate document end (closing body + scripts)
 */
function generateDocumentEnd(context: KilatContext): string {
  const { mode } = context
  
  return `  </div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js"></script>
  <script src="/_kilat/client.js"></script>
  
  ${mode === 'development' ? generateDevEndScripts() : ''}
  
  <!-- Analytics -->
  ${mode === 'production' ? generateAnalyticsScript() : ''}
  
</body>
</html>`
}

/**
 * Generate development scripts
 */
function generateDevScripts(): string {
  return `
  <!-- Development Scripts -->
  <script>
    // Development mode indicator
    window.__KILAT_DEV__ = true;
    
    // Error overlay
    window.__KILAT_ERROR_OVERLAY__ = true;
    
    // HMR client
    window.__KILAT_HMR__ = true;
  </script>
  
  <!-- Error Overlay Styles -->
  <style>
    .kilat-error-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.9);
      color: #ff6b6b;
      font-family: 'JetBrains Mono', monospace;
      z-index: 9999;
      padding: 20px;
      overflow: auto;
    }
    
    .kilat-error-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #ff6b6b;
    }
    
    .kilat-error-message {
      font-size: 16px;
      margin-bottom: 20px;
      color: #fff;
    }
    
    .kilat-error-stack {
      background: #1a1a1a;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      white-space: pre-wrap;
      font-size: 14px;
      line-height: 1.5;
    }
  </style>`
}

/**
 * Generate development end scripts
 */
function generateDevEndScripts(): string {
  return `
  <!-- HMR Client -->
  <script>
    // Hot Module Replacement client
    if (window.__KILAT_HMR__) {
      const ws = new WebSocket('ws://localhost:3001');
      
      ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        if (data.type === 'reload') {
          window.location.reload();
        } else if (data.type === 'error') {
          showErrorOverlay(data.error);
        }
      };
      
      function showErrorOverlay(error) {
        const overlay = document.createElement('div');
        overlay.className = 'kilat-error-overlay';
        overlay.innerHTML = \`
          <div class="kilat-error-title">⚡ Kilat.js Development Error</div>
          <div class="kilat-error-message">\${error.message}</div>
          <div class="kilat-error-stack">\${error.stack}</div>
        \`;
        
        overlay.onclick = function() {
          document.body.removeChild(overlay);
        };
        
        document.body.appendChild(overlay);
      }
    }
  </script>`
}

/**
 * Generate analytics script
 */
function generateAnalyticsScript(): string {
  return `
  <!-- Kilat.js Analytics -->
  <script>
    // Built-in analytics for production
    (function() {
      const analytics = {
        track: function(event, data) {
          // Send analytics data
          fetch('/_kilat/analytics', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ event, data, timestamp: Date.now() })
          }).catch(() => {});
        }
      };
      
      // Track page view
      analytics.track('page_view', {
        url: window.location.href,
        referrer: document.referrer,
        userAgent: navigator.userAgent
      });
      
      // Track performance
      window.addEventListener('load', function() {
        const perfData = performance.getEntriesByType('navigation')[0];
        analytics.track('performance', {
          loadTime: perfData.loadEventEnd - perfData.loadEventStart,
          domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
          firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
        });
      });
      
      window.__KILAT_ANALYTICS__ = analytics;
    })();
  </script>`
}
