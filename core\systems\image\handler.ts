/**
 * 🖼️ Image Handler - API Handler for Image Optimization
 */

import type { KilatContext } from '../../../types/config'
import { getImageOptimizer, type ImageOptions } from './optimizer'

/**
 * Handle image optimization request
 */
export async function handleImageRequest(request: Request, context: KilatContext): Promise<Response> {
  try {
    const url = new URL(request.url)
    
    // Get source image path
    const src = url.searchParams.get('src')
    if (!src) {
      return new Response('Missing src parameter', { status: 400 })
    }
    
    // Parse options
    const options: Partial<ImageOptions> = {
      // Format
      format: parseFormat(url.searchParams.get('fm')),
      quality: parseQuality(url.searchParams.get('q')),
      
      // Size
      width: parseNumber(url.searchParams.get('w')),
      height: parseNumber(url.searchParams.get('h')),
      fit: parseFit(url.searchParams.get('fit')),
      
      // Effects
      blur: parseNumber(url.searchParams.get('blur')),
      sharpen: parseNumber(url.searchParams.get('sharpen')),
      grayscale: url.searchParams.get('grayscale') === 'true',
      rotate: parseNumber(url.searchParams.get('rotate')),
      
      // Cache
      cache: url.searchParams.get('cache') !== 'false',
      cacheMaxAge: parseNumber(url.searchParams.get('maxage')) || 86400 * 7, // 7 days default
    }
    
    // Get image optimizer
    const imageOptimizer = getImageOptimizer()
    if (!imageOptimizer) {
      return new Response('Image optimizer not initialized', { status: 500 })
    }
    
    // Optimize image
    const optimizedImage = await imageOptimizer.optimize(src, options)
    
    // Set appropriate content type
    const contentType = getContentType(optimizedImage.metadata.format)
    
    // Set cache control headers
    const cacheControl = options.cache
      ? `public, max-age=${options.cacheMaxAge}, stale-while-revalidate=86400`
      : 'no-store, must-revalidate'
    
    // Return optimized image
    return new Response(optimizedImage.buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Length': optimizedImage.metadata.size.toString(),
        'Cache-Control': cacheControl,
        'Etag': `"${optimizedImage.cacheKey}"`,
        'X-Kilat-Image': 'Optimized',
        'X-Kilat-Image-Size': `${optimizedImage.metadata.width}x${optimizedImage.metadata.height}`,
        'X-Kilat-Image-Format': optimizedImage.metadata.format,
      }
    })
    
  } catch (error) {
    console.error('❌ Image handler error:', error)
    
    return new Response('Image processing error', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-store'
      }
    })
  }
}

/**
 * Parse image format
 */
function parseFormat(value: string | null): 'jpeg' | 'png' | 'webp' | 'avif' | 'original' {
  if (!value) return 'webp' // Default to WebP
  
  switch (value.toLowerCase()) {
    case 'jpg':
    case 'jpeg':
      return 'jpeg'
    case 'png':
      return 'png'
    case 'webp':
      return 'webp'
    case 'avif':
      return 'avif'
    case 'original':
      return 'original'
    default:
      return 'webp'
  }
}

/**
 * Parse image quality
 */
function parseQuality(value: string | null): number {
  if (!value) return 80 // Default quality
  
  const quality = parseInt(value, 10)
  return Math.min(Math.max(quality, 1), 100) // Clamp between 1-100
}

/**
 * Parse number value
 */
function parseNumber(value: string | null): number | undefined {
  if (!value) return undefined
  
  const num = parseInt(value, 10)
  return isNaN(num) ? undefined : num
}

/**
 * Parse fit mode
 */
function parseFit(value: string | null): 'cover' | 'contain' | 'fill' | 'inside' | 'outside' | undefined {
  if (!value) return undefined
  
  switch (value.toLowerCase()) {
    case 'cover':
      return 'cover'
    case 'contain':
      return 'contain'
    case 'fill':
      return 'fill'
    case 'inside':
      return 'inside'
    case 'outside':
      return 'outside'
    default:
      return undefined
  }
}

/**
 * Get content type for image format
 */
function getContentType(format: string): string {
  switch (format.toLowerCase()) {
    case 'jpeg':
    case 'jpg':
      return 'image/jpeg'
    case 'png':
      return 'image/png'
    case 'webp':
      return 'image/webp'
    case 'avif':
      return 'image/avif'
    case 'gif':
      return 'image/gif'
    case 'svg':
      return 'image/svg+xml'
    default:
      return 'application/octet-stream'
  }
}
