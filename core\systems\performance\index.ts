/**
 * 📊 Kilat.js Advanced Performance Monitoring
 * Real-time performance tracking and optimization
 */

export interface PerformanceMetrics {
  renderTime: number
  bundleSize: number
  memoryUsage: number
  cacheHitRate: number
  requestCount: number
  errorRate: number
  timestamp: number
}

export interface PerformanceConfig {
  enabled: boolean
  sampleRate: number
  maxMetrics: number
  alertThresholds: {
    renderTime: number
    memoryUsage: number
    errorRate: number
  }
}

export class KilatPerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private config: PerformanceConfig
  private startTime: number = Date.now()

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enabled: true,
      sampleRate: 1.0,
      maxMetrics: 1000,
      alertThresholds: {
        renderTime: 100, // ms
        memoryUsage: 100, // MB
        errorRate: 0.05 // 5%
      },
      ...config
    }
  }

  /**
   * Record performance metric
   */
  recordMetric(metric: Partial<PerformanceMetrics>): void {
    if (!this.config.enabled || Math.random() > this.config.sampleRate) {
      return
    }

    const fullMetric: PerformanceMetrics = {
      renderTime: 0,
      bundleSize: 0,
      memoryUsage: this.getMemoryUsage(),
      cacheHitRate: 0,
      requestCount: 0,
      errorRate: 0,
      timestamp: Date.now(),
      ...metric
    }

    this.metrics.push(fullMetric)

    // Keep only recent metrics
    if (this.metrics.length > this.config.maxMetrics) {
      this.metrics = this.metrics.slice(-this.config.maxMetrics)
    }

    // Check for alerts
    this.checkAlerts(fullMetric)
  }

  /**
   * Record render performance
   */
  recordRender(startTime: number, endTime: number): void {
    const renderTime = endTime - startTime
    this.recordMetric({ renderTime })
  }

  /**
   * Record request
   */
  recordRequest(success: boolean = true): void {
    const recentMetrics = this.getRecentMetrics(60000) // Last minute
    const totalRequests = recentMetrics.reduce((sum, m) => sum + m.requestCount, 0) + 1
    const errors = recentMetrics.filter(m => m.errorRate > 0).length + (success ? 0 : 1)
    const errorRate = errors / totalRequests

    this.recordMetric({ 
      requestCount: 1,
      errorRate: success ? 0 : errorRate
    })
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed / 1024 / 1024 // MB
    }
    return 0
  }

  /**
   * Get recent metrics
   */
  private getRecentMetrics(timeWindow: number): PerformanceMetrics[] {
    const cutoff = Date.now() - timeWindow
    return this.metrics.filter(m => m.timestamp > cutoff)
  }

  /**
   * Check for performance alerts
   */
  private checkAlerts(metric: PerformanceMetrics): void {
    const { alertThresholds } = this.config

    if (metric.renderTime > alertThresholds.renderTime) {
      console.warn(`⚠️ Slow render detected: ${metric.renderTime}ms`)
    }

    if (metric.memoryUsage > alertThresholds.memoryUsage) {
      console.warn(`⚠️ High memory usage: ${metric.memoryUsage.toFixed(2)}MB`)
    }

    if (metric.errorRate > alertThresholds.errorRate) {
      console.warn(`⚠️ High error rate: ${(metric.errorRate * 100).toFixed(2)}%`)
    }
  }

  /**
   * Get performance summary
   */
  getSummary(): {
    averageRenderTime: number
    totalRequests: number
    errorRate: number
    memoryUsage: number
    uptime: number
  } {
    const recentMetrics = this.getRecentMetrics(300000) // Last 5 minutes

    if (recentMetrics.length === 0) {
      return {
        averageRenderTime: 0,
        totalRequests: 0,
        errorRate: 0,
        memoryUsage: this.getMemoryUsage(),
        uptime: Date.now() - this.startTime
      }
    }

    const totalRenderTime = recentMetrics.reduce((sum, m) => sum + m.renderTime, 0)
    const totalRequests = recentMetrics.reduce((sum, m) => sum + m.requestCount, 0)
    const totalErrors = recentMetrics.filter(m => m.errorRate > 0).length

    return {
      averageRenderTime: totalRenderTime / recentMetrics.length,
      totalRequests,
      errorRate: totalErrors / recentMetrics.length,
      memoryUsage: this.getMemoryUsage(),
      uptime: Date.now() - this.startTime
    }
  }

  /**
   * Get detailed metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics]
  }

  /**
   * Clear metrics
   */
  clearMetrics(): void {
    this.metrics = []
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    return JSON.stringify({
      config: this.config,
      metrics: this.metrics,
      summary: this.getSummary(),
      exportedAt: new Date().toISOString()
    }, null, 2)
  }
}

// Global performance monitor instance
export const performanceMonitor = new KilatPerformanceMonitor()

// Performance middleware for server
export function performanceMiddleware() {
  return async (context: any, next: () => Promise<void>) => {
    const startTime = Date.now()
    
    try {
      await next()
      performanceMonitor.recordRequest(true)
    } catch (error) {
      performanceMonitor.recordRequest(false)
      throw error
    } finally {
      const endTime = Date.now()
      performanceMonitor.recordRender(startTime, endTime)
    }
  }
}

// Client-side performance tracking
export function initClientPerformance(): void {
  if (typeof window === 'undefined') return

  // Track page load performance
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      performanceMonitor.recordMetric({
        renderTime: navigation.loadEventEnd - navigation.loadEventStart,
        timestamp: Date.now()
      })
    }
  })

  // Track resource loading
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'measure') {
        performanceMonitor.recordMetric({
          renderTime: entry.duration,
          timestamp: Date.now()
        })
      }
    }
  })

  observer.observe({ entryTypes: ['measure', 'navigation'] })
}

// Performance utilities
export const PerformanceUtils = {
  /**
   * Measure function execution time
   */
  measure: async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
    const startTime = Date.now()
    try {
      const result = await fn()
      const endTime = Date.now()
      performanceMonitor.recordRender(startTime, endTime)
      return result
    } catch (error) {
      performanceMonitor.recordRequest(false)
      throw error
    }
  },

  /**
   * Create performance mark
   */
  mark: (name: string): void => {
    if (typeof performance !== 'undefined') {
      performance.mark(name)
    }
  },

  /**
   * Measure between marks
   */
  measureBetween: (name: string, startMark: string, endMark: string): void => {
    if (typeof performance !== 'undefined') {
      performance.measure(name, startMark, endMark)
    }
  }
}
