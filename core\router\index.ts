/**
 * 🚀 Kilat.js Advanced Router System
 * Next.js App Router-like file-based routing with SSG, SSR, CSR support
 */

import { join, dirname, basename, extname } from 'path'
import { existsSync, readdirSync, statSync } from 'fs'
import type { KilatContext, RouteConfig, RenderMode, RouteParams } from '../types'

export interface RouteSegment {
  path: string
  isDynamic: boolean
  isOptional: boolean
  isCatchAll: boolean
  paramName?: string
}

export interface RouteMatch {
  route: RouteConfig
  params: RouteParams
  segments: RouteSegment[]
}

export interface LayoutChain {
  path: string
  component: string
  level: number
}

export class KilatRouter {
  private routes: Map<string, RouteConfig> = new Map()
  private layouts: Map<string, string> = new Map()
  private middleware: Map<string, string> = new Map()
  private staticRoutes: Set<string> = new Set()
  private dynamicRoutes: RouteConfig[] = []

  constructor(private appsDir: string = 'apps') {
    this.scanRoutes()
  }

  /**
   * Scan and register all routes from apps directory
   */
  private scanRoutes(): void {
    console.log('🔍 Scanning routes...')
    this.scanDirectory(this.appsDir, '')
    console.log(`✅ Found ${this.routes.size} routes, ${this.layouts.size} layouts`)
  }

  /**
   * Recursively scan directory for route files
   */
  private scanDirectory(dir: string, routePath: string): void {
    if (!existsSync(dir)) return

    const entries = readdirSync(dir)
    
    for (const entry of entries) {
      const fullPath = join(dir, entry)
      const stat = statSync(fullPath)

      if (stat.isDirectory()) {
        // Handle dynamic routes: [param], [...param], [[...param]]
        const dynamicMatch = entry.match(/^(\[{1,2})([^[\]]+)(\]{1,2})$/)
        let segmentPath = routePath

        if (dynamicMatch) {
          const [, openBrackets, paramName, closeBrackets] = dynamicMatch
          const isDynamic = true
          const isOptional = openBrackets === '[[' && closeBrackets === ']]'
          const isCatchAll = paramName.startsWith('...')
          
          segmentPath = routePath + (isCatchAll ? '/*' : '/:' + paramName.replace('...', ''))
        } else {
          segmentPath = routePath + '/' + entry
        }

        // Scan subdirectory
        this.scanDirectory(fullPath, segmentPath)
      } else if (stat.isFile()) {
        this.processRouteFile(fullPath, routePath, entry)
      }
    }
  }

  /**
   * Process individual route files
   */
  private processRouteFile(filePath: string, routePath: string, fileName: string): void {
    const ext = extname(fileName)
    const baseName = basename(fileName, ext)

    if (!['.tsx', '.ts', '.jsx', '.js'].includes(ext)) return

    switch (baseName) {
      case 'page':
        this.registerPage(filePath, routePath)
        break
      case 'layout':
        this.registerLayout(filePath, routePath)
        break
      case 'middleware':
        this.registerMiddleware(filePath, routePath)
        break
      case 'loading':
      case 'error':
      case 'not-found':
        this.registerSpecialFile(filePath, routePath, baseName)
        break
      case 'route':
        this.registerApiRoute(filePath, routePath)
        break
      case 'generate':
        this.registerStaticGeneration(filePath, routePath)
        break
    }
  }

  /**
   * Register page component
   */
  private registerPage(filePath: string, routePath: string): void {
    const normalizedPath = routePath || '/'
    const route: RouteConfig = {
      path: normalizedPath,
      component: filePath,
      type: 'page',
      renderMode: this.detectRenderMode(filePath),
      isDynamic: this.isDynamicRoute(normalizedPath),
      params: this.extractParams(normalizedPath)
    }

    if (route.isDynamic) {
      this.dynamicRoutes.push(route)
    } else {
      this.routes.set(normalizedPath, route)
      this.staticRoutes.add(normalizedPath)
    }

    console.log(`📄 Page: ${normalizedPath} (${route.renderMode})`)
  }

  /**
   * Register layout component
   */
  private registerLayout(filePath: string, routePath: string): void {
    const normalizedPath = routePath || '/'
    this.layouts.set(normalizedPath, filePath)
    console.log(`🏗️ Layout: ${normalizedPath}`)
  }

  /**
   * Register middleware
   */
  private registerMiddleware(filePath: string, routePath: string): void {
    const normalizedPath = routePath || '/'
    this.middleware.set(normalizedPath, filePath)
    console.log(`🛡️ Middleware: ${normalizedPath}`)
  }

  /**
   * Register special files (loading, error, not-found)
   */
  private registerSpecialFile(filePath: string, routePath: string, type: string): void {
    const normalizedPath = routePath || '/'
    const route: RouteConfig = {
      path: normalizedPath,
      component: filePath,
      type: type as any,
      renderMode: 'csr',
      isDynamic: false,
      params: []
    }
    
    this.routes.set(`${normalizedPath}/${type}`, route)
    console.log(`🎭 ${type}: ${normalizedPath}`)
  }

  /**
   * Register API route
   */
  private registerApiRoute(filePath: string, routePath: string): void {
    const normalizedPath = routePath || '/'
    const route: RouteConfig = {
      path: normalizedPath,
      component: filePath,
      type: 'api',
      renderMode: 'ssr',
      isDynamic: this.isDynamicRoute(normalizedPath),
      params: this.extractParams(normalizedPath)
    }

    if (route.isDynamic) {
      this.dynamicRoutes.push(route)
    } else {
      this.routes.set(normalizedPath, route)
    }

    console.log(`🛠️ API: ${normalizedPath}`)
  }

  /**
   * Register static generation config
   */
  private registerStaticGeneration(filePath: string, routePath: string): void {
    const normalizedPath = routePath || '/'
    this.staticRoutes.add(normalizedPath)
    console.log(`⚡ SSG: ${normalizedPath}`)
  }

  /**
   * Detect render mode from file content or conventions
   */
  private detectRenderMode(filePath: string): RenderMode {
    // Check for generate.ts file in same directory
    const dir = dirname(filePath)
    if (existsSync(join(dir, 'generate.ts')) || existsSync(join(dir, 'generate.js'))) {
      return 'ssg'
    }

    // Default to SSR for server-side rendering
    return 'ssr'
  }

  /**
   * Check if route has dynamic segments
   */
  private isDynamicRoute(path: string): boolean {
    return path.includes(':') || path.includes('*')
  }

  /**
   * Extract parameter names from route path
   */
  private extractParams(path: string): string[] {
    const params: string[] = []
    const segments = path.split('/')
    
    for (const segment of segments) {
      if (segment.startsWith(':')) {
        params.push(segment.slice(1))
      } else if (segment === '*') {
        params.push('catchAll')
      }
    }
    
    return params
  }

  /**
   * Match incoming request to route
   */
  public matchRoute(pathname: string): RouteMatch | null {
    // Try exact match first
    const exactRoute = this.routes.get(pathname)
    if (exactRoute) {
      return {
        route: exactRoute,
        params: {},
        segments: this.parseSegments(pathname)
      }
    }

    // Try dynamic routes
    for (const route of this.dynamicRoutes) {
      const match = this.matchDynamicRoute(pathname, route)
      if (match) {
        return match
      }
    }

    return null
  }

  /**
   * Match dynamic route patterns
   */
  private matchDynamicRoute(pathname: string, route: RouteConfig): RouteMatch | null {
    const pathSegments = pathname.split('/').filter(Boolean)
    const routeSegments = route.path.split('/').filter(Boolean)
    const params: RouteParams = {}

    if (pathSegments.length !== routeSegments.length) {
      // Handle catch-all routes
      if (!route.path.includes('*')) {
        return null
      }
    }

    for (let i = 0; i < routeSegments.length; i++) {
      const routeSegment = routeSegments[i]
      const pathSegment = pathSegments[i]

      if (routeSegment.startsWith(':')) {
        // Dynamic parameter
        const paramName = routeSegment.slice(1)
        params[paramName] = pathSegment
      } else if (routeSegment === '*') {
        // Catch-all
        params.catchAll = pathSegments.slice(i).join('/')
        break
      } else if (routeSegment !== pathSegment) {
        return null
      }
    }

    return {
      route,
      params,
      segments: this.parseSegments(pathname)
    }
  }

  /**
   * Parse URL segments
   */
  private parseSegments(pathname: string): RouteSegment[] {
    return pathname.split('/').filter(Boolean).map(segment => ({
      path: segment,
      isDynamic: false,
      isOptional: false,
      isCatchAll: false
    }))
  }

  /**
   * Get layout chain for a route
   */
  public getLayoutChain(pathname: string): LayoutChain[] {
    const layouts: LayoutChain[] = []
    const segments = pathname.split('/').filter(Boolean)
    
    // Add root layout
    const rootLayout = this.layouts.get('/')
    if (rootLayout) {
      layouts.push({
        path: '/',
        component: rootLayout,
        level: 0
      })
    }

    // Add nested layouts
    let currentPath = ''
    for (let i = 0; i < segments.length; i++) {
      currentPath += '/' + segments[i]
      const layout = this.layouts.get(currentPath)
      if (layout) {
        layouts.push({
          path: currentPath,
          component: layout,
          level: i + 1
        })
      }
    }

    return layouts
  }

  /**
   * Get all static routes for build
   */
  public getStaticRoutes(): string[] {
    return Array.from(this.staticRoutes)
  }

  /**
   * Get all routes
   */
  public getAllRoutes(): RouteConfig[] {
    return Array.from(this.routes.values()).concat(this.dynamicRoutes)
  }

  /**
   * Get middleware for route
   */
  public getMiddleware(pathname: string): string[] {
    const middlewares: string[] = []
    const segments = pathname.split('/').filter(Boolean)
    
    // Check for middleware at each level
    let currentPath = ''
    for (const segment of segments) {
      currentPath += '/' + segment
      const middleware = this.middleware.get(currentPath)
      if (middleware) {
        middlewares.push(middleware)
      }
    }

    return middlewares
  }
}

// Export singleton instance
export const router = new KilatRouter()
