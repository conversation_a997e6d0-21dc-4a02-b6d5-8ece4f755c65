<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Blog | Kilat.js Framework</title>
  <meta name="description" content="Tutorials, guides, and insights about building lightning-fast web applications with Kilat.js framework and SpeedRun™ Runtime." />
  <meta name="keywords" content="kilat.js, blog, tutorials, web development, performance" />
  <meta property="og:title" content="Kilat.js Blog - Tutorials & Guides" />
  <meta property="og:description" content="Learn how to build lightning-fast web applications with Kilat.js framework." />
  <meta property="og:image" content="/api/og?title=Kilat.js Blog" />
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><section class="py-20"><div class="kilat-container"><div class="text-center mb-16 kilat-fade-in"><h1 class="text-4xl md:text-5xl font-bold text-white mb-6">Kilat.js <span class="kilat-gradient-text">Blog</span></h1><p class="text-xl text-gray-300 max-w-3xl mx-auto">Tutorials, guides, and insights about building lightning-fast web applications with Kilat.js framework and SpeedRun™ Runtime.</p></div><div class="mb-16 kilat-fade-in" style="animation-delay:0.2s"><div class="kilat-card kilat-glass p-8 kilat-glow-hover"><div class="flex items-center space-x-2 mb-4"><span class="px-3 py-1 bg-blue-500/20 text-blue-400 text-sm rounded-full">Featured</span><span class="text-gray-400 text-sm">5 min read</span></div><h2 class="text-3xl font-bold text-white mb-4"><a href="/blog/getting-started" class="hover:kilat-gradient-text transition-all duration-300">Getting Started with Kilat.js</a></h2><p class="text-gray-300 text-lg mb-6 leading-relaxed">Learn how to build lightning-fast applications with Kilat.js framework and SpeedRun™ Runtime.</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4 text-gray-400"><span>By <!-- -->Kilat Team</span><span>•</span><span>1/25/2024</span></div><a href="/blog/getting-started" class="kilat-btn kilat-btn-primary px-6 py-3 kilat-glow-hover">Read More →</a></div></div></div></div></section><section class="pb-20"><div class="kilat-container"><h2 class="text-2xl font-bold text-white mb-8 kilat-fade-in">Latest Posts</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><article class="kilat-card kilat-glass p-6 kilat-glow-hover kilat-fade-in" style="animation-delay:0.4s"><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">routing</span><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">advanced</span></div><h3 class="text-xl font-bold text-white mb-3"><a href="/blog/advanced-routing" class="hover:kilat-gradient-text transition-all duration-300">Advanced Routing in Kilat.js</a></h3><p class="text-gray-300 mb-4 leading-relaxed">Explore the powerful routing system with SSG, SSR, and CSR support for modern web applications.</p><div class="flex items-center justify-between text-sm text-gray-400"><div class="flex items-center space-x-2"><span>Kilat Team</span><span>•</span><span>1/20/2024</span></div><span>8 min read</span></div><div class="mt-4 pt-4 border-t border-white/10"><a href="/blog/advanced-routing" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">Read more →</a></div></article><article class="kilat-card kilat-glass p-6 kilat-glow-hover kilat-fade-in" style="animation-delay:0.5s"><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">performance</span><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">optimization</span></div><h3 class="text-xl font-bold text-white mb-3"><a href="/blog/performance-optimization" class="hover:kilat-gradient-text transition-all duration-300">Performance Optimization with SpeedRun™</a></h3><p class="text-gray-300 mb-4 leading-relaxed">Discover how SpeedRun™ Runtime delivers unmatched performance and developer experience.</p><div class="flex items-center justify-between text-sm text-gray-400"><div class="flex items-center space-x-2"><span>Kilat Team</span><span>•</span><span>1/15/2024</span></div><span>6 min read</span></div><div class="mt-4 pt-4 border-t border-white/10"><a href="/blog/performance-optimization" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">Read more →</a></div></article></div></div></section><section class="py-16"><div class="kilat-container"><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in"><h2 class="text-3xl font-bold text-white mb-4">Stay Updated</h2><p class="text-gray-300 mb-8 max-w-2xl mx-auto">Get the latest tutorials, guides, and updates about Kilat.js framework delivered straight to your inbox.</p><form class="max-w-md mx-auto flex gap-4"><input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"/><button type="submit" class="kilat-btn kilat-btn-primary px-6 py-3 kilat-glow-hover">Subscribe</button></form></div></div></section></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>