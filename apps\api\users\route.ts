/**
 * 👥 Users API Route
 */

import { ApiResponse } from '../../../core/kernel/api-handler'

// Mock data
const users = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'admin' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'user' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'user' },
]

/**
 * GET /api/users - Get all users
 */
export async function GET(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const offset = parseInt(url.searchParams.get('offset') || '0')
    
    const paginatedUsers = users.slice(offset, offset + limit)
    
    return ApiResponse.json({
      users: paginatedUsers,
      total: users.length,
      limit,
      offset
    })
  } catch (error) {
    return ApiResponse.error('Failed to fetch users', 500)
  }
}

/**
 * POST /api/users - Create new user
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.name || !body.email) {
      return ApiResponse.error('Name and email are required', 400)
    }
    
    // Check if email already exists
    const existingUser = users.find(user => user.email === body.email)
    if (existingUser) {
      return ApiResponse.error('Email already exists', 409)
    }
    
    // Create new user
    const newUser = {
      id: users.length + 1,
      name: body.name,
      email: body.email,
      role: body.role || 'user'
    }
    
    users.push(newUser)
    
    return ApiResponse.json(newUser, 201)
  } catch (error) {
    return ApiResponse.error('Failed to create user', 500)
  }
}

/**
 * PUT /api/users - Update user
 */
export async function PUT(request: Request): Promise<Response> {
  try {
    const body = await request.json()
    
    if (!body.id) {
      return ApiResponse.error('User ID is required', 400)
    }
    
    const userIndex = users.findIndex(user => user.id === body.id)
    if (userIndex === -1) {
      return ApiResponse.error('User not found', 404)
    }
    
    // Update user
    users[userIndex] = { ...users[userIndex], ...body }
    
    return ApiResponse.json(users[userIndex])
  } catch (error) {
    return ApiResponse.error('Failed to update user', 500)
  }
}

/**
 * DELETE /api/users - Delete user
 */
export async function DELETE(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url)
    const id = parseInt(url.searchParams.get('id') || '0')
    
    if (!id) {
      return ApiResponse.error('User ID is required', 400)
    }
    
    const userIndex = users.findIndex(user => user.id === id)
    if (userIndex === -1) {
      return ApiResponse.error('User not found', 404)
    }
    
    // Remove user
    const deletedUser = users.splice(userIndex, 1)[0]
    
    return ApiResponse.json({ message: 'User deleted successfully', user: deletedUser })
  } catch (error) {
    return ApiResponse.error('Failed to delete user', 500)
  }
}
