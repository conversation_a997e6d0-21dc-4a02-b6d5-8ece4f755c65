
/**
 * 💧 Kilat.js Client Hydration
 */
(function() {
  'use strict';

  // Wait for DOM and Kilat runtime
  function initHydration() {
    if (!window.Kilat || !window.React) {
      setTimeout(initHydration, 50);
      return;
    }

    const data = window.__KILAT_DATA__;
    if (!data) {
      console.warn('No hydration data found');
      return;
    }

    // Load and render page component
    loadPageComponent(data.route, data.params)
      .then(Component => {
        if (Component) {
          hydrateComponent(Component, data.params);
        }
      })
      .catch(error => {
        console.error('Hydration failed:', error);
        showError(error);
      });
  }

  async function loadPageComponent(route, params) {
    try {
      // Dynamic import based on route
      const componentPath = getComponentPath(route);
      const module = await import(componentPath);
      return module.default || module;
    } catch (error) {
      console.error('Failed to load component:', error);
      return null;
    }
  }

  function hydrateComponent(Component, params) {
    const container = document.getElementById('__kilat');
    if (!container) return;

    // Create React element
    const element = React.createElement(Component, { params });
    
    // Hydrate
    if (window.ReactDOM && window.ReactDOM.hydrate) {
      window.ReactDOM.hydrate(element, container);
    } else {
      // Fallback to render
      container.innerHTML = '';
      window.ReactDOM.render(element, container);
    }

    console.log('💧 Hydration complete');
  }

  function getComponentPath(route) {
    // Map route to component path
    const routeMap = window.__KILAT_ROUTES__ || {};
    return routeMap[route] || '/apps' + route + '/page.js';
  }

  function showError(error) {
    const container = document.getElementById('__kilat');
    if (container) {
      container.innerHTML = `
        <div class="min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-purple-900 flex items-center justify-center">
          <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">Hydration Error</h1>
            <p class="text-gray-300">${error.message}</p>
          </div>
        </div>
      `;
    }
  }

  // Start hydration
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initHydration);
  } else {
    initHydration();
  }
})();
