<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js SpeedRun™ Runtime" />
  
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><section class="relative py-20 sm:py-32"><div class="kilat-container"><div class="mx-auto max-w-4xl text-center"><div class="kilat-fade-in mb-8"><div class="inline-flex items-center px-4 py-2 rounded-full text-sm kilat-glass text-gray-300 hover:bg-white/20 transition-all duration-300 kilat-glow-hover"><span class="mr-2">⚡</span>Powered by SpeedRun™ Runtime<a href="/about" class="ml-2 text-blue-400 hover:text-blue-300">Learn more →</a></div></div><div class="kilat-fade-in" style="animation-delay:0.2s"><h1 class="text-5xl md:text-7xl font-bold tracking-tight text-white mb-6">Build<!-- --> <span class="kilat-gradient-text kilat-text-glow">Lightning-Fast</span><br/>Apps with Kilat.js</h1><p class="text-xl md:text-2xl leading-relaxed text-gray-300 max-w-3xl mx-auto mb-10">Modern fullstack framework with SpeedRun™ Runtime, file-based routing, streaming SSR, and built-in optimizations. Zero configuration, maximum performance.</p><div class="flex flex-col sm:flex-row items-center justify-center gap-4 kilat-fade-in" style="animation-delay:0.4s"><a href="/dashboard" class="kilat-btn kilat-btn-primary px-8 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300"><span class="mr-2">🚀</span>Get Started</a><a href="/about" class="kilat-btn kilat-btn-outline px-8 py-4 text-lg font-semibold hover:kilat-glow transition-all duration-300">Learn More<span class="ml-2">→</span></a></div></div></div></div></section><div class="py-24 sm:py-32 relative"><div class="kilat-container"><div class="text-center mb-20 kilat-fade-in"><h2 class="text-4xl md:text-5xl font-bold tracking-tight text-white mb-6">Everything you need to build<!-- --> <span class="kilat-gradient-text">modern apps</span></h2><p class="text-xl leading-8 text-gray-300 max-w-2xl mx-auto">Kilat.js provides all the tools and optimizations you need out of the box.</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto"><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.1s"><div class="text-4xl mb-4">⚡</div><h3 class="text-xl font-bold text-white mb-4">SpeedRun™ Runtime</h3><p class="text-gray-300 leading-relaxed">Lightning-fast runtime powered by Bun.js with automatic optimizations and zero cold starts.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.2s"><div class="text-4xl mb-4">🗂️</div><h3 class="text-xl font-bold text-white mb-4">Apps Mapping</h3><p class="text-gray-300 leading-relaxed">File-based routing system similar to Next.js App Router with layouts and nested routes.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.3s"><div class="text-4xl mb-4">🌊</div><h3 class="text-xl font-bold text-white mb-4">Streaming SSR</h3><p class="text-gray-300 leading-relaxed">Server-side rendering with streaming, React Server Components, and Suspense support.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.4s"><div class="text-4xl mb-4">🎨</div><h3 class="text-xl font-bold text-white mb-4">KilatCSS &amp; KilatAnim</h3><p class="text-gray-300 leading-relaxed">Built-in styling system with Tailwind integration and smooth animations out of the box.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.5s"><div class="text-4xl mb-4">🔌</div><h3 class="text-xl font-bold text-white mb-4">Plugin System</h3><p class="text-gray-300 leading-relaxed">Extensible architecture with built-in plugins and easy third-party integration.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.6s"><div class="text-4xl mb-4">🚀</div><h3 class="text-xl font-bold text-white mb-4">Production Ready</h3><p class="text-gray-300 leading-relaxed">ISR, caching, error handling, OTA updates, and all production optimizations included.</p></div></div></div></div><section class="py-24 relative"><div class="kilat-container text-center"><div class="kilat-fade-in"><h2 class="text-3xl md:text-4xl font-bold text-white mb-6">Ready to build something amazing?</h2><p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">Join thousands of developers building the next generation of web applications with Kilat.js.</p><a href="/dashboard" class="kilat-btn kilat-btn-primary px-10 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300">Start Building Now<span class="ml-2">🚀</span></a></div></div></section></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>