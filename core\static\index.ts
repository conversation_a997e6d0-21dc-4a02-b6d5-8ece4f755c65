/**
 * 📁 Kilat.js Static File Handler
 * Optimized static file serving with caching
 */

import { join, extname } from 'path'
import { existsSync, readFileSync, statSync } from 'fs'
import type { KilatContext } from '../types'

export class KilatStaticHandler {
  private cache: Map<string, { content: Buffer; headers: Record<string, string> }> = new Map()
  private cacheEnabled: boolean

  constructor(cacheEnabled = true) {
    this.cacheEnabled = cacheEnabled
  }

  /**
   * Serve static file
   */
  async serve(pathname: string, context?: KilatContext): Promise<Response> {
    try {
      // Security check - prevent directory traversal
      if (pathname.includes('..') || pathname.includes('\\')) {
        return new Response('Forbidden', { status: 403 })
      }

      let filePath: string

      // Handle Kilat.js internal files
      if (pathname.startsWith('/_kilat/')) {
        filePath = join(process.cwd(), 'public', pathname)
      } else if (pathname.startsWith('/public/')) {
        filePath = join(process.cwd(), pathname)
      } else {
        // Handle public files
        filePath = join(process.cwd(), 'public', pathname)
      }

      // Check if file exists
      if (!existsSync(filePath)) {
        return new Response('File not found', { status: 404 })
      }

      // Check if it's a file (not directory)
      const stat = statSync(filePath)
      if (!stat.isFile()) {
        return new Response('Not a file', { status: 404 })
      }

      // Check cache first
      if (this.cacheEnabled && this.cache.has(filePath)) {
        const cached = this.cache.get(filePath)!
        return new Response(cached.content, { headers: cached.headers })
      }

      // Read file
      const content = readFileSync(filePath)
      const ext = extname(filePath)
      const contentType = this.getContentType(ext)

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': contentType,
        'Content-Length': content.length.toString(),
        'Cache-Control': this.getCacheControl(ext),
        'Last-Modified': stat.mtime.toUTCString(),
        'ETag': `"${stat.size}-${stat.mtime.getTime()}"`
      }

      // Add CORS headers for development
      if (process.env.NODE_ENV === 'development') {
        headers['Access-Control-Allow-Origin'] = '*'
      }

      // Cache the result
      if (this.cacheEnabled) {
        this.cache.set(filePath, { content, headers })
      }

      return new Response(content, { headers })

    } catch (error) {
      console.error('Static file error:', error)
      return new Response('Internal Server Error', { status: 500 })
    }
  }

  /**
   * Get content type based on file extension
   */
  private getContentType(ext: string): string {
    const types: Record<string, string> = {
      '.html': 'text/html; charset=utf-8',
      '.css': 'text/css; charset=utf-8',
      '.js': 'application/javascript; charset=utf-8',
      '.mjs': 'application/javascript; charset=utf-8',
      '.json': 'application/json; charset=utf-8',
      '.xml': 'application/xml; charset=utf-8',
      '.txt': 'text/plain; charset=utf-8',
      
      // Images
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp',
      '.ico': 'image/x-icon',
      
      // Fonts
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject',
      
      // Audio/Video
      '.mp3': 'audio/mpeg',
      '.mp4': 'video/mp4',
      '.webm': 'video/webm',
      
      // Archives
      '.zip': 'application/zip',
      '.pdf': 'application/pdf'
    }

    return types[ext.toLowerCase()] || 'application/octet-stream'
  }

  /**
   * Get cache control header based on file type
   */
  private getCacheControl(ext: string): string {
    // Long cache for assets with hash in filename
    if (ext.match(/\.(css|js|png|jpg|jpeg|gif|svg|webp|woff|woff2|ttf)$/)) {
      return 'public, max-age=31536000, immutable'
    }

    // Short cache for HTML and other files
    if (ext === '.html') {
      return 'public, max-age=0, must-revalidate'
    }

    // Default cache
    return 'public, max-age=3600'
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Get cache stats
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// Export singleton instance
export const staticHandler = new KilatStaticHandler()

// Legacy compatibility
export async function serveStatic(pathname: string, context?: KilatContext): Promise<Response> {
  return staticHandler.serve(pathname, context)
}
