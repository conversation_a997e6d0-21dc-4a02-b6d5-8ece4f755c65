# ⚡ Kilat.js Quick Start Guide

Get up and running with <PERSON><PERSON>.js in minutes!

## 🚀 Installation

### Prerequisites

- [Bun](https://bun.sh) >= 1.0.0 (recommended)
- Node.js >= 18.0.0 (fallback)

### Create New Project

```bash
# Clone the framework
git clone https://github.com/kilat-js/kilat.js.git my-kilat-app
cd my-kilat-app

# Install dependencies
bun install

# Start development server
bun dev
```

## 🏃‍♂️ Quick Start

### 1. Start the Development Server

```bash
# Using CLI
bun dev

# Or using quick start script
bun quick-start
```

Your app will be available at `http://localhost:3000`

### 2. Project Structure

```
my-kilat-app/
├── apps/                  # 🔀 File-based routing
│   ├── layout.tsx         # Global layout
│   ├── page.tsx           # Homepage
│   ├── about/page.tsx     # About page
│   ├── dashboard/         # Dashboard section
│   │   ├── layout.tsx     # Dashboard layout
│   │   └── page.tsx       # Dashboard page
│   └── api/               # API routes
│       └── users/route.ts # Users API
├── components/            # 💠 UI Components
│   ├── ui/                # Reusable UI components
│   ├── layout/            # Layout components
│   └── shared/            # Shared components
├── core/                  # 🧠 Framework core (don't modify)
├── public/                # 🖼️ Static assets
└── kilat.config.ts        # ⚙️ Configuration
```

### 3. Create Your First Page

Create a new page at `apps/hello/page.tsx`:

```tsx
import React from 'react'

export default function HelloPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-4xl font-bold text-center kilat-gradient-text">
        Hello, Kilat.js! ⚡
      </h1>
      <p className="text-center mt-4 text-gray-600">
        Your new page is ready!
      </p>
    </div>
  )
}
```

Visit `http://localhost:3000/hello` to see your page!

### 4. Create an API Route

Create an API endpoint at `apps/api/hello/route.ts`:

```tsx
import { ApiResponse } from '../../../core/kernel/api-handler'

export async function GET(request: Request): Promise<Response> {
  return ApiResponse.json({
    message: 'Hello from Kilat.js API!',
    timestamp: new Date().toISOString()
  })
}

export async function POST(request: Request): Promise<Response> {
  const body = await request.json()
  
  return ApiResponse.json({
    message: 'Data received!',
    data: body
  })
}
```

Test your API at `http://localhost:3000/api/hello`

### 5. Add Animations

Use KilatAnim for scroll animations:

```tsx
import React from 'react'
import { ScrollAnimation } from '../core/systems/anim/scroll'

export default function AnimatedPage() {
  return (
    <div className="container mx-auto py-8">
      <ScrollAnimation animation="fadeInUp">
        <h1 className="text-4xl font-bold">Animated Content</h1>
      </ScrollAnimation>
      
      <ScrollAnimation animation="slideInUp" delay={200}>
        <p className="mt-4">This content slides in after a delay!</p>
      </ScrollAnimation>
    </div>
  )
}
```

## 🎨 Themes

Kilat.js comes with built-in themes:

### Glow Theme (Default)
Cyberpunk neon aesthetic with glowing effects

### Cyber Theme
Matrix-inspired dark theme with green accents

### Pastel Theme
Soft and modern light theme

### Switch Themes

Update `kilat.config.ts`:

```typescript
export default {
  ui: {
    css: {
      defaultTheme: 'cyber', // 'glow' | 'cyber' | 'pastel'
    }
  }
}
```

## 🔌 Plugins

Enable built-in plugins in `kilat.config.ts`:

```typescript
import analyticsPlugin from './core/systems/plugin/builtin/analytics'
import seoPlugin from './core/systems/plugin/builtin/seo'

export default {
  plugins: [
    {
      ...analyticsPlugin,
      config: {
        enabled: true,
        trackPageViews: true
      }
    },
    {
      ...seoPlugin,
      config: {
        defaultTitle: 'My Awesome App',
        siteName: 'My App'
      }
    }
  ]
}
```

## 🛠️ CLI Commands

```bash
# Development
bun dev                    # Start dev server
bun dev --port 4000        # Custom port

# Building
bun build                  # Build for production
bun start                  # Start production server

# Generation
bun generate page about    # Generate new page
bun generate api posts     # Generate new API route
bun generate component Button --ui  # Generate UI component

# Testing
bun test                   # Run unit tests
bun test:e2e              # Run E2E tests
bun test:all              # Run all tests

# Utilities
bun graph                  # Show dependency graph
bun upgrade               # Update framework
bun export                # Export static site
```

## 🚀 Deployment

### Build for Production

```bash
bun build
```

### Deploy to Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

### Deploy to Netlify

```bash
# Build
bun build

# Deploy dist folder to Netlify
```

### Deploy to Cloudflare Pages

```bash
# Build
bun build

# Deploy dist folder to Cloudflare Pages
```

## 📚 Next Steps

- Read the [full documentation](https://kilat-js.pcode.my.id)
- Explore [examples](https://github.com/kilat-js/examples)
- Join our [Discord community](https://discord.gg/kilat-js)
- Check out [plugins](https://github.com/kilat-js/plugins)

## 🆘 Need Help?

- 📖 [Documentation](https://kilat-js.pcode.my.id)
- 💬 [Discord](https://discord.gg/kilat-js)
- 🐛 [Issues](https://github.com/kilat-js/kilat.js/issues)
- 💡 [Discussions](https://github.com/kilat-js/kilat.js/discussions)

---

Happy coding with Kilat.js! ⚡
