
/**
 * 🧭 Kilat.js Client Router
 */
window.__KILAT_ROUTES__ = {
  "/about": "apps\\about\\page.tsx",
  "/api/auth/login": "apps\\api\\auth\\login\\route.ts",
  "/api/auth/register": "apps\\api\\auth\\register\\route.ts",
  "/api/users": "apps\\api\\users\\route.ts",
  "/blog": "apps\\blog\\page.tsx",
  "/dashboard": "apps\\dashboard\\page.tsx",
  "/login": "apps\\login\\page.tsx",
  "/": "apps\\page.tsx",
  "/register": "apps\\register\\page.tsx",
  "/blog/:slug": "apps\\blog\\[slug]\\page.tsx"
};

class KilatClientRouter {
  constructor() {
    this.currentRoute = window.location.pathname;
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle popstate for back/forward
    window.addEventListener('popstate', (event) => {
      this.navigate(window.location.pathname, false);
    });

    // Handle link clicks
    document.addEventListener('click', (event) => {
      const link = event.target.closest('a[href^="/"]');
      if (link && !link.hasAttribute('target')) {
        event.preventDefault();
        this.navigate(link.getAttribute('href'));
      }
    });
  }

  async navigate(path, pushState = true) {
    if (path === this.currentRoute) return;

    try {
      // Load page component
      const componentPath = window.__KILAT_ROUTES__[path];
      if (!componentPath) {
        throw new Error('Route not found: ' + path);
      }

      const module = await import(componentPath);
      const Component = module.default || module;

      // Update URL
      if (pushState) {
        history.pushState({}, '', path);
      }

      // Render component
      const container = document.getElementById('__kilat');
      const element = React.createElement(Component);
      ReactDOM.render(element, container);

      this.currentRoute = path;
      
    } catch (error) {
      console.error('Navigation failed:', error);
    }
  }
}

// Initialize client router
if (typeof window !== 'undefined') {
  window.__KILAT_ROUTER__ = new KilatClientRouter();
}
