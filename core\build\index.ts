/**
 * 🏗️ Kilat.js Build System
 * Auto-build with SSG, SSR, CSR optimization
 */

import { join } from 'path'
import { existsSync, writeFileSync, mkdirSync } from 'fs'
import type { BuildConfig, SpeedRunConfig } from '../types'
import { generator } from '../generator'
import { router } from '../router'

export class KilatBuilder {
  private config: BuildConfig & SpeedRunConfig

  constructor(config: Partial<BuildConfig & SpeedRunConfig> = {}) {
    this.config = {
      // Build config
      outDir: '.kilat/dist',
      staticDir: '.kilat/static',
      serverDir: '.kilat/server',
      clientDir: '.kilat/client',
      mode: 'production',
      target: 'hybrid',
      
      // SpeedRun config
      runtime: 'bun',
      port: 3000,
      host: '0.0.0.0',
      cors: true,
      compression: true,
      staticFiles: true,
      hotReload: false,
      
      ...config
    }
  }

  /**
   * Full build process
   */
  async build(): Promise<void> {
    console.log('🚀 Starting Kilat.js build...')
    const startTime = Date.now()

    try {
      // Clean previous build
      await this.clean()
      
      // Initialize router
      console.log('🔍 Scanning routes...')
      
      // Generate static pages
      console.log('⚡ Generating static pages...')
      await generator.generateAll()
      
      // Build client bundle
      console.log('📦 Building client bundle...')
      await this.buildClient()
      
      // Build server bundle
      console.log('🖥️ Building server bundle...')
      await this.buildServer()
      
      // Generate additional files
      await this.generateAdditionalFiles()
      
      // Build summary
      const buildTime = Date.now() - startTime
      console.log(`✅ Build completed in ${buildTime}ms`)
      
      this.printBuildSummary()
      
    } catch (error) {
      console.error('❌ Build failed:', error)
      process.exit(1)
    }
  }

  /**
   * Development build
   */
  async dev(): Promise<void> {
    console.log('🔥 Starting development server...')
    
    this.config.mode = 'development'
    this.config.hotReload = true
    
    // Start file watcher
    await this.startFileWatcher()
    
    // Start dev server
    await this.startDevServer()
  }

  /**
   * Clean build directory
   */
  private async clean(): Promise<void> {
    console.log('🧹 Cleaning build directory...')
    generator.clean()
  }

  /**
   * Build client-side bundle
   */
  private async buildClient(): Promise<void> {
    const clientDir = this.config.clientDir
    mkdirSync(clientDir, { recursive: true })

    // Generate hydration script
    const hydrateScript = this.generateHydrateScript()
    writeFileSync(join(clientDir, 'hydrate.js'), hydrateScript)

    // Generate client router
    const clientRouter = this.generateClientRouter()
    writeFileSync(join(clientDir, 'router.js'), clientRouter)

    // Copy runtime files
    await this.copyRuntimeFiles(clientDir)
  }

  /**
   * Build server-side bundle
   */
  private async buildServer(): Promise<void> {
    const serverDir = this.config.serverDir
    mkdirSync(serverDir, { recursive: true })

    // Generate server entry
    const serverEntry = this.generateServerEntry()
    writeFileSync(join(serverDir, 'index.js'), serverEntry)

    // Generate production config
    const prodConfig = this.generateProductionConfig()
    writeFileSync(join(serverDir, 'config.json'), JSON.stringify(prodConfig, null, 2))
  }

  /**
   * Generate hydration script for CSR
   */
  private generateHydrateScript(): string {
    return `
/**
 * 💧 Kilat.js Client Hydration
 */
(function() {
  'use strict';

  // Wait for DOM and Kilat runtime
  function initHydration() {
    if (!window.Kilat || !window.React) {
      setTimeout(initHydration, 50);
      return;
    }

    const data = window.__KILAT_DATA__;
    if (!data) {
      console.warn('No hydration data found');
      return;
    }

    // Load and render page component
    loadPageComponent(data.route, data.params)
      .then(Component => {
        if (Component) {
          hydrateComponent(Component, data.params);
        }
      })
      .catch(error => {
        console.error('Hydration failed:', error);
        showError(error);
      });
  }

  async function loadPageComponent(route, params) {
    try {
      // Dynamic import based on route
      const componentPath = getComponentPath(route);
      const module = await import(componentPath);
      return module.default || module;
    } catch (error) {
      console.error('Failed to load component:', error);
      return null;
    }
  }

  function hydrateComponent(Component, params) {
    const container = document.getElementById('__kilat');
    if (!container) return;

    // Create React element
    const element = React.createElement(Component, { params });
    
    // Hydrate
    if (window.ReactDOM && window.ReactDOM.hydrate) {
      window.ReactDOM.hydrate(element, container);
    } else {
      // Fallback to render
      container.innerHTML = '';
      window.ReactDOM.render(element, container);
    }

    console.log('💧 Hydration complete');
  }

  function getComponentPath(route) {
    // Map route to component path
    const routeMap = window.__KILAT_ROUTES__ || {};
    return routeMap[route] || '/apps' + route + '/page.js';
  }

  function showError(error) {
    const container = document.getElementById('__kilat');
    if (container) {
      container.innerHTML = \`
        <div class="min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-purple-900 flex items-center justify-center">
          <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">Hydration Error</h1>
            <p class="text-gray-300">\${error.message}</p>
          </div>
        </div>
      \`;
    }
  }

  // Start hydration
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initHydration);
  } else {
    initHydration();
  }
})();
`
  }

  /**
   * Generate client-side router
   */
  private generateClientRouter(): string {
    const routes = router.getAllRoutes()
    const routeMap = routes.reduce((map, route) => {
      map[route.path] = route.component
      return map
    }, {} as Record<string, string>)

    return `
/**
 * 🧭 Kilat.js Client Router
 */
window.__KILAT_ROUTES__ = ${JSON.stringify(routeMap, null, 2)};

class KilatClientRouter {
  constructor() {
    this.currentRoute = window.location.pathname;
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle popstate for back/forward
    window.addEventListener('popstate', (event) => {
      this.navigate(window.location.pathname, false);
    });

    // Handle link clicks
    document.addEventListener('click', (event) => {
      const link = event.target.closest('a[href^="/"]');
      if (link && !link.hasAttribute('target')) {
        event.preventDefault();
        this.navigate(link.getAttribute('href'));
      }
    });
  }

  async navigate(path, pushState = true) {
    if (path === this.currentRoute) return;

    try {
      // Load page component
      const componentPath = window.__KILAT_ROUTES__[path];
      if (!componentPath) {
        throw new Error('Route not found: ' + path);
      }

      const module = await import(componentPath);
      const Component = module.default || module;

      // Update URL
      if (pushState) {
        history.pushState({}, '', path);
      }

      // Render component
      const container = document.getElementById('__kilat');
      const element = React.createElement(Component);
      ReactDOM.render(element, container);

      this.currentRoute = path;
      
    } catch (error) {
      console.error('Navigation failed:', error);
    }
  }
}

// Initialize client router
if (typeof window !== 'undefined') {
  window.__KILAT_ROUTER__ = new KilatClientRouter();
}
`
  }

  /**
   * Generate server entry point
   */
  private generateServerEntry(): string {
    return `
/**
 * 🖥️ Kilat.js Production Server
 */
const { serve } = require('${this.config.runtime === 'bun' ? 'bun' : 'http'}');
const { renderer } = require('../renderer');
const { router } = require('../router');

const config = require('./config.json');

async function handleRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;

  try {
    // Serve static files
    if (pathname.startsWith('/_kilat/')) {
      return serveStatic(pathname);
    }

    // Handle API routes
    if (pathname.startsWith('/api/')) {
      return handleApiRoute(pathname, request);
    }

    // Render page
    const context = {
      request,
      pathname,
      searchParams: url.searchParams
    };

    const result = await renderer.render(pathname, context);
    
    return new Response(result.html, {
      status: result.statusCode || 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        ...result.headers
      }
    });

  } catch (error) {
    console.error('Server error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

function serveStatic(pathname) {
  // Serve static files from dist directory
  const fs = require('fs');
  const path = require('path');
  
  const filePath = path.join(__dirname, '../dist', pathname);
  
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath);
    const ext = path.extname(filePath);
    const contentType = getContentType(ext);
    
    return new Response(content, {
      headers: { 'Content-Type': contentType }
    });
  }
  
  return new Response('Not Found', { status: 404 });
}

function getContentType(ext) {
  const types = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.svg': 'image/svg+xml'
  };
  return types[ext] || 'text/plain';
}

async function handleApiRoute(pathname, request) {
  const routeMatch = router.matchRoute(pathname);
  if (!routeMatch || routeMatch.route.type !== 'api') {
    return new Response('API route not found', { status: 404 });
  }

  try {
    const module = require(routeMatch.route.component);
    const method = request.method.toUpperCase();
    const handler = module[method] || module.default;
    
    if (!handler) {
      return new Response('Method not allowed', { status: 405 });
    }

    return await handler(request);
  } catch (error) {
    console.error('API error:', error);
    return new Response('API Error', { status: 500 });
  }
}

// Start server
const server = serve({
  port: config.port,
  hostname: config.host,
  fetch: handleRequest
});

console.log(\`🚀 Kilat.js server running on http://\${config.host}:\${config.port}\`);
`
  }

  /**
   * Generate production configuration
   */
  private generateProductionConfig(): any {
    return {
      runtime: this.config.runtime,
      port: this.config.port,
      host: this.config.host,
      cors: this.config.cors,
      compression: this.config.compression,
      staticFiles: this.config.staticFiles,
      mode: 'production'
    }
  }

  /**
   * Copy runtime files to build directory
   */
  private async copyRuntimeFiles(targetDir: string): Promise<void> {
    const runtimeFiles = [
      'public/_kilat/styles.css',
      'public/_kilat/runtime.js',
      'public/_kilat/client.js',
      'public/_kilat/kilat-anim.js'
    ]

    for (const file of runtimeFiles) {
      if (existsSync(file)) {
        const fs = require('fs')
        const content = fs.readFileSync(file)
        const targetPath = join(targetDir, file.replace('public/_kilat/', ''))
        writeFileSync(targetPath, content)
      }
    }
  }

  /**
   * Generate additional files
   */
  private async generateAdditionalFiles(): Promise<void> {
    // Generate sitemap
    await generator.generateSitemap()
    
    // Generate robots.txt
    await generator.generateRobots()
    
    // Generate package.json for deployment
    const packageJson = {
      name: 'kilat-app',
      version: '1.0.0',
      type: 'module',
      scripts: {
        start: `${this.config.runtime} server/index.js`
      },
      dependencies: {
        react: '^18.0.0',
        'react-dom': '^18.0.0'
      }
    }
    
    writeFileSync(
      join(this.config.outDir, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    )
  }

  /**
   * Start file watcher for development
   */
  private async startFileWatcher(): Promise<void> {
    // Simple file watcher - in production, use chokidar
    console.log('👀 Watching for file changes...')
  }

  /**
   * Start development server
   */
  private async startDevServer(): Promise<void> {
    console.log(`🔥 Dev server running on http://${this.config.host}:${this.config.port}`)
  }

  /**
   * Print build summary
   */
  private printBuildSummary(): void {
    const stats = generator.getStats()
    
    console.log('\n📊 Build Summary:')
    console.log(`   Pages: ${stats.totalPages}`)
    console.log(`   Target: ${this.config.target}`)
    console.log(`   Runtime: ${this.config.runtime}`)
    console.log(`   Output: ${this.config.outDir}`)
    console.log('')
  }
}

// Export singleton instance
export const builder = new KilatBuilder()
