/**
 * ⚡ Kilat.js Configuration Example
 * Copy this file to kilat.config.ts and customize as needed
 */

import type { KilatConfig } from './types/config'
import analyticsPlugin from './core/systems/plugin/builtin/analytics'
import hmrPlugin from './core/systems/plugin/builtin/hmr'
import compressionPlugin from './core/systems/plugin/builtin/compression'
import seoPlugin from './core/systems/plugin/builtin/seo'

const config: KilatConfig = {
  // 🚀 Runtime Configuration
  runtime: {
    engine: 'bun', // 'bun' | 'node'
    port: 3000,
    host: 'localhost',
    https: false,
  },

  // 🗂️ Apps Mapping (File-based Routing)
  apps: {
    dir: './apps',
    extensions: ['.tsx', '.ts', '.jsx', '.js'],
    middleware: true,
    groups: true,
  },

  // 🎨 UI Systems
  ui: {
    css: {
      framework: 'tailwind', // 'tailwind' | 'custom'
      themes: ['glow', 'cyber', 'pastel'],
      defaultTheme: 'glow',
    },
    animations: {
      enabled: true,
      presets: ['scroll', 'orbit', '3d', 'hover'],
    },
  },

  // 🌊 Rendering
  render: {
    mode: 'ssr', // 'ssr' | 'csr' | 'ssg' | 'isr'
    streaming: true,
    rsc: true, // React Server Components
    suspense: true,
  },

  // 🔌 Plugins
  plugins: [
    // Built-in plugins
    {
      ...analyticsPlugin,
      config: {
        enabled: true,
        trackPageViews: true,
        trackPerformance: true,
      }
    },
    {
      ...hmrPlugin,
      config: {
        enabled: process.env.NODE_ENV === 'development',
        port: 3001,
      }
    },
    {
      ...compressionPlugin,
      config: {
        enabled: process.env.NODE_ENV === 'production',
        algorithms: ['gzip', 'br'],
        threshold: 1024,
      }
    },
    {
      ...seoPlugin,
      config: {
        enabled: true,
        defaultTitle: 'My Kilat.js App',
        defaultDescription: 'Built with Kilat.js - Modern Fullstack Framework',
        siteName: 'My App',
        siteUrl: 'https://myapp.com',
      }
    },
    
    // Custom plugins
    // {
    //   name: 'my-custom-plugin',
    //   module: './plugins/my-plugin.ts',
    //   config: {
    //     apiKey: process.env.MY_PLUGIN_API_KEY
    //   }
    // }
  ],

  // 🛠️ Development
  dev: {
    overlay: true,
    hmr: true,
    analytics: true,
  },

  // 🏗️ Build
  build: {
    outDir: './dist',
    target: 'es2022',
    minify: true,
    sourcemap: true,
  },

  // 🌐 Deployment
  deploy: {
    platform: 'auto', // 'vercel' | 'netlify' | 'cloudflare' | 'auto'
    edge: true,
  },
}

export default config
