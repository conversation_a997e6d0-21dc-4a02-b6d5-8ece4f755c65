/**
 * 🧭 Header Component - Advanced Navigation with Dropdown Menus
 */

import React from 'react'

interface HeaderProps {
  isAuthenticated?: boolean
  user?: {
    name: string
    email: string
    avatar?: string
  }
}

export default function Header({ isAuthenticated = false, user }: HeaderProps) {
  return (
    <header className="fixed top-4 left-4 right-4 z-50 kilat-glass rounded-2xl border border-white/20 backdrop-blur-xl bg-white/10 shadow-2xl">
      <div className="px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <a href="/" className="flex items-center space-x-3 kilat-glow-hover transition-all duration-300 group">
            <div className="w-9 h-9 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <span className="text-white font-bold">K</span>
            </div>
            <div className="hidden sm:block">
              <span className="text-xl font-bold kilat-gradient-text">Kilat.js</span>
              <p className="text-xs text-gray-400 leading-none">SpeedRun™</p>
            </div>
          </a>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            <a href="/" className="nav-link group">
              <span className="nav-icon">🏠</span>
              <span className="nav-text">Home</span>
            </a>
            <a href="/about" className="nav-link group">
              <span className="nav-icon">📄</span>
              <span className="nav-text">About</span>
            </a>
            <a href="/blog" className="nav-link group">
              <span className="nav-icon">📝</span>
              <span className="nav-text">Blog</span>
            </a>
            {isAuthenticated && (
              <a href="/dashboard" className="nav-link group">
                <span className="nav-icon">📊</span>
                <span className="nav-text">Dashboard</span>
              </a>
            )}

            {/* Dropdown Menu - Documentation */}
            <div className="relative group">
              <button className="nav-link">
                <span className="nav-icon">📚</span>
                <span className="nav-text">Docs</span>
                <svg className="w-3 h-3 ml-1 transition-transform group-hover:rotate-180" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>

              {/* Dropdown Content */}
              <div className="absolute top-full left-0 mt-2 w-56 kilat-glass rounded-xl border border-white/20 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                <div className="p-2">
                  <a href="/docs/getting-started" className="dropdown-item">
                    <span className="text-green-400">🚀</span>
                    <div>
                      <div className="font-medium">Getting Started</div>
                      <div className="text-xs text-gray-400">Quick setup guide</div>
                    </div>
                  </a>
                  <a href="/docs/routing" className="dropdown-item">
                    <span className="text-blue-400">🧭</span>
                    <div>
                      <div className="font-medium">Routing</div>
                      <div className="text-xs text-gray-400">File-based routing</div>
                    </div>
                  </a>
                  <a href="/docs/api" className="dropdown-item">
                    <span className="text-purple-400">🛠️</span>
                    <div>
                      <div className="font-medium">API Reference</div>
                      <div className="text-xs text-gray-400">Complete API docs</div>
                    </div>
                  </a>
                  <a href="/docs/examples" className="dropdown-item">
                    <span className="text-orange-400">💡</span>
                    <div>
                      <div className="font-medium">Examples</div>
                      <div className="text-xs text-gray-400">Code examples</div>
                    </div>
                  </a>
                </div>
              </div>
            </div>

            {/* Dropdown Menu - Community */}
            <div className="relative group">
              <button className="nav-link">
                <span className="nav-icon">👥</span>
                <span className="nav-text">Community</span>
                <svg className="w-3 h-3 ml-1 transition-transform group-hover:rotate-180" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>

              {/* Dropdown Content */}
              <div className="absolute top-full right-0 mt-2 w-48 kilat-glass rounded-xl border border-white/20 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                <div className="p-2">
                  <a href="/community/discord" className="dropdown-item">
                    <span className="text-indigo-400">💬</span>
                    <div>
                      <div className="font-medium">Discord</div>
                      <div className="text-xs text-gray-400">Join our server</div>
                    </div>
                  </a>
                  <a href="/community/github" className="dropdown-item">
                    <span className="text-gray-400">🐙</span>
                    <div>
                      <div className="font-medium">GitHub</div>
                      <div className="text-xs text-gray-400">Source code</div>
                    </div>
                  </a>
                  <a href="/community/discussions" className="dropdown-item">
                    <span className="text-yellow-400">💭</span>
                    <div>
                      <div className="font-medium">Discussions</div>
                      <div className="text-xs text-gray-400">Community forum</div>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </nav>

          {/* Mobile Menu Button */}
          <button className="lg:hidden kilat-glass p-2 rounded-xl hover:bg-white/20 transition-all duration-300" id="mobile-menu-button">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          {/* Theme Switcher & Auth Section */}
          <div className="flex items-center space-x-4">
            {/* Theme Switcher */}
            <div className="kilat-theme-switcher flex items-center space-x-2 kilat-glass px-3 py-2 rounded-xl">
              <button
                data-theme="default"
                className="kilat-theme-btn w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 border-2 border-transparent hover:border-white/50 transition-all duration-300"
                title="Default Theme"
              ></button>
              <button
                data-theme="cyber"
                className="kilat-theme-btn w-6 h-6 rounded-full bg-gradient-to-r from-green-400 to-pink-500 border-2 border-transparent hover:border-white/50 transition-all duration-300"
                title="Cyber Theme"
              ></button>
              <button
                data-theme="nusantara"
                className="kilat-theme-btn w-6 h-6 rounded-full bg-gradient-to-r from-orange-500 to-red-500 border-2 border-transparent hover:border-white/50 transition-all duration-300"
                title="Nusantara Theme"
              ></button>
            </div>

            {isAuthenticated && user ? (
              <div className="flex items-center space-x-3">
                {/* User Menu */}
                <div className="flex items-center space-x-2 kilat-glass px-3 py-2 rounded-xl">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="text-white font-medium hidden sm:block">{user.name}</span>
                </div>

                {/* Logout Button */}
                <button className="kilat-btn kilat-btn-outline px-4 py-2 text-sm rounded-xl">
                  🚪 Logout
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <a href="/login" className="kilat-btn kilat-btn-outline px-4 py-2 text-sm rounded-xl">
                  🔑 Login
                </a>
                <a href="/register" className="kilat-btn kilat-btn-primary px-4 py-2 text-sm rounded-xl">
                  ✨ Register
                </a>
              </div>
            )}

            {/* Mobile Menu Button */}
            <button className="md:hidden kilat-btn kilat-btn-outline px-3 py-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-white/10 py-4 hidden" id="mobile-menu">
          <div className="flex flex-col space-y-2">
            <a href="/" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
              🏠 Home
            </a>
            <a href="/about" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
              📄 About
            </a>
            {isAuthenticated ? (
              <a href="/dashboard" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
                📊 Dashboard
              </a>
            ) : null}
            <a href="/api/users" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
              🛠️ API
            </a>
            
            {!isAuthenticated && (
              <div className="flex flex-col space-y-2 pt-4 border-t border-white/10">
                <a href="/login" className="kilat-btn kilat-btn-outline text-center">
                  🔑 Login
                </a>
                <a href="/register" className="kilat-btn kilat-btn-primary text-center">
                  ✨ Register
                </a>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div className="lg:hidden absolute top-full left-0 right-0 mt-2 mx-4 kilat-glass rounded-xl border border-white/20 opacity-0 invisible transition-all duration-300" id="mobile-menu">
        <div className="p-4">
          {/* Mobile Navigation */}
          <nav className="space-y-2 mb-4">
            <a href="/" className="mobile-nav-link">
              <span className="text-lg">🏠</span>
              <span>Home</span>
            </a>
            <a href="/about" className="mobile-nav-link">
              <span className="text-lg">📄</span>
              <span>About</span>
            </a>
            <a href="/blog" className="mobile-nav-link">
              <span className="text-lg">📝</span>
              <span>Blog</span>
            </a>
            {isAuthenticated && (
              <a href="/dashboard" className="mobile-nav-link">
                <span className="text-lg">📊</span>
                <span>Dashboard</span>
              </a>
            )}

            {/* Mobile Documentation */}
            <div className="border-t border-white/10 pt-2 mt-2">
              <p className="text-xs text-gray-400 mb-2 px-3">Documentation</p>
              <a href="/docs/getting-started" className="mobile-nav-link">
                <span className="text-green-400">🚀</span>
                <span>Getting Started</span>
              </a>
              <a href="/docs/routing" className="mobile-nav-link">
                <span className="text-blue-400">🧭</span>
                <span>Routing</span>
              </a>
              <a href="/docs/api" className="mobile-nav-link">
                <span className="text-purple-400">🛠️</span>
                <span>API Reference</span>
              </a>
            </div>

            {/* Mobile Community */}
            <div className="border-t border-white/10 pt-2 mt-2">
              <p className="text-xs text-gray-400 mb-2 px-3">Community</p>
              <a href="/community/discord" className="mobile-nav-link">
                <span className="text-indigo-400">💬</span>
                <span>Discord</span>
              </a>
              <a href="/community/github" className="mobile-nav-link">
                <span className="text-gray-400">🐙</span>
                <span>GitHub</span>
              </a>
            </div>
          </nav>
        </div>
      </div>
    </header>
  )
}
