/**
 * 🧭 Header Component - Global Navigation
 */

import React from 'react'

interface HeaderProps {
  isAuthenticated?: boolean
  user?: {
    name: string
    email: string
    avatar?: string
  }
}

export default function Header({ isAuthenticated = false, user }: HeaderProps) {
  return (
    <header className="fixed top-4 left-4 right-4 z-50 kilat-glass rounded-2xl border border-white/20 backdrop-blur-xl bg-white/10 shadow-2xl">
      <div className="px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <a href="/" className="flex items-center space-x-2 kilat-glow-hover transition-all duration-300">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">K</span>
            </div>
            <span className="text-xl font-bold kilat-gradient-text">Kilat.js</span>
          </a>

          {/* Navigation Links */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="/" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
              🏠 Home
            </a>
            <a href="/about" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
              📄 About
            </a>
            {isAuthenticated ? (
              <a href="/dashboard" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
                📊 Dashboard
              </a>
            ) : null}
            <a href="/api/users" className="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">
              🛠️ API
            </a>
          </nav>

          {/* Theme Switcher & Auth Section */}
          <div className="flex items-center space-x-4">
            {/* Theme Switcher */}
            <div className="kilat-theme-switcher flex items-center space-x-2 kilat-glass px-3 py-2 rounded-xl">
              <button
                data-theme="default"
                className="kilat-theme-btn w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 border-2 border-transparent hover:border-white/50 transition-all duration-300"
                title="Default Theme"
              ></button>
              <button
                data-theme="cyber"
                className="kilat-theme-btn w-6 h-6 rounded-full bg-gradient-to-r from-green-400 to-pink-500 border-2 border-transparent hover:border-white/50 transition-all duration-300"
                title="Cyber Theme"
              ></button>
              <button
                data-theme="nusantara"
                className="kilat-theme-btn w-6 h-6 rounded-full bg-gradient-to-r from-orange-500 to-red-500 border-2 border-transparent hover:border-white/50 transition-all duration-300"
                title="Nusantara Theme"
              ></button>
            </div>

            {isAuthenticated && user ? (
              <div className="flex items-center space-x-3">
                {/* User Menu */}
                <div className="flex items-center space-x-2 kilat-glass px-3 py-2 rounded-xl">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="text-white font-medium hidden sm:block">{user.name}</span>
                </div>

                {/* Logout Button */}
                <button className="kilat-btn kilat-btn-outline px-4 py-2 text-sm rounded-xl">
                  🚪 Logout
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <a href="/login" className="kilat-btn kilat-btn-outline px-4 py-2 text-sm rounded-xl">
                  🔑 Login
                </a>
                <a href="/register" className="kilat-btn kilat-btn-primary px-4 py-2 text-sm rounded-xl">
                  ✨ Register
                </a>
              </div>
            )}

            {/* Mobile Menu Button */}
            <button className="md:hidden kilat-btn kilat-btn-outline px-3 py-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-white/10 py-4 hidden" id="mobile-menu">
          <div className="flex flex-col space-y-2">
            <a href="/" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
              🏠 Home
            </a>
            <a href="/about" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
              📄 About
            </a>
            {isAuthenticated ? (
              <a href="/dashboard" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
                📊 Dashboard
              </a>
            ) : null}
            <a href="/api/users" className="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">
              🛠️ API
            </a>
            
            {!isAuthenticated && (
              <div className="flex flex-col space-y-2 pt-4 border-t border-white/10">
                <a href="/login" className="kilat-btn kilat-btn-outline text-center">
                  🔑 Login
                </a>
                <a href="/register" className="kilat-btn kilat-btn-primary text-center">
                  ✨ Register
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
