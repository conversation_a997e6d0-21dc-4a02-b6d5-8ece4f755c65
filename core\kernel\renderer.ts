/**
 * 🎨 Page Renderer - SSR with Streaming Support
 */

import React from 'react'
import type { KilatContext } from '../../types/config'
import type { RouteMatch } from './router'
import { loadComponent } from './esm-loader'

/**
 * Render page with SSR
 */
export async function renderPage(
  request: Request,
  routeMatch: RouteMatch,
  context: KilatContext
): Promise<Response> {
  try {
    const { route, params, query } = routeMatch
    
    // Load page component
    const PageComponent = await loadComponent(route.component)
    
    // Load layout component if exists
    let LayoutComponent: React.ComponentType<any> | null = null
    if (route.layout) {
      LayoutComponent = await loadComponent(route.layout)
    }
    
    // Prepare props
    const pageProps = {
      params,
      searchParams: query,
    }
    
    // Create page element
    const pageElement = React.createElement(PageComponent, pageProps)
    
    // Wrap with layout if exists
    const element = LayoutComponent 
      ? React.createElement(LayoutComponent, { children: pageElement })
      : pageElement
    
    // Render based on configuration
    if (context.config.render.streaming) {
      return renderWithStreaming(element, context)
    } else {
      return renderStatic(element, context)
    }
    
  } catch (error) {
    console.error('❌ Page render error:', error)
    throw error
  }
}

/**
 * Render with streaming SSR
 */
async function renderWithStreaming(
  element: React.ReactElement,
  context: KilatContext
): Promise<Response> {
  // Import React DOM server with dynamic import
  const { renderToPipeableStream } = await import('react-dom/server')
  
  let didError = false
  
  const stream = new ReadableStream({
    start(controller) {
      const pipe = renderToPipeableStream(element, {
        onShellReady() {
          // Start streaming the shell
          const encoder = new TextEncoder()
          controller.enqueue(encoder.encode(getDocumentStart()))
        },
        onAllReady() {
          // Finish streaming
          const encoder = new TextEncoder()
          controller.enqueue(encoder.encode(getDocumentEnd()))
          controller.close()
        },
        onError(error: Error) {
          didError = true
          console.error('❌ Streaming render error:', error)
          controller.error(error)
        }
      })
      
      // Pipe to controller
      const writer = new WritableStream({
        write(chunk) {
          controller.enqueue(chunk)
        }
      })
      
      pipe.pipe(writer as any)
    }
  })
  
  return new Response(stream, {
    status: didError ? 500 : 200,
    headers: {
      'Content-Type': 'text/html; charset=utf-8',
      'Transfer-Encoding': 'chunked',
    }
  })
}

/**
 * Render static HTML
 */
async function renderStatic(
  element: React.ReactElement,
  context: KilatContext
): Promise<Response> {
  try {
    // Import React DOM server
    const { renderToString } = await import('react-dom/server')
    
    // Render to string
    const html = renderToString(element)
    
    // Create complete HTML document
    const document = `
      ${getDocumentStart()}
      ${html}
      ${getDocumentEnd()}
    `
    
    return new Response(document, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      }
    })
    
  } catch (error) {
    console.error('❌ Static render error:', error)
    throw error
  }
}

/**
 * Get document start HTML
 */
function getDocumentStart(): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Kilat.js App</title>
      <link rel="stylesheet" href="/_kilat/styles.css">
    </head>
    <body>
      <div id="__kilat">
  `
}

/**
 * Get document end HTML
 */
function getDocumentEnd(): string {
  return `
      </div>
      <script src="/_kilat/runtime.js"></script>
      <script src="/_kilat/client.js"></script>
    </body>
    </html>
  `
}
