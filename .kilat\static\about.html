<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js SpeedRun™ Runtime" />
  
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><section class="py-20 sm:py-32"><div class="kilat-container"><div class="text-center mb-20 kilat-fade-in"><h1 class="text-5xl md:text-6xl font-bold text-white mb-6">About<!-- --> <span class="kilat-gradient-text kilat-text-glow">Kilat.js</span></h1><p class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">The next-generation fullstack framework built for speed, developer experience, and modern web development with cutting-edge technologies.</p></div><div class="grid md:grid-cols-2 gap-8 mb-20"><div class="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style="animation-delay:0.1s"><div class="flex items-center mb-4"><div class="text-3xl mr-4">⚡</div><h2 class="text-2xl font-bold text-white">Lightning Fast</h2></div><p class="text-gray-300 leading-relaxed">Built on Bun.js runtime with SpeedRun™ technology. Experience blazing-fast development and production performance that outpaces traditional frameworks by up to 10x.</p></div><div class="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style="animation-delay:0.2s"><div class="flex items-center mb-4"><div class="text-3xl mr-4">🗂️</div><h2 class="text-2xl font-bold text-white">File-based Routing</h2></div><p class="text-gray-300 leading-relaxed">Apps Mapping system provides intuitive file-based routing similar to Next.js App Router, with support for layouts, middleware, and nested routes for complex applications.</p></div><div class="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style="animation-delay:0.3s"><div class="flex items-center mb-4"><div class="text-3xl mr-4">🌊</div><h2 class="text-2xl font-bold text-white">Streaming SSR</h2></div><p class="text-gray-300 leading-relaxed">Advanced server-side rendering with React 18 features including Suspense, streaming, and React Server Components for optimal performance and SEO.</p></div><div class="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style="animation-delay:0.4s"><div class="flex items-center mb-4"><div class="text-3xl mr-4">🎨</div><h2 class="text-2xl font-bold text-white">Built-in UI Systems</h2></div><p class="text-gray-300 leading-relaxed">KilatCSS and KilatAnim provide beautiful styling and smooth animations out of the box, with Tailwind integration and cyberpunk/nusantara themes.</p></div></div><div class="text-center mb-20 kilat-fade-in" style="animation-delay:0.5s"><h2 class="text-3xl md:text-4xl font-bold text-white mb-12">Built with Modern<!-- --> <span class="kilat-gradient-text">Technologies</span></h2><div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"><div class="kilat-card kilat-glass p-6 text-center kilat-glow-hover"><div class="text-2xl mb-2">🟡</div><h3 class="font-bold text-white">Bun.js</h3><p class="text-sm text-gray-400">Runtime</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-glow-hover"><div class="text-2xl mb-2">⚛️</div><h3 class="font-bold text-white">React 18</h3><p class="text-sm text-gray-400">UI Library</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-glow-hover"><div class="text-2xl mb-2">🎨</div><h3 class="font-bold text-white">Tailwind</h3><p class="text-sm text-gray-400">CSS Framework</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-glow-hover"><div class="text-2xl mb-2">📦</div><h3 class="font-bold text-white">TypeScript</h3><p class="text-sm text-gray-400">Type Safety</p></div></div></div><div class="text-center kilat-fade-in" style="animation-delay:0.6s"><h2 class="text-3xl md:text-4xl font-bold text-white mb-8">Ready to experience the future?</h2><div class="flex flex-col sm:flex-row gap-4 justify-center"><a href="/dashboard" class="kilat-btn kilat-btn-primary px-8 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300"><span class="mr-2">📊</span>Try Dashboard</a><a href="/" class="kilat-btn kilat-btn-outline px-8 py-4 text-lg font-semibold hover:kilat-glow transition-all duration-300"><span class="mr-2">🏠</span>Back to Home</a></div></div></div></section></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>