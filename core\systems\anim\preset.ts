/**
 * 🎭 KilatAnim - Animation Presets
 */

export interface AnimationPreset {
  name: string
  description: string
  className: string
  duration?: string
  easing?: string
  delay?: string
}

/**
 * Scroll-based animations
 */
export const scrollAnimations: AnimationPreset[] = [
  {
    name: 'fadeInUp',
    description: 'Fade in from bottom',
    className: 'kilat-anim-fade-in-up',
    duration: '0.6s',
    easing: 'ease-out',
  },
  {
    name: 'fadeInDown',
    description: 'Fade in from top',
    className: 'kilat-anim-fade-in-down',
    duration: '0.6s',
    easing: 'ease-out',
  },
  {
    name: 'fadeInLeft',
    description: 'Fade in from left',
    className: 'kilat-anim-fade-in-left',
    duration: '0.6s',
    easing: 'ease-out',
  },
  {
    name: 'fadeInRight',
    description: 'Fade in from right',
    className: 'kilat-anim-fade-in-right',
    duration: '0.6s',
    easing: 'ease-out',
  },
  {
    name: 'slideInUp',
    description: 'Slide in from bottom',
    className: 'kilat-anim-slide-in-up',
    duration: '0.8s',
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  },
  {
    name: 'zoomIn',
    description: 'Zoom in effect',
    className: 'kilat-anim-zoom-in',
    duration: '0.5s',
    easing: 'ease-out',
  },
]

/**
 * 3D animations
 */
export const threeDAnimations: AnimationPreset[] = [
  {
    name: 'rotateX',
    description: '3D rotation on X axis',
    className: 'kilat-anim-rotate-x',
    duration: '1s',
    easing: 'ease-in-out',
  },
  {
    name: 'rotateY',
    description: '3D rotation on Y axis',
    className: 'kilat-anim-rotate-y',
    duration: '1s',
    easing: 'ease-in-out',
  },
  {
    name: 'flip',
    description: '3D flip effect',
    className: 'kilat-anim-flip',
    duration: '0.8s',
    easing: 'ease-in-out',
  },
  {
    name: 'cube',
    description: '3D cube rotation',
    className: 'kilat-anim-cube',
    duration: '2s',
    easing: 'linear',
  },
]

/**
 * Hover animations
 */
export const hoverAnimations: AnimationPreset[] = [
  {
    name: 'hoverGlow',
    description: 'Glow effect on hover',
    className: 'kilat-anim-hover-glow',
    duration: '0.3s',
    easing: 'ease-out',
  },
  {
    name: 'hoverScale',
    description: 'Scale up on hover',
    className: 'kilat-anim-hover-scale',
    duration: '0.2s',
    easing: 'ease-out',
  },
  {
    name: 'hoverFloat',
    description: 'Float up on hover',
    className: 'kilat-anim-hover-float',
    duration: '0.3s',
    easing: 'ease-out',
  },
  {
    name: 'hoverTilt',
    description: 'Tilt effect on hover',
    className: 'kilat-anim-hover-tilt',
    duration: '0.3s',
    easing: 'ease-out',
  },
]

/**
 * Orbit animations
 */
export const orbitAnimations: AnimationPreset[] = [
  {
    name: 'orbitSlow',
    description: 'Slow orbital motion',
    className: 'kilat-anim-orbit-slow',
    duration: '10s',
    easing: 'linear',
  },
  {
    name: 'orbitFast',
    description: 'Fast orbital motion',
    className: 'kilat-anim-orbit-fast',
    duration: '3s',
    easing: 'linear',
  },
  {
    name: 'orbitReverse',
    description: 'Reverse orbital motion',
    className: 'kilat-anim-orbit-reverse',
    duration: '8s',
    easing: 'linear',
  },
]

/**
 * Get all animation presets
 */
export function getAllAnimationPresets(): Record<string, AnimationPreset[]> {
  return {
    scroll: scrollAnimations,
    '3d': threeDAnimations,
    hover: hoverAnimations,
    orbit: orbitAnimations,
  }
}

/**
 * Get animation preset by name
 */
export function getAnimationPreset(name: string): AnimationPreset | undefined {
  const allPresets = getAllAnimationPresets()
  
  for (const category of Object.values(allPresets)) {
    const preset = category.find(p => p.name === name)
    if (preset) return preset
  }
  
  return undefined
}

/**
 * Generate CSS for animation presets
 */
export function generateAnimationCSS(): string {
  const allPresets = getAllAnimationPresets()
  let css = '/* KilatAnim - Animation Presets */\n\n'
  
  // Scroll animations
  css += `
/* Scroll Animations */
@keyframes kilat-fade-in-up {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes kilat-fade-in-down {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes kilat-fade-in-left {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes kilat-fade-in-right {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes kilat-slide-in-up {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes kilat-zoom-in {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

/* 3D Animations */
@keyframes kilat-rotate-x {
  from { transform: rotateX(0deg); }
  to { transform: rotateX(360deg); }
}

@keyframes kilat-rotate-y {
  from { transform: rotateY(0deg); }
  to { transform: rotateY(360deg); }
}

@keyframes kilat-flip {
  0% { transform: rotateY(0); }
  50% { transform: rotateY(180deg); }
  100% { transform: rotateY(360deg); }
}

@keyframes kilat-cube {
  0% { transform: rotateX(0deg) rotateY(0deg); }
  25% { transform: rotateX(90deg) rotateY(0deg); }
  50% { transform: rotateX(90deg) rotateY(90deg); }
  75% { transform: rotateX(0deg) rotateY(90deg); }
  100% { transform: rotateX(0deg) rotateY(0deg); }
}

/* Orbit Animations */
@keyframes kilat-orbit {
  from { transform: rotate(0deg) translateX(50px) rotate(0deg); }
  to { transform: rotate(360deg) translateX(50px) rotate(-360deg); }
}

/* Animation Classes */
.kilat-anim-fade-in-up { animation: kilat-fade-in-up 0.6s ease-out; }
.kilat-anim-fade-in-down { animation: kilat-fade-in-down 0.6s ease-out; }
.kilat-anim-fade-in-left { animation: kilat-fade-in-left 0.6s ease-out; }
.kilat-anim-fade-in-right { animation: kilat-fade-in-right 0.6s ease-out; }
.kilat-anim-slide-in-up { animation: kilat-slide-in-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
.kilat-anim-zoom-in { animation: kilat-zoom-in 0.5s ease-out; }

.kilat-anim-rotate-x { animation: kilat-rotate-x 1s ease-in-out; }
.kilat-anim-rotate-y { animation: kilat-rotate-y 1s ease-in-out; }
.kilat-anim-flip { animation: kilat-flip 0.8s ease-in-out; }
.kilat-anim-cube { animation: kilat-cube 2s linear infinite; }

.kilat-anim-orbit-slow { animation: kilat-orbit 10s linear infinite; }
.kilat-anim-orbit-fast { animation: kilat-orbit 3s linear infinite; }
.kilat-anim-orbit-reverse { animation: kilat-orbit 8s linear infinite reverse; }

/* Hover Animations */
.kilat-anim-hover-glow:hover { box-shadow: 0 0 20px currentColor; transition: box-shadow 0.3s ease-out; }
.kilat-anim-hover-scale:hover { transform: scale(1.05); transition: transform 0.2s ease-out; }
.kilat-anim-hover-float:hover { transform: translateY(-5px); transition: transform 0.3s ease-out; }
.kilat-anim-hover-tilt:hover { transform: rotate(2deg); transition: transform 0.3s ease-out; }
`
  
  return css
}
