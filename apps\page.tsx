/**
 * 🏠 Homepage - Enhanced with KilatCSS & KilatAnim
 */

import React from 'react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl kilat-hero-float" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl kilat-hero-pulse" />
      </div>

      {/* Hero Section */}
      <div className="relative">
        <div className="kilat-container">
          <div className="mx-auto max-w-4xl pt-20 pb-32 sm:pt-48 sm:pb-40">
            {/* Badge */}
            <div className="kilat-fade-in text-center mb-8">
              <div className="inline-flex items-center px-4 py-2 rounded-full text-sm kilat-glass text-gray-300 hover:bg-white/20 transition-all duration-300 kilat-glow-hover">
                <span className="mr-2">⚡</span>
                Powered by SpeedRun™ Runtime
                <a href="/about" className="ml-2 text-blue-400 hover:text-blue-300">
                  Learn more →
                </a>
              </div>
            </div>

            {/* Main Heading */}
            <div className="text-center kilat-fade-in" style={{ animationDelay: '0.2s' }}>
              <h1 className="text-5xl md:text-7xl font-bold tracking-tight text-white mb-6">
                Build{' '}
                <span className="kilat-gradient-text kilat-text-glow">
                  Lightning-Fast
                </span>
                <br />
                Apps with Kilat.js
              </h1>

              <p className="text-xl md:text-2xl leading-relaxed text-gray-300 max-w-3xl mx-auto mb-10">
                Modern fullstack framework with SpeedRun™ Runtime, file-based routing,
                streaming SSR, and built-in optimizations. Zero configuration, maximum performance.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 kilat-fade-in" style={{ animationDelay: '0.4s' }}>
                <a
                  href="/dashboard"
                  className="kilat-btn kilat-btn-primary px-8 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300"
                >
                  <span className="mr-2">🚀</span>
                  Get Started
                </a>
                <a
                  href="/about"
                  className="kilat-btn kilat-btn-outline px-8 py-4 text-lg font-semibold hover:kilat-glow transition-all duration-300"
                >
                  Learn More
                  <span className="ml-2">→</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 sm:py-32 relative">
        <div className="kilat-container">
          {/* Section Header */}
          <div className="text-center mb-20 kilat-fade-in">
            <h2 className="text-4xl md:text-5xl font-bold tracking-tight text-white mb-6">
              Everything you need to build{' '}
              <span className="kilat-gradient-text">modern apps</span>
            </h2>
            <p className="text-xl leading-8 text-gray-300 max-w-2xl mx-auto">
              Kilat.js provides all the tools and optimizations you need out of the box.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Feature 1 */}
            <div className="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.1s' }}>
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-bold text-white mb-4">SpeedRun™ Runtime</h3>
              <p className="text-gray-300 leading-relaxed">
                Lightning-fast runtime powered by Bun.js with automatic optimizations and zero cold starts.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.2s' }}>
              <div className="text-4xl mb-4">🗂️</div>
              <h3 className="text-xl font-bold text-white mb-4">Apps Mapping</h3>
              <p className="text-gray-300 leading-relaxed">
                File-based routing system similar to Next.js App Router with layouts and nested routes.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.3s' }}>
              <div className="text-4xl mb-4">🌊</div>
              <h3 className="text-xl font-bold text-white mb-4">Streaming SSR</h3>
              <p className="text-gray-300 leading-relaxed">
                Server-side rendering with streaming, React Server Components, and Suspense support.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.4s' }}>
              <div className="text-4xl mb-4">🎨</div>
              <h3 className="text-xl font-bold text-white mb-4">KilatCSS & KilatAnim</h3>
              <p className="text-gray-300 leading-relaxed">
                Built-in styling system with Tailwind integration and smooth animations out of the box.
              </p>
            </div>

            {/* Feature 5 */}
            <div className="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.5s' }}>
              <div className="text-4xl mb-4">🔌</div>
              <h3 className="text-xl font-bold text-white mb-4">Plugin System</h3>
              <p className="text-gray-300 leading-relaxed">
                Extensible architecture with built-in plugins and easy third-party integration.
              </p>
            </div>

            {/* Feature 6 */}
            <div className="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.6s' }}>
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-xl font-bold text-white mb-4">Production Ready</h3>
              <p className="text-gray-300 leading-relaxed">
                ISR, caching, error handling, OTA updates, and all production optimizations included.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-24 relative">
        <div className="kilat-container text-center">
          <div className="kilat-fade-in">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to build something amazing?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of developers building the next generation of web applications with Kilat.js.
            </p>
            <a
              href="/dashboard"
              className="kilat-btn kilat-btn-primary px-10 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              Start Building Now
              <span className="ml-2">🚀</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
