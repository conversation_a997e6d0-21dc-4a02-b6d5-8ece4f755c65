# ⚡ Kilat.js

**Modern Fullstack Framework with SpeedRun™ Runtime**

Kilat.js adalah framework fullstack modern yang dibangun 100% dari nol tanpa bergantung pada Next.js, Vite, Nuxt, Express, atau Webpack.

## ✨ Features

- ⚡ **SpeedRun™ Runtime** - Built on Bun.js with Node.js fallback
- 🗂️ **Apps Mapping** - File-based routing like Next.js App Router
- 🌊 **Streaming SSR** - React Server Components with Suspense
- 🎨 **KilatCSS** - Built-in Tailwind wrapper with themes
- 🎭 **KilatAnim** - 3D animations and scroll effects
- 🔌 **Plugin System** - Semi-modular architecture
- 🛠️ **CLI Tools** - Complete DX tooling
- 🚀 **Production Ready** - ISR, caching, error handling

## 🚀 Quick Start

```bash
# Install Bun (if not installed)
curl -fsSL https://bun.sh/install | bash

# Create new Kilat.js project
bun create kilat my-app
cd my-app

# Start development server
bun dev
```

## 📁 Project Structure

```
kilat/
├── apps/                  # 🔀 File-based routing
│   ├── layout.tsx         # Global layout
│   ├── page.tsx           # Homepage
│   └── api/               # API routes
├── components/            # 💠 UI Components
├── core/                  # 🧠 Framework core
│   ├── kernel/            # SpeedRun™ runtime
│   ├── systems/           # Internal subsystems
│   └── tools/             # CLI & dev tools
└── public/                # 🖼️ Static assets
```

## 🛠️ CLI Commands

```bash
bun kilat dev              # Start dev server
bun kilat build            # Build for production
bun kilat generate         # Scaffold components
bun kilat graph            # Dependency visualizer
bun kilat upgrade          # OTA framework updates
bun kilat test             # Run tests
```

## 📖 Documentation

Visit [kilat.js.org](https://kilat.js.org) for complete documentation.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md).

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.
