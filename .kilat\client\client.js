/**
 * 🌐 Kilat.js Client - Client-side Features
 */

(function() {
  'use strict';

  // Wait for <PERSON><PERSON> runtime to be ready
  function initClient() {
    if (!window.Kilat || !window.Kilat.initialized) {
      setTimeout(initClient, 50);
      return;
    }

    console.log('🌐 Kilat.js Client initializing...');

    // Client-side navigation
    const navigation = {
      init: function() {
        // Handle navigation links
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a[href^="/"]');
          if (link && !link.hasAttribute('target') && !e.ctrlKey && !e.metaKey) {
            // For now, use regular navigation
            // In future versions, implement SPA navigation
          }
        });
      }
    };

    // Form handling
    const forms = {
      init: function() {
        document.addEventListener('submit', function(e) {
          const form = e.target;
          if (form.hasAttribute('data-kilat-form')) {
            e.preventDefault();
            forms.handleSubmit(form);
          }
        });
      },
      
      handleSubmit: function(form) {
        const formData = new FormData(form);
        const action = form.action || window.location.href;
        const method = form.method || 'POST';
        
        // Show loading state
        const submitBtn = form.querySelector('[type="submit"]');
        if (submitBtn) {
          submitBtn.disabled = true;
          submitBtn.textContent = 'Loading...';
        }
        
        fetch(action, {
          method: method,
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          // Handle response
          if (data.success) {
            window.Kilat.emit('form:success', { form, data });
          } else {
            window.Kilat.emit('form:error', { form, data });
          }
        })
        .catch(error => {
          window.Kilat.emit('form:error', { form, error });
        })
        .finally(() => {
          // Reset loading state
          if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = submitBtn.getAttribute('data-original-text') || 'Submit';
          }
        });
      }
    };

    // Lazy loading
    const lazyLoading = {
      init: function() {
        if ('IntersectionObserver' in window) {
          const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target;
                if (img.dataset.src) {
                  img.src = img.dataset.src;
                  img.removeAttribute('data-src');
                  imageObserver.unobserve(img);
                }
              }
            });
          });

          document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
          });
        }
      }
    };

    // Smooth scrolling
    const smoothScroll = {
      init: function() {
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a[href^="#"]');
          if (link) {
            e.preventDefault();
            const target = document.querySelector(link.getAttribute('href'));
            if (target) {
              target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          }
        });
      }
    };

    // Theme switcher
    const themeSwitcher = {
      init: function() {
        // Create theme switcher if it doesn't exist
        if (!document.querySelector('.kilat-theme-switcher')) {
          this.createSwitcher();
        }
        
        // Handle theme buttons
        document.addEventListener('click', function(e) {
          if (e.target.hasAttribute('data-theme')) {
            const theme = e.target.getAttribute('data-theme');
            window.Kilat.modules.theme.set(theme);
          }
        });
      },
      
      createSwitcher: function() {
        const switcher = document.createElement('div');
        switcher.className = 'kilat-theme-switcher fixed top-4 right-4 z-50 flex gap-2';
        switcher.innerHTML = `
          <button data-theme="default" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm" title="Default Theme">🌙</button>
          <button data-theme="cyber" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm" title="Cyber Theme">🤖</button>
          <button data-theme="nusantara" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm" title="Nusantara Theme">🏛️</button>
        `;
        document.body.appendChild(switcher);
      }
    };

    // Performance monitoring
    const performance = {
      init: function() {
        // Monitor Core Web Vitals
        if ('web-vital' in window) {
          // This would integrate with web-vitals library
        }
        
        // Monitor page load time
        window.addEventListener('load', function() {
          setTimeout(() => {
            const perfData = window.performance.getEntriesByType('navigation')[0];
            if (perfData) {
              console.log('📊 Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
              
              // Send to analytics if available
              if (window.__KILAT_ANALYTICS__) {
                window.__KILAT_ANALYTICS__.track('performance', {
                  loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                  domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart
                });
              }
            }
          }, 0);
        });
      }
    };

    // Initialize all modules
    navigation.init();
    forms.init();
    lazyLoading.init();
    smoothScroll.init();
    themeSwitcher.init();
    performance.init();

    console.log('✅ Kilat.js Client initialized');
    window.Kilat.emit('client:ready');
  }

  // Start initialization
  initClient();

})();
