/**
 * 📄 About Page
 */

import React from 'react'

export default function AboutPage() {
  return (
    <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
      <div className="lg:grid lg:grid-cols-2 lg:gap-8">
        <div>
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            About Kilat.js
          </h2>
          <p className="mt-3 max-w-3xl text-lg text-gray-300">
            Kilat.js is a modern fullstack framework built from scratch with Bun.js and TypeScript.
            It provides a complete solution for building fast, scalable, and maintainable web applications.
          </p>
          <div className="mt-8 space-y-4">
            <h3 className="text-xl font-bold text-white">Key Features</h3>
            <ul className="mt-4 space-y-2">
              <li className="flex items-start">
                <span className="flex-shrink-0 h-6 w-6 text-blue-400">⚡</span>
                <p className="ml-3 text-base text-gray-300">
                  <strong className="font-medium text-white">SpeedRun™ Runtime</strong> - Built on Bun.js with Node.js fallback
                </p>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 h-6 w-6 text-blue-400">🗂️</span>
                <p className="ml-3 text-base text-gray-300">
                  <strong className="font-medium text-white">Apps Mapping</strong> - File-based routing like Next.js App Router
                </p>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 h-6 w-6 text-blue-400">🌊</span>
                <p className="ml-3 text-base text-gray-300">
                  <strong className="font-medium text-white">Streaming SSR</strong> - React Server Components with Suspense
                </p>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 h-6 w-6 text-blue-400">🎨</span>
                <p className="ml-3 text-base text-gray-300">
                  <strong className="font-medium text-white">KilatCSS</strong> - Built-in Tailwind wrapper with themes
                </p>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 h-6 w-6 text-blue-400">🎭</span>
                <p className="ml-3 text-base text-gray-300">
                  <strong className="font-medium text-white">KilatAnim</strong> - 3D animations and scroll effects
                </p>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 h-6 w-6 text-blue-400">🔌</span>
                <p className="ml-3 text-base text-gray-300">
                  <strong className="font-medium text-white">Plugin System</strong> - Semi-modular architecture
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-12 lg:mt-0">
          <div className="bg-gray-800 rounded-lg shadow-xl overflow-hidden">
            <div className="px-6 py-8 sm:p-10">
              <h3 className="text-xl font-medium text-white">Philosophy</h3>
              <div className="mt-4 text-base text-gray-300">
                <p>
                  Kilat.js was created with a simple philosophy: provide a modern, fast, and independent framework
                  that gives developers complete control without the complexity of existing solutions.
                </p>
                <p className="mt-6">
                  Built from scratch without dependencies on Next.js, Vite, Nuxt, Express, or Webpack,
                  Kilat.js offers a fresh approach to web development with the speed of Bun.js,
                  the modularity of Nuxt, the file-based routing of App Router, and the developer experience of Vite.
                </p>
              </div>
              <div className="mt-8">
                <div className="rounded-md shadow">
                  <a
                    href="https://github.com/kilat-js/kilat.js"
                    className="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    View on GitHub
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
