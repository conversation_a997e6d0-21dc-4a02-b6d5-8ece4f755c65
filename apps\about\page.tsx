/**
 * 📄 About Page - Enhanced with KilatCSS & KilatAnim
 */

import React from 'react'

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl kilat-hero-float" />
        <div className="absolute bottom-1/4 left-1/4 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl kilat-hero-pulse" />
      </div>

      <div className="relative kilat-container py-16">
        {/* Header Section */}
        <div className="text-center mb-20 kilat-fade-in">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            About{' '}
            <span className="kilat-gradient-text kilat-text-glow">Kilat.js</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            The next-generation fullstack framework built for speed, developer experience,
            and modern web development with cutting-edge technologies.
          </p>
        </div>
        {/* Features Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          <div className="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.1s' }}>
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-4">⚡</div>
              <h2 className="text-2xl font-bold text-white">Lightning Fast</h2>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Built on Bun.js runtime with SpeedRun™ technology. Experience blazing-fast development
              and production performance that outpaces traditional frameworks by up to 10x.
            </p>
          </div>

          <div className="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.2s' }}>
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-4">🗂️</div>
              <h2 className="text-2xl font-bold text-white">File-based Routing</h2>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Apps Mapping system provides intuitive file-based routing similar to Next.js App Router,
              with support for layouts, middleware, and nested routes for complex applications.
            </p>
          </div>

          <div className="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.3s' }}>
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-4">🌊</div>
              <h2 className="text-2xl font-bold text-white">Streaming SSR</h2>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Advanced server-side rendering with React 18 features including Suspense,
              streaming, and React Server Components for optimal performance and SEO.
            </p>
          </div>

          <div className="kilat-card kilat-glass p-8 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.4s' }}>
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-4">🎨</div>
              <h2 className="text-2xl font-bold text-white">Built-in UI Systems</h2>
            </div>
            <p className="text-gray-300 leading-relaxed">
              KilatCSS and KilatAnim provide beautiful styling and smooth animations
              out of the box, with Tailwind integration and cyberpunk/nusantara themes.
            </p>
          </div>
        </div>

        {/* Technology Stack */}
        <div className="text-center mb-20 kilat-fade-in" style={{ animationDelay: '0.5s' }}>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-12">
            Built with Modern{' '}
            <span className="kilat-gradient-text">Technologies</span>
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="kilat-card kilat-glass p-6 text-center kilat-glow-hover">
              <div className="text-2xl mb-2">🟡</div>
              <h3 className="font-bold text-white">Bun.js</h3>
              <p className="text-sm text-gray-400">Runtime</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-glow-hover">
              <div className="text-2xl mb-2">⚛️</div>
              <h3 className="font-bold text-white">React 18</h3>
              <p className="text-sm text-gray-400">UI Library</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-glow-hover">
              <div className="text-2xl mb-2">🎨</div>
              <h3 className="font-bold text-white">Tailwind</h3>
              <p className="text-sm text-gray-400">CSS Framework</p>
            </div>

            <div className="kilat-card kilat-glass p-6 text-center kilat-glow-hover">
              <div className="text-2xl mb-2">📦</div>
              <h3 className="font-bold text-white">TypeScript</h3>
              <p className="text-sm text-gray-400">Type Safety</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center kilat-fade-in" style={{ animationDelay: '0.6s' }}>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">
            Ready to experience the future?
          </h2>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/dashboard"
              className="kilat-btn kilat-btn-primary px-8 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              <span className="mr-2">📊</span>
              Try Dashboard
            </a>
            <a
              href="/"
              className="kilat-btn kilat-btn-outline px-8 py-4 text-lg font-semibold hover:kilat-glow transition-all duration-300"
            >
              <span className="mr-2">🏠</span>
              Back to Home
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
