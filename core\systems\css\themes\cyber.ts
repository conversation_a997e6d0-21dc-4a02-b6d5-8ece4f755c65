/**
 * 🤖 Cyber Theme - Matrix-inspired Dark Theme
 */

export const cyberTheme = {
  name: 'cyber',
  displayName: 'Cyber',
  description: 'Matrix-inspired dark theme with green accents',
  
  colors: {
    // Base colors
    background: {
      primary: '#000000',
      secondary: '#0d1117',
      tertiary: '#161b22',
    },
    
    // Text colors
    text: {
      primary: '#00ff41',
      secondary: '#58a6ff',
      muted: '#7d8590',
      accent: '#00ff41',
    },
    
    // Brand colors
    brand: {
      primary: '#00ff41',
      secondary: '#39ff14',
      tertiary: '#00cc33',
    },
    
    // Semantic colors
    success: '#00ff41',
    warning: '#ffcc00',
    error: '#ff4444',
    info: '#00ccff',
    
    // Glow effects
    glow: {
      green: '#00ff41',
      blue: '#00ccff',
      yellow: '#ffcc00',
      red: '#ff4444',
    },
  },
  
  // CSS custom properties
  cssVariables: {
    '--theme-bg-primary': '#000000',
    '--theme-bg-secondary': '#0d1117',
    '--theme-bg-tertiary': '#161b22',
    '--theme-text-primary': '#00ff41',
    '--theme-text-secondary': '#58a6ff',
    '--theme-text-muted': '#7d8590',
    '--theme-text-accent': '#00ff41',
    '--theme-brand-primary': '#00ff41',
    '--theme-brand-secondary': '#39ff14',
    '--theme-brand-tertiary': '#00cc33',
    '--theme-success': '#00ff41',
    '--theme-warning': '#ffcc00',
    '--theme-error': '#ff4444',
    '--theme-info': '#00ccff',
    '--theme-glow-green': '#00ff41',
    '--theme-glow-blue': '#00ccff',
    '--theme-glow-yellow': '#ffcc00',
    '--theme-glow-red': '#ff4444',
  },
  
  // Component styles
  components: {
    button: {
      primary: 'bg-brand-primary text-black hover:shadow-[0_0_20px_var(--theme-glow-green)]',
      secondary: 'bg-brand-secondary text-black hover:shadow-[0_0_20px_var(--theme-glow-green)]',
      ghost: 'text-brand-primary border border-brand-primary hover:bg-brand-primary hover:text-black',
    },
    
    card: {
      default: 'bg-bg-secondary border border-brand-primary/20 shadow-[0_0_10px_var(--theme-glow-green)/20]',
      hover: 'hover:shadow-[0_0_20px_var(--theme-glow-green)/40] hover:border-brand-primary/40',
    },
    
    input: {
      default: 'bg-bg-tertiary border border-brand-primary/30 focus:border-brand-primary focus:shadow-[0_0_10px_var(--theme-glow-green)/50]',
    },
  },
  
  // Animation presets
  animations: {
    glow: 'animate-kilat-glow',
    float: 'animate-kilat-float',
    pulse: 'animate-kilat-pulse',
  },
}

export default cyberTheme
