/**
 * ✅ API Validator - Request/Response Validation System
 */

export interface ValidationRule {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'email' | 'url' | 'uuid'
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  enum?: any[]
  custom?: (value: any) => boolean | string
}

export interface ValidationSchema {
  [key: string]: ValidationRule | ValidationSchema
}

export interface ValidationError {
  field: string
  message: string
  value?: any
}

export interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
  data?: any
}

class ApiValidator {
  /**
   * Validate data against schema
   */
  validate(data: any, schema: ValidationSchema): ValidationResult {
    const errors: ValidationError[] = []
    const validatedData: any = {}
    
    // Check for required fields
    for (const [field, rule] of Object.entries(schema)) {
      const value = data[field]
      const fieldRule = rule as ValidationRule
      
      // Check if field is required
      if (fieldRule.required && (value === undefined || value === null)) {
        errors.push({
          field,
          message: `Field '${field}' is required`,
          value
        })
        continue
      }
      
      // Skip validation if field is not provided and not required
      if (value === undefined || value === null) {
        continue
      }
      
      // Validate field
      const fieldErrors = this.validateField(field, value, fieldRule)
      errors.push(...fieldErrors)
      
      if (fieldErrors.length === 0) {
        validatedData[field] = this.transformValue(value, fieldRule)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      data: errors.length === 0 ? validatedData : undefined
    }
  }
  
  /**
   * Validate individual field
   */
  private validateField(field: string, value: any, rule: ValidationRule): ValidationError[] {
    const errors: ValidationError[] = []
    
    // Type validation
    if (!this.validateType(value, rule.type)) {
      errors.push({
        field,
        message: `Field '${field}' must be of type ${rule.type}`,
        value
      })
      return errors
    }
    
    // String validations
    if (rule.type === 'string' && typeof value === 'string') {
      if (rule.min && value.length < rule.min) {
        errors.push({
          field,
          message: `Field '${field}' must be at least ${rule.min} characters`,
          value
        })
      }
      
      if (rule.max && value.length > rule.max) {
        errors.push({
          field,
          message: `Field '${field}' must be at most ${rule.max} characters`,
          value
        })
      }
      
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push({
          field,
          message: `Field '${field}' does not match required pattern`,
          value
        })
      }
    }
    
    // Number validations
    if (rule.type === 'number' && typeof value === 'number') {
      if (rule.min && value < rule.min) {
        errors.push({
          field,
          message: `Field '${field}' must be at least ${rule.min}`,
          value
        })
      }
      
      if (rule.max && value > rule.max) {
        errors.push({
          field,
          message: `Field '${field}' must be at most ${rule.max}`,
          value
        })
      }
    }
    
    // Array validations
    if (rule.type === 'array' && Array.isArray(value)) {
      if (rule.min && value.length < rule.min) {
        errors.push({
          field,
          message: `Field '${field}' must have at least ${rule.min} items`,
          value
        })
      }
      
      if (rule.max && value.length > rule.max) {
        errors.push({
          field,
          message: `Field '${field}' must have at most ${rule.max} items`,
          value
        })
      }
    }
    
    // Enum validation
    if (rule.enum && !rule.enum.includes(value)) {
      errors.push({
        field,
        message: `Field '${field}' must be one of: ${rule.enum.join(', ')}`,
        value
      })
    }
    
    // Custom validation
    if (rule.custom) {
      const customResult = rule.custom(value)
      if (customResult !== true) {
        errors.push({
          field,
          message: typeof customResult === 'string' ? customResult : `Field '${field}' failed custom validation`,
          value
        })
      }
    }
    
    return errors
  }
  
  /**
   * Validate type
   */
  private validateType(value: any, type: ValidationRule['type']): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string'
      case 'number':
        return typeof value === 'number' && !isNaN(value)
      case 'boolean':
        return typeof value === 'boolean'
      case 'array':
        return Array.isArray(value)
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value)
      case 'email':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      case 'url':
        try {
          new URL(value)
          return true
        } catch {
          return false
        }
      case 'uuid':
        return typeof value === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value)
      default:
        return true
    }
  }
  
  /**
   * Transform value based on type
   */
  private transformValue(value: any, rule: ValidationRule): any {
    switch (rule.type) {
      case 'number':
        return typeof value === 'string' ? parseFloat(value) : value
      case 'boolean':
        return typeof value === 'string' ? value === 'true' : value
      default:
        return value
    }
  }
}

// Global validator instance
const validator = new ApiValidator()

/**
 * Validate request body
 */
export function validateBody(data: any, schema: ValidationSchema): ValidationResult {
  return validator.validate(data, schema)
}

/**
 * Validate query parameters
 */
export function validateQuery(searchParams: URLSearchParams, schema: ValidationSchema): ValidationResult {
  const data: Record<string, any> = {}
  
  for (const [key, value] of searchParams.entries()) {
    data[key] = value
  }
  
  return validator.validate(data, schema)
}

/**
 * Create validation middleware
 */
export function createValidationMiddleware(schema: ValidationSchema, target: 'body' | 'query' = 'body') {
  return async (request: Request): Promise<Response | null> => {
    try {
      let data: any
      
      if (target === 'body') {
        const contentType = request.headers.get('content-type') || ''
        
        if (contentType.includes('application/json')) {
          data = await request.json()
        } else if (contentType.includes('application/x-www-form-urlencoded')) {
          const formData = await request.formData()
          data = Object.fromEntries(formData.entries())
        } else {
          return new Response(JSON.stringify({
            error: 'Unsupported content type',
            message: 'Expected application/json or application/x-www-form-urlencoded'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          })
        }
      } else {
        const url = new URL(request.url)
        data = Object.fromEntries(url.searchParams.entries())
      }
      
      const result = validator.validate(data, schema)
      
      if (!result.valid) {
        return new Response(JSON.stringify({
          error: 'Validation failed',
          errors: result.errors
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        })
      }
      
      // Attach validated data to request
      ;(request as any).validatedData = result.data
      
      return null // Continue to next middleware/handler
      
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Invalid request data',
        message: (error as Error).message
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }
  }
}

export default validator
