/**
 * 🛡️ Dashboard Middleware
 * Authentication and authorization for dashboard routes
 */

import type { KilatContext, MiddlewareConfig } from '../../core/types'

/**
 * Dashboard authentication middleware
 */
export default async function dashboardMiddleware(context: KilatContext): Promise<void> {
  const { request, pathname } = context
  
  console.log(`🛡️ Dashboard middleware: ${pathname}`)

  // Check for authentication
  const authHeader = request.headers.get('Authorization')
  const sessionCookie = getCookie(request, 'kilat-session')

  // Skip auth for login/register pages
  if (pathname?.includes('/login') || pathname?.includes('/register')) {
    return
  }

  // Check authentication
  if (!authHeader && !sessionCookie) {
    // Redirect to login
    throw new Response(null, {
      status: 302,
      headers: {
        'Location': '/login?redirect=' + encodeURIComponent(pathname || '/dashboard')
      }
    })
  }

  // Validate session (in real app, check against database)
  if (sessionCookie && !sessionCookie.startsWith('demo-session-')) {
    throw new Response(null, {
      status: 302,
      headers: {
        'Location': '/login?error=invalid_session'
      }
    })
  }

  // Add user info to context
  if (sessionCookie) {
    context.user = {
      id: '1',
      name: 'Demo User',
      email: '<EMAIL>'
    }
  }

  // Log access
  console.log(`✅ Dashboard access granted: ${context.user?.email || 'unknown'}`)
}

/**
 * Get cookie value from request
 */
function getCookie(request: Request, name: string): string | null {
  const cookieHeader = request.headers.get('Cookie')
  if (!cookieHeader) return null

  const cookies = cookieHeader.split(';').map(c => c.trim())
  const cookie = cookies.find(c => c.startsWith(name + '='))
  
  return cookie ? cookie.split('=')[1] : null
}

/**
 * Middleware configuration
 */
export const config: MiddlewareConfig = {
  matcher: ['/dashboard/:path*'],
  runtime: 'nodejs'
}
