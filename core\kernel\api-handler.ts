/**
 * 🛠️ API Route Handler
 */

import type { KilatContext } from '../../types/config'
import { loadModule } from './esm-loader'
import { join } from 'path'

export interface ApiRoute {
  GET?: (request: Request) => Promise<Response> | Response
  POST?: (request: Request) => Promise<Response> | Response
  PUT?: (request: Request) => Promise<Response> | Response
  DELETE?: (request: Request) => Promise<Response> | Response
  PATCH?: (request: Request) => Promise<Response> | Response
  HEAD?: (request: Request) => Promise<Response> | Response
  OPTIONS?: (request: Request) => Promise<Response> | Response
}

/**
 * Handle API route request
 */
export async function handleApiRoute(
  request: Request,
  context: KilatContext
): Promise<Response> {
  try {
    const url = new URL(request.url)
    const pathname = url.pathname
    
    // Convert API path to file path
    const apiPath = pathname.replace('/api/', '')
    const routeFilePath = join(context.config.apps.dir, 'api', apiPath, 'route.ts')
    
    // Load API route module
    const routeModule = await loadModule(routeFilePath)
    const apiRoute: ApiRoute = routeModule.default || routeModule
    
    // Get handler for HTTP method
    const method = request.method as keyof ApiRoute
    const handler = apiRoute[method]
    
    if (!handler) {
      return new Response(`Method ${method} Not Allowed`, {
        status: 405,
        headers: {
          'Allow': Object.keys(apiRoute).join(', '),
          'Content-Type': 'text/plain'
        }
      })
    }
    
    // Execute handler
    const response = await handler(request)
    
    // Ensure response has CORS headers in development
    if (context.mode === 'development') {
      response.headers.set('Access-Control-Allow-Origin', '*')
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS')
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    }
    
    return response
    
  } catch (error) {
    console.error('❌ API route error:', error)
    
    // Return error response
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        message: context.mode === 'development' ? (error as Error).message : 'Something went wrong'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}

/**
 * Create API response helpers
 */
export const ApiResponse = {
  json(data: any, status = 200): Response {
    return new Response(JSON.stringify(data), {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  },
  
  text(text: string, status = 200): Response {
    return new Response(text, {
      status,
      headers: {
        'Content-Type': 'text/plain'
      }
    })
  },
  
  html(html: string, status = 200): Response {
    return new Response(html, {
      status,
      headers: {
        'Content-Type': 'text/html'
      }
    })
  },
  
  redirect(url: string, status = 302): Response {
    return new Response(null, {
      status,
      headers: {
        'Location': url
      }
    })
  },
  
  error(message: string, status = 400): Response {
    return new Response(JSON.stringify({ error: message }), {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
