/**
 * 🛠️ API Route Handler
 */

import type { KilatContext } from '../../types/config'
import { loadModule } from './esm-loader'
import { join } from 'path'

export interface ApiRoute {
  GET?: (request: Request) => Promise<Response> | Response
  POST?: (request: Request) => Promise<Response> | Response
  PUT?: (request: Request) => Promise<Response> | Response
  DELETE?: (request: Request) => Promise<Response> | Response
  PATCH?: (request: Request) => Promise<Response> | Response
  HEAD?: (request: Request) => Promise<Response> | Response
  OPTIONS?: (request: Request) => Promise<Response> | Response
}

/**
 * Handle API route request with middleware support
 */
export async function handleApiRoute(
  request: Request,
  context: KilatContext
): Promise<Response> {
  try {
    // Execute middlewares first
    const { executeMiddlewares } = await import('../systems/api/middleware')
    const middlewareResponse = await executeMiddlewares(request)
    if (middlewareResponse) {
      return addResponseHeaders(middlewareResponse, request)
    }

    const url = new URL(request.url)
    const pathname = url.pathname

    // Convert API path to file path
    const apiPath = pathname.replace('/api/', '')
    const routeFilePath = join(context.config.apps.dir, 'api', apiPath, 'route.ts')

    // Load API route module
    const routeModule = await loadModule(routeFilePath)
    const apiRoute: ApiRoute = routeModule.default || routeModule

    // Get handler for HTTP method
    const method = request.method as keyof ApiRoute
    const handler = apiRoute[method]

    if (!handler) {
      const errorResponse = new Response(`Method ${method} Not Allowed`, {
        status: 405,
        headers: {
          'Allow': Object.keys(apiRoute).join(', '),
          'Content-Type': 'text/plain'
        }
      })
      return addResponseHeaders(errorResponse, request)
    }

    // Execute handler
    const response = await handler(request)

    // Add middleware headers and return
    return addResponseHeaders(response, request)

  } catch (error) {
    console.error('❌ API route error:', error)

    // Return error response
    const errorResponse = new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        message: context.mode === 'development' ? (error as Error).message : 'Something went wrong'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

    return addResponseHeaders(errorResponse, request)
  }
}

/**
 * Add middleware headers to response
 */
function addResponseHeaders(response: Response, request: Request): Response {
  const headers = new Headers(response.headers)

  // Add CORS headers
  const corsHeaders = (request as any).corsHeaders
  if (corsHeaders) {
    Object.entries(corsHeaders).forEach(([key, value]) => {
      headers.set(key, value as string)
    })
  }

  // Add security headers
  const securityHeaders = (request as any).securityHeaders
  if (securityHeaders) {
    Object.entries(securityHeaders).forEach(([key, value]) => {
      headers.set(key, value as string)
    })
  }

  // Add rate limit headers
  const rateLimitInfo = (request as any).rateLimitInfo
  if (rateLimitInfo) {
    headers.set('X-RateLimit-Limit', rateLimitInfo.totalHitsPerWindow.toString())
    headers.set('X-RateLimit-Remaining', rateLimitInfo.remaining.toString())
    headers.set('X-RateLimit-Reset', Math.ceil(rateLimitInfo.resetTime.getTime() / 1000).toString())
  }

  // Add response time header
  const startTime = (request as any).startTime
  if (startTime) {
    const responseTime = Date.now() - startTime
    headers.set('X-Response-Time', `${responseTime}ms`)
  }

  // Add Kilat.js headers
  headers.set('X-Powered-By', 'Kilat.js')
  headers.set('X-Kilat-Version', '1.0.0')

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  })
}

/**
 * Create API response helpers
 */
export const ApiResponse = {
  json(data: any, status = 200): Response {
    return new Response(JSON.stringify(data), {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  },
  
  text(text: string, status = 200): Response {
    return new Response(text, {
      status,
      headers: {
        'Content-Type': 'text/plain'
      }
    })
  },
  
  html(html: string, status = 200): Response {
    return new Response(html, {
      status,
      headers: {
        'Content-Type': 'text/html'
      }
    })
  },
  
  redirect(url: string, status = 302): Response {
    return new Response(null, {
      status,
      headers: {
        'Location': url
      }
    })
  },
  
  error(message: string, status = 400): Response {
    return new Response(JSON.stringify({ error: message }), {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
