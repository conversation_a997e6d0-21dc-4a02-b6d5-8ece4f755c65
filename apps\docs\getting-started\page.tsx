/**
 * 📚 Getting Started Documentation Page
 */

import React from 'react'
import type { GenerateMetadataResult } from '../../../core/types'

export default function GettingStartedPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="py-20">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto text-center kilat-fade-in">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              🚀 Getting Started with <span className="kilat-gradient-text">Kilat.js</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Learn how to build lightning-fast applications with Kilat.js framework and SpeedRun™ Runtime.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <span className="px-4 py-2 kilat-glass rounded-full text-sm text-green-400">
                ⚡ 3x Faster than Next.js
              </span>
              <span className="px-4 py-2 kilat-glass rounded-full text-sm text-blue-400">
                🎨 Built-in UI System
              </span>
              <span className="px-4 py-2 kilat-glass rounded-full text-sm text-purple-400">
                🔧 Zero Configuration
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Installation */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <div className="kilat-card kilat-glass p-8 kilat-fade-in">
              <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
                <span className="mr-3">📦</span>
                Installation
              </h2>
              
              <p className="text-gray-300 mb-6">
                Get started with Kilat.js in seconds using our CLI tool:
              </p>
              
              <div className="bg-gray-900 rounded-lg p-6 mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-400">Terminal</span>
                  <button className="text-xs text-blue-400 hover:text-blue-300">Copy</button>
                </div>
                <code className="text-green-400 font-mono">
                  # Create new Kilat.js project<br/>
                  bun create kilat-app my-app<br/><br/>
                  # Navigate to project<br/>
                  cd my-app<br/><br/>
                  # Start development server<br/>
                  bun run dev
                </code>
              </div>
              
              <div className="kilat-glass p-4 rounded-lg">
                <p className="text-sm text-gray-300">
                  <span className="text-yellow-400">💡 Tip:</span> Make sure you have Bun.js installed. 
                  If not, install it from <a href="https://bun.sh" className="text-blue-400 hover:text-blue-300">bun.sh</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Project Structure */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <div className="kilat-card kilat-glass p-8 kilat-fade-in">
              <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
                <span className="mr-3">📁</span>
                Project Structure
              </h2>
              
              <div className="bg-gray-900 rounded-lg p-6">
                <pre className="text-sm text-gray-300 font-mono">
{`my-app/
├── apps/                    # File-based routing
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Homepage
│   ├── about/
│   │   └── page.tsx        # About page
│   ├── blog/
│   │   ├── page.tsx        # Blog index
│   │   └── [slug]/
│   │       └── page.tsx    # Dynamic blog post
│   └── api/
│       └── users/
│           └── route.ts    # API endpoint
├── components/             # Reusable components
│   ├── Header.tsx
│   └── Footer.tsx
├── public/                 # Static assets
├── core/                   # Framework core
├── kilat.config.ts         # Configuration
└── package.json`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* First Steps */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">
              🎯 Your First Steps
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="kilat-card kilat-glass p-6 kilat-glow-hover">
                <div className="text-2xl mb-4">🎨</div>
                <h3 className="text-xl font-bold text-white mb-3">Customize Your App</h3>
                <p className="text-gray-300 mb-4">
                  Edit <code className="bg-gray-800 px-2 py-1 rounded">apps/page.tsx</code> to customize your homepage.
                </p>
                <a href="/docs/routing" className="text-blue-400 hover:text-blue-300 font-medium">
                  Learn about routing →
                </a>
              </div>
              
              <div className="kilat-card kilat-glass p-6 kilat-glow-hover">
                <div className="text-2xl mb-4">🛠️</div>
                <h3 className="text-xl font-bold text-white mb-3">Add API Routes</h3>
                <p className="text-gray-300 mb-4">
                  Create API endpoints in <code className="bg-gray-800 px-2 py-1 rounded">apps/api/</code> directory.
                </p>
                <a href="/docs/api" className="text-blue-400 hover:text-blue-300 font-medium">
                  API documentation →
                </a>
              </div>
              
              <div className="kilat-card kilat-glass p-6 kilat-glow-hover">
                <div className="text-2xl mb-4">🎭</div>
                <h3 className="text-xl font-bold text-white mb-3">Style with KilatCSS</h3>
                <p className="text-gray-300 mb-4">
                  Use built-in KilatCSS classes for beautiful, responsive designs.
                </p>
                <a href="/docs/styling" className="text-blue-400 hover:text-blue-300 font-medium">
                  Styling guide →
                </a>
              </div>
              
              <div className="kilat-card kilat-glass p-6 kilat-glow-hover">
                <div className="text-2xl mb-4">🚀</div>
                <h3 className="text-xl font-bold text-white mb-3">Deploy Your App</h3>
                <p className="text-gray-300 mb-4">
                  Deploy to Vercel, Netlify, or any platform with one command.
                </p>
                <a href="/docs/deployment" className="text-blue-400 hover:text-blue-300 font-medium">
                  Deployment guide →
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Next Steps */}
      <section className="py-16">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto text-center">
            <div className="kilat-card kilat-glass p-8">
              <h2 className="text-3xl font-bold text-white mb-6">
                🎉 Ready to Build Amazing Apps?
              </h2>
              <p className="text-gray-300 mb-8">
                You're all set! Start building with Kilat.js and experience the power of SpeedRun™ Runtime.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a href="/docs/routing" className="kilat-btn kilat-btn-primary px-6 py-3">
                  📚 Read Documentation
                </a>
                <a href="/docs/examples" className="kilat-btn kilat-btn-outline px-6 py-3">
                  💡 View Examples
                </a>
                <a href="/community/discord" className="kilat-btn kilat-btn-outline px-6 py-3">
                  💬 Join Community
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata(): Promise<GenerateMetadataResult> {
  return {
    title: 'Getting Started | Kilat.js Documentation',
    description: 'Learn how to build lightning-fast applications with Kilat.js framework and SpeedRun™ Runtime. Complete setup guide and first steps.',
    keywords: ['kilat.js', 'getting started', 'tutorial', 'setup', 'installation'],
    openGraph: {
      title: 'Getting Started with Kilat.js',
      description: 'Complete guide to building lightning-fast applications with Kilat.js framework.',
      images: ['/api/og?title=Getting Started with Kilat.js']
    }
  }
}
