/**
 * 📜 KilatAnim - Scroll-based Animations
 */

import React, { useEffect, useRef, useState } from 'react'

export interface ScrollAnimationProps {
  children: React.ReactNode
  animation?: string
  threshold?: number
  delay?: number
  duration?: number
  once?: boolean
  className?: string
}

/**
 * Scroll Animation Component
 */
export function ScrollAnimation({
  children,
  animation = 'fadeInUp',
  threshold = 0.1,
  delay = 0,
  duration = 600,
  once = true,
  className = '',
}: ScrollAnimationProps) {
  const elementRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [hasAnimated, setHasAnimated] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          if (!hasAnimated || !once) {
            setTimeout(() => {
              setIsVisible(true)
              if (once) setHasAnimated(true)
            }, delay)
          }
        } else if (!once) {
          setIsVisible(false)
        }
      },
      { threshold }
    )

    observer.observe(element)

    return () => observer.disconnect()
  }, [threshold, delay, once, hasAnimated])

  const animationClass = `kilat-anim-${animation}`
  const visibilityClass = isVisible ? animationClass : 'opacity-0'

  return (
    <div
      ref={elementRef}
      className={`${visibilityClass} ${className}`}
      style={{
        animationDuration: `${duration}ms`,
        animationFillMode: 'both',
      }}
    >
      {children}
    </div>
  )
}

/**
 * Scroll Trigger Hook
 */
export function useScrollTrigger(threshold = 0.1, once = true) {
  const elementRef = useRef<HTMLElement>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [hasTriggered, setHasTriggered] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          if (!hasTriggered || !once) {
            setIsVisible(true)
            if (once) setHasTriggered(true)
          }
        } else if (!once) {
          setIsVisible(false)
        }
      },
      { threshold }
    )

    observer.observe(element)

    return () => observer.disconnect()
  }, [threshold, once, hasTriggered])

  return { elementRef, isVisible }
}

/**
 * Stagger Animation Component
 */
export interface StaggerAnimationProps {
  children: React.ReactNode[]
  animation?: string
  staggerDelay?: number
  threshold?: number
  className?: string
}

export function StaggerAnimation({
  children,
  animation = 'fadeInUp',
  staggerDelay = 100,
  threshold = 0.1,
  className = '',
}: StaggerAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold }
    )

    observer.observe(container)

    return () => observer.disconnect()
  }, [threshold])

  return (
    <div ref={containerRef} className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className={isVisible ? `kilat-anim-${animation}` : 'opacity-0'}
          style={{
            animationDelay: `${index * staggerDelay}ms`,
            animationFillMode: 'both',
          }}
        >
          {child}
        </div>
      ))}
    </div>
  )
}

/**
 * Parallax Scroll Component
 */
export interface ParallaxScrollProps {
  children: React.ReactNode
  speed?: number
  className?: string
}

export function ParallaxScroll({
  children,
  speed = 0.5,
  className = '',
}: ParallaxScrollProps) {
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const handleScroll = () => {
      const rect = element.getBoundingClientRect()
      const scrolled = window.pageYOffset
      const rate = scrolled * -speed

      element.style.transform = `translateY(${rate}px)`
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [speed])

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  )
}

/**
 * Scroll Progress Component
 */
export interface ScrollProgressProps {
  className?: string
  color?: string
  height?: number
}

export function ScrollProgress({
  className = '',
  color = '#00d4ff',
  height = 3,
}: ScrollProgressProps) {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrolled = window.pageYOffset
      const progress = (scrolled / totalHeight) * 100

      setProgress(Math.min(progress, 100))
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div
      className={`fixed top-0 left-0 z-50 transition-all duration-300 ${className}`}
      style={{
        width: `${progress}%`,
        height: `${height}px`,
        backgroundColor: color,
        boxShadow: `0 0 10px ${color}`,
      }}
    />
  )
}

export default ScrollAnimation
