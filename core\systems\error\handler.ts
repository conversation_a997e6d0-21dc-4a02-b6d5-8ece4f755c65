/**
 * 🚨 Erro<PERSON>
 */

import type { KilatContext } from '../../../types/config'

/**
 * Setup global error handling
 */
export function setupErrorHandling(context: KilatContext): void {
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error)
    
    if (context.mode === 'development') {
      // In development, log and continue
      console.error('Stack:', error.stack)
    } else {
      // In production, log and exit gracefully
      console.error('Application will exit due to uncaught exception')
      process.exit(1)
    }
  })
  
  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
    
    if (context.mode === 'production') {
      console.error('Application will exit due to unhandled rejection')
      process.exit(1)
    }
  })
  
  console.log('✅ Error handling setup complete')
}
