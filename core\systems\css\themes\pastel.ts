/**
 * 🌸 Pastel Theme - Soft and Modern Light Theme
 */

export const pastelTheme = {
  name: 'pastel',
  displayName: 'Pastel',
  description: 'Soft and modern light theme with pastel colors',
  
  colors: {
    // Base colors
    background: {
      primary: '#fefefe',
      secondary: '#f8fafc',
      tertiary: '#f1f5f9',
    },
    
    // Text colors
    text: {
      primary: '#1e293b',
      secondary: '#475569',
      muted: '#94a3b8',
      accent: '#8b5cf6',
    },
    
    // Brand colors
    brand: {
      primary: '#8b5cf6',
      secondary: '#ec4899',
      tertiary: '#06b6d4',
    },
    
    // Semantic colors
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    
    // Soft effects
    soft: {
      purple: '#8b5cf6',
      pink: '#ec4899',
      blue: '#06b6d4',
      green: '#10b981',
      orange: '#f59e0b',
    },
  },
  
  // CSS custom properties
  cssVariables: {
    '--theme-bg-primary': '#fefefe',
    '--theme-bg-secondary': '#f8fafc',
    '--theme-bg-tertiary': '#f1f5f9',
    '--theme-text-primary': '#1e293b',
    '--theme-text-secondary': '#475569',
    '--theme-text-muted': '#94a3b8',
    '--theme-text-accent': '#8b5cf6',
    '--theme-brand-primary': '#8b5cf6',
    '--theme-brand-secondary': '#ec4899',
    '--theme-brand-tertiary': '#06b6d4',
    '--theme-success': '#10b981',
    '--theme-warning': '#f59e0b',
    '--theme-error': '#ef4444',
    '--theme-info': '#3b82f6',
    '--theme-soft-purple': '#8b5cf6',
    '--theme-soft-pink': '#ec4899',
    '--theme-soft-blue': '#06b6d4',
    '--theme-soft-green': '#10b981',
    '--theme-soft-orange': '#f59e0b',
  },
  
  // Component styles
  components: {
    button: {
      primary: 'bg-brand-primary text-white hover:bg-brand-primary/90 shadow-lg',
      secondary: 'bg-brand-secondary text-white hover:bg-brand-secondary/90 shadow-lg',
      ghost: 'text-brand-primary border border-brand-primary hover:bg-brand-primary hover:text-white',
    },
    
    card: {
      default: 'bg-bg-secondary border border-gray-200 shadow-sm',
      hover: 'hover:shadow-md hover:border-brand-primary/30',
    },
    
    input: {
      default: 'bg-white border border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20',
    },
  },
  
  // Animation presets
  animations: {
    float: 'animate-kilat-float',
    fade: 'animate-kilat-fade-in',
    slide: 'animate-kilat-slide-up',
  },
}

export default pastelTheme
