/**
 * 🔑 Login API Endpoint
 */

export async function POST(request: Request): Promise<Response> {
  try {
    const body = await request.json()
    const { email, password, remember } = body

    // Validate input
    if (!email || !password) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Email and password are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Demo authentication (in real app, check against database)
    if (email === '<EMAIL>' && password === 'demo123') {
      // Create session token (in real app, use proper JWT or session management)
      const sessionToken = 'demo-session-' + Date.now()
      
      const user = {
        id: '1',
        name: 'Demo User',
        email: '<EMAIL>',
        avatar: 'DU'
      }

      // Set session cookie
      const headers = new Headers({
        'Content-Type': 'application/json',
        'Set-Cookie': `kilat-session=${sessionToken}; HttpOnly; Path=/; ${remember ? 'Max-Age=2592000;' : ''} SameSite=Strict`
      })

      return new Response(JSON.stringify({
        success: true,
        message: 'Login successful',
        user,
        redirectTo: '/dashboard'
      }), {
        status: 200,
        headers
      })
    }

    // Invalid credentials
    return new Response(JSON.stringify({
      success: false,
      error: 'Invalid email or password'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Login error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

// Handle form submission
export async function handleFormSubmission(formData: FormData): Promise<Response> {
  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const remember = formData.get('remember') === 'on'

  return POST(new Request('http://localhost/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password, remember })
  }))
}
