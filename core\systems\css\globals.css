/**
 * 🎨 KilatCSS - Global Styles
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * Base styles
 */
@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    
    --radius: 0.5rem;
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/**
 * KilatCSS Components
 */
@layer components {
  /* Buttons */
  .kilat-btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors 
           focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }
  
  .kilat-btn-primary {
    @apply kilat-btn bg-primary text-primary-foreground shadow hover:bg-primary/90;
  }
  
  .kilat-btn-secondary {
    @apply kilat-btn bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80;
  }
  
  .kilat-btn-outline {
    @apply kilat-btn border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground;
  }
  
  .kilat-btn-ghost {
    @apply kilat-btn hover:bg-accent hover:text-accent-foreground;
  }
  
  .kilat-btn-link {
    @apply kilat-btn text-primary underline-offset-4 hover:underline;
  }
  
  /* Cards */
  .kilat-card {
    @apply rounded-lg border bg-card text-card-foreground shadow;
  }
  
  .kilat-card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .kilat-card-title {
    @apply font-semibold leading-none tracking-tight;
  }
  
  .kilat-card-description {
    @apply text-sm text-muted-foreground;
  }
  
  .kilat-card-content {
    @apply p-6 pt-0;
  }
  
  .kilat-card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  /* Inputs */
  .kilat-input {
    @apply flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm
           transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium
           placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1
           focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Layout */
  .kilat-container {
    @apply mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8;
  }
}

/**
 * KilatCSS Utilities
 */
@layer utilities {
  .kilat-glow {
    @apply relative;
  }
  
  .kilat-glow::after {
    @apply absolute inset-0 -z-10 rounded-lg opacity-0 transition-opacity content-[''];
    box-shadow: 0 0 25px 3px theme('colors.primary.500');
  }
  
  .kilat-glow:hover::after {
    @apply opacity-100;
  }
  
  .kilat-gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500;
  }
}
