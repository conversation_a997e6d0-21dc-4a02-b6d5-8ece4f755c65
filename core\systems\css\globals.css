/**
 * 🎨 KilatCSS - Global Styles
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * Base styles
 */
@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    
    --radius: 0.5rem;
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/**
 * KilatCSS Components
 */
@layer components {
  /* Buttons */
  .kilat-btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors 
           focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }
  
  .kilat-btn-primary {
    @apply kilat-btn bg-primary text-primary-foreground shadow hover:bg-primary/90;
  }
  
  .kilat-btn-secondary {
    @apply kilat-btn bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80;
  }
  
  .kilat-btn-outline {
    @apply kilat-btn border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground;
  }
  
  .kilat-btn-ghost {
    @apply kilat-btn hover:bg-accent hover:text-accent-foreground;
  }
  
  .kilat-btn-link {
    @apply kilat-btn text-primary underline-offset-4 hover:underline;
  }
  
  /* Cards */
  .kilat-card {
    @apply rounded-lg border bg-card text-card-foreground shadow;
  }
  
  .kilat-card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .kilat-card-title {
    @apply font-semibold leading-none tracking-tight;
  }
  
  .kilat-card-description {
    @apply text-sm text-muted-foreground;
  }
  
  .kilat-card-content {
    @apply p-6 pt-0;
  }
  
  .kilat-card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  /* Inputs */
  .kilat-input {
    @apply flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm
           transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium
           placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1
           focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Layout */
  .kilat-container {
    @apply mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8;
  }
}

/**
 * KilatCSS Utilities
 */
@layer utilities {
  .kilat-glow {
    @apply relative;
  }

  .kilat-glow::after {
    @apply absolute inset-0 -z-10 rounded-lg opacity-0 transition-opacity content-[''];
    box-shadow: 0 0 25px 3px theme('colors.primary.500');
  }

  .kilat-glow:hover::after {
    @apply opacity-100;
  }

  .kilat-gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500;
  }

  .kilat-gradient-bg {
    @apply bg-gradient-to-br from-blue-600 via-purple-600 to-cyan-500;
  }

  .kilat-glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .kilat-shadow-glow {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
  }

  .kilat-text-glow {
    text-shadow: 0 0 10px currentColor;
  }

  /* Hero animations */
  .kilat-hero-float {
    animation: kilat-float 6s ease-in-out infinite;
  }

  .kilat-hero-pulse {
    animation: kilat-pulse 2s ease-in-out infinite;
  }

  /* Scroll animations */
  .kilat-fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }

  .kilat-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .kilat-slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease-out;
  }

  .kilat-slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
  }

  .kilat-slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease-out;
  }

  .kilat-slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
  }

  .kilat-scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease-out;
  }

  .kilat-scale-in.visible {
    opacity: 1;
    transform: scale(1);
  }
}

/**
 * KilatCSS Animations
 */
@keyframes kilat-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes kilat-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes kilat-bounce-in {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes kilat-slide-up {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes kilat-rotate-in {
  from { transform: rotate(-180deg) scale(0); opacity: 0; }
  to { transform: rotate(0deg) scale(1); opacity: 1; }
}

/* KilatAnim Animation Classes */
.kilat-anim-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.kilat-anim-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.kilat-anim-fadeInDown {
  animation: fadeInDown 0.6s ease-out forwards;
}

.kilat-anim-fadeInLeft {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.kilat-anim-fadeInRight {
  animation: fadeInRight 0.6s ease-out forwards;
}

.kilat-anim-slideInUp {
  animation: slideInUp 0.6s ease-out forwards;
}

.kilat-anim-slideInDown {
  animation: slideInDown 0.6s ease-out forwards;
}

.kilat-anim-scaleIn {
  animation: scaleIn 0.6s ease-out forwards;
}

.kilat-anim-rotateIn {
  animation: rotateIn 0.6s ease-out forwards;
}

.kilat-anim-bounceIn {
  animation: bounceIn 0.8s ease-out forwards;
}

.kilat-anim-zoomIn {
  animation: zoomIn 0.6s ease-out forwards;
}

.kilat-anim-flipInX {
  animation: flipInX 0.6s ease-out forwards;
}

.kilat-anim-flipInY {
  animation: flipInY 0.6s ease-out forwards;
}

/* Animation Keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes slideInDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes rotateIn {
  from { opacity: 0; transform: rotate(-180deg) scale(0.8); }
  to { opacity: 1; transform: rotate(0deg) scale(1); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

@keyframes zoomIn {
  from { opacity: 0; transform: scale(0.5); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes flipInX {
  from { opacity: 0; transform: perspective(400px) rotateX(90deg); }
  to { opacity: 1; transform: perspective(400px) rotateX(0deg); }
}

@keyframes flipInY {
  from { opacity: 0; transform: perspective(400px) rotateY(90deg); }
  to { opacity: 1; transform: perspective(400px) rotateY(0deg); }
}

/* Button Styles */
.kilat-btn {
  @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg transition-all duration-300 ease-in-out;
}

.kilat-btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-4 focus:ring-blue-500/50;
}

.kilat-btn-outline {
  @apply bg-transparent text-blue-400 border-2 border-blue-400 hover:bg-blue-400 hover:text-white;
}

/* Card Styles */
.kilat-card {
  @apply bg-gray-800/50 border border-gray-700 rounded-xl p-6 transition-all duration-300 ease-in-out;
}

.kilat-card:hover {
  @apply transform -translate-y-2 border-blue-500/50;
}

/* Container */
.kilat-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .kilat-container {
    @apply px-2;
  }

  .kilat-btn {
    @apply text-sm px-4 py-2;
  }

  .kilat-card {
    @apply p-4;
  }
}
