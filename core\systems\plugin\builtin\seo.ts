/**
 * 🔍 SEO Plugin - Search Engine Optimization
 */

import type { KilatPlugin, KilatContext } from '../../../../types/config'

export interface SEOConfig {
  enabled: boolean
  defaultTitle: string
  defaultDescription: string
  defaultKeywords: string[]
  defaultOgImage: string
  defaultTwitterCard: string
  siteName: string
  siteUrl: string
  locale: string
  generateSitemap: boolean
  generateRobots: boolean
  jsonLd: boolean
}

export interface SEOMetadata {
  title?: string
  description?: string
  keywords?: string[]
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  ogType?: string
  twitterCard?: string
  twitterTitle?: string
  twitterDescription?: string
  twitterImage?: string
  canonicalUrl?: string
  jsonLd?: Record<string, any>
}

class SEOManager {
  private config: SEOConfig
  private pageMetadata: Map<string, SEOMetadata> = new Map()
  
  constructor(config: SEOConfig) {
    this.config = config
  }
  
  setPageMetadata(path: string, metadata: SEOMetadata): void {
    this.pageMetadata.set(path, metadata)
  }
  
  getPageMetadata(path: string): SEOMetadata {
    return this.pageMetadata.get(path) || {}
  }
  
  generateMetaTags(path: string, customMetadata?: SEOMetadata): string {
    const pageMetadata = this.pageMetadata.get(path) || {}
    const metadata = { ...pageMetadata, ...customMetadata }
    
    const title = metadata.title || this.config.defaultTitle
    const description = metadata.description || this.config.defaultDescription
    const keywords = metadata.keywords || this.config.defaultKeywords
    const ogImage = metadata.ogImage || this.config.defaultOgImage
    const twitterCard = metadata.twitterCard || this.config.defaultTwitterCard
    const canonicalUrl = metadata.canonicalUrl || `${this.config.siteUrl}${path}`
    
    let tags = `
      <!-- Primary Meta Tags -->
      <title>${title}</title>
      <meta name="title" content="${title}">
      <meta name="description" content="${description}">
      ${keywords.length > 0 ? `<meta name="keywords" content="${keywords.join(', ')}">` : ''}
      <link rel="canonical" href="${canonicalUrl}">
      
      <!-- Open Graph / Facebook -->
      <meta property="og:type" content="${metadata.ogType || 'website'}">
      <meta property="og:url" content="${canonicalUrl}">
      <meta property="og:title" content="${metadata.ogTitle || title}">
      <meta property="og:description" content="${metadata.ogDescription || description}">
      <meta property="og:image" content="${ogImage}">
      <meta property="og:site_name" content="${this.config.siteName}">
      <meta property="og:locale" content="${this.config.locale}">
      
      <!-- Twitter -->
      <meta property="twitter:card" content="${twitterCard}">
      <meta property="twitter:url" content="${canonicalUrl}">
      <meta property="twitter:title" content="${metadata.twitterTitle || title}">
      <meta property="twitter:description" content="${metadata.twitterDescription || description}">
      <meta property="twitter:image" content="${metadata.twitterImage || ogImage}">
    `
    
    // Add JSON-LD if enabled
    if (this.config.jsonLd && metadata.jsonLd) {
      tags += `
        <!-- JSON-LD -->
        <script type="application/ld+json">
          ${JSON.stringify(metadata.jsonLd)}
        </script>
      `
    }
    
    return tags
  }
  
  async generateSitemap(): Promise<string> {
    const urls = Array.from(this.pageMetadata.keys())
      .filter(path => !path.startsWith('/api/'))
      .map(path => `${this.config.siteUrl}${path}`)
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${urls.map(url => `
  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  `).join('')}
</urlset>`
  }
  
  generateRobots(): string {
    return `# Kilat.js Robots.txt
User-agent: *
Allow: /

# Sitemap
Sitemap: ${this.config.siteUrl}/sitemap.xml`
  }
}

let seoManager: SEOManager

const seoPlugin: KilatPlugin = {
  name: 'seo',
  version: '1.0.0',
  description: 'Search Engine Optimization for better discoverability',
  author: 'Kilat.js Team',
  builtin: true,
  
  config: {
    enabled: true,
    defaultTitle: 'Kilat.js App',
    defaultDescription: 'Built with Kilat.js - Modern Fullstack Framework',
    defaultKeywords: ['kilat', 'framework', 'javascript', 'typescript', 'react'],
    defaultOgImage: '/og-image.png',
    defaultTwitterCard: 'summary_large_image',
    siteName: 'Kilat.js App',
    siteUrl: 'https://example.com',
    locale: 'en_US',
    generateSitemap: true,
    generateRobots: true,
    jsonLd: true
  },
  
  hooks: {
    'plugin:init': async (context: KilatContext) => {
      const config = context.config.plugins.find(p => p.name === 'seo')?.config || {}
      seoManager = new SEOManager(config as SEOConfig)
      console.log('🔍 SEO plugin initialized')
    },
    
    'render:before': async (context: KilatContext, data: any) => {
      if (data?.route && data?.htmlContent) {
        const path = data.route.path
        const metaTags = seoManager.generateMetaTags(path)
        
        // Inject meta tags into HTML
        data.htmlContent = data.htmlContent.replace('</head>', `${metaTags}\n</head>`)
      }
    },
    
    'request:before': async (context: KilatContext, data: any) => {
      if (!data?.request) return
      
      const url = new URL(data.request.url)
      
      // Handle sitemap.xml
      if (url.pathname === '/sitemap.xml' && seoManager.config.generateSitemap) {
        const sitemap = await seoManager.generateSitemap()
        data.response = new Response(sitemap, {
          status: 200,
          headers: {
            'Content-Type': 'application/xml',
            'Cache-Control': 'public, max-age=86400'
          }
        })
      }
      
      // Handle robots.txt
      if (url.pathname === '/robots.txt' && seoManager.config.generateRobots) {
        const robots = seoManager.generateRobots()
        data.response = new Response(robots, {
          status: 200,
          headers: {
            'Content-Type': 'text/plain',
            'Cache-Control': 'public, max-age=86400'
          }
        })
      }
    }
  }
}

/**
 * Get SEO manager instance
 */
export function getSEOManager(): SEOManager | undefined {
  return seoManager
}

/**
 * Set page metadata
 */
export function setPageMetadata(path: string, metadata: SEOMetadata): void {
  if (seoManager) {
    seoManager.setPageMetadata(path, metadata)
  }
}

export default seoPlugin
