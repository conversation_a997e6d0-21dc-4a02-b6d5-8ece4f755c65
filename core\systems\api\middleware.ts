/**
 * 🔗 API Middleware System
 */

export type MiddlewareFunction = (request: Request) => Promise<Response | null>

export interface MiddlewareConfig {
  name: string
  enabled: boolean
  order: number
  middleware: MiddlewareFunction
}

class MiddlewareManager {
  private middlewares: MiddlewareConfig[] = []
  
  /**
   * Register middleware
   */
  use(name: string, middleware: MiddlewareFunction, options: { enabled?: boolean, order?: number } = {}): void {
    const config: MiddlewareConfig = {
      name,
      enabled: options.enabled ?? true,
      order: options.order ?? 0,
      middleware
    }
    
    this.middlewares.push(config)
    
    // Sort by order
    this.middlewares.sort((a, b) => a.order - b.order)
  }
  
  /**
   * Execute all middlewares
   */
  async execute(request: Request): Promise<Response | null> {
    for (const config of this.middlewares) {
      if (!config.enabled) continue
      
      try {
        const result = await config.middleware(request)
        
        // If middleware returns a response, stop execution
        if (result) {
          return result
        }
      } catch (error) {
        console.error(`❌ Middleware error (${config.name}):`, error)
        
        return new Response(JSON.stringify({
          error: 'Middleware error',
          message: 'An error occurred while processing the request'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        })
      }
    }
    
    return null // Continue to route handler
  }
  
  /**
   * Get middleware by name
   */
  get(name: string): MiddlewareConfig | undefined {
    return this.middlewares.find(m => m.name === name)
  }
  
  /**
   * Enable/disable middleware
   */
  toggle(name: string, enabled: boolean): void {
    const middleware = this.get(name)
    if (middleware) {
      middleware.enabled = enabled
    }
  }
  
  /**
   * Remove middleware
   */
  remove(name: string): void {
    this.middlewares = this.middlewares.filter(m => m.name !== name)
  }
  
  /**
   * Get all middlewares
   */
  getAll(): MiddlewareConfig[] {
    return [...this.middlewares]
  }
}

// Global middleware manager
const middlewareManager = new MiddlewareManager()

/**
 * Built-in middleware functions
 */

/**
 * CORS middleware
 */
export function corsMiddleware(options: {
  origin?: string | string[]
  methods?: string[]
  headers?: string[]
  credentials?: boolean
} = {}): MiddlewareFunction {
  const {
    origin = '*',
    methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'],
    headers = ['Content-Type', 'Authorization'],
    credentials = false
  } = options
  
  return async (request: Request): Promise<Response | null> => {
    const requestOrigin = request.headers.get('origin')
    
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      const headers = new Headers()
      
      // Set origin
      if (Array.isArray(origin)) {
        if (requestOrigin && origin.includes(requestOrigin)) {
          headers.set('Access-Control-Allow-Origin', requestOrigin)
        }
      } else if (origin === '*' || origin === requestOrigin) {
        headers.set('Access-Control-Allow-Origin', origin)
      }
      
      headers.set('Access-Control-Allow-Methods', methods.join(', '))
      headers.set('Access-Control-Allow-Headers', headers.join(', '))
      
      if (credentials) {
        headers.set('Access-Control-Allow-Credentials', 'true')
      }
      
      return new Response(null, { status: 204, headers })
    }
    
    // Add CORS headers to request for later use
    ;(request as any).corsHeaders = {
      'Access-Control-Allow-Origin': Array.isArray(origin) 
        ? (requestOrigin && origin.includes(requestOrigin) ? requestOrigin : origin[0])
        : origin,
      'Access-Control-Allow-Credentials': credentials.toString()
    }
    
    return null
  }
}

/**
 * Authentication middleware
 */
export function authMiddleware(options: {
  secret?: string
  algorithm?: string
  skipPaths?: string[]
} = {}): MiddlewareFunction {
  const {
    secret = 'your-secret-key',
    algorithm = 'HS256',
    skipPaths = []
  } = options
  
  return async (request: Request): Promise<Response | null> => {
    const url = new URL(request.url)
    
    // Skip authentication for certain paths
    if (skipPaths.some(path => url.pathname.startsWith(path))) {
      return null
    }
    
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    const token = authHeader.substring(7) // Remove 'Bearer ' prefix
    
    try {
      // In a real implementation, you would verify the JWT token here
      // For now, we'll just check if it's not empty
      if (!token) {
        throw new Error('Invalid token')
      }
      
      // Mock user data - in real implementation, decode from JWT
      ;(request as any).user = {
        id: '1',
        email: '<EMAIL>',
        role: 'user'
      }
      
      return null
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        message: 'Invalid token'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }
  }
}

/**
 * Logging middleware
 */
export function loggingMiddleware(): MiddlewareFunction {
  return async (request: Request): Promise<Response | null> => {
    const start = Date.now()
    const url = new URL(request.url)
    
    console.log(`📝 ${request.method} ${url.pathname} - ${new Date().toISOString()}`)
    
    // Store start time for response logging
    ;(request as any).startTime = start
    
    return null
  }
}

/**
 * Security headers middleware
 */
export function securityHeadersMiddleware(): MiddlewareFunction {
  return async (request: Request): Promise<Response | null> => {
    // Add security headers to request for later use
    ;(request as any).securityHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': "default-src 'self'"
    }
    
    return null
  }
}

/**
 * Request size limit middleware
 */
export function requestSizeLimitMiddleware(maxSize: number = 1024 * 1024): MiddlewareFunction {
  return async (request: Request): Promise<Response | null> => {
    const contentLength = request.headers.get('content-length')
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      return new Response(JSON.stringify({
        error: 'Payload too large',
        message: `Request size exceeds ${maxSize} bytes`
      }), {
        status: 413,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    return null
  }
}

/**
 * Register middleware
 */
export function useMiddleware(name: string, middleware: MiddlewareFunction, options?: { enabled?: boolean, order?: number }): void {
  middlewareManager.use(name, middleware, options)
}

/**
 * Execute all middlewares
 */
export function executeMiddlewares(request: Request): Promise<Response | null> {
  return middlewareManager.execute(request)
}

/**
 * Get middleware manager
 */
export function getMiddlewareManager(): MiddlewareManager {
  return middlewareManager
}

export default middlewareManager
