import type { KilatConfig } from './types/config'

const config: KilatConfig = {
  // 🚀 Runtime Configuration
  runtime: {
    engine: 'bun', // 'bun' | 'node'
    port: 3000,
    host: 'localhost',
    https: false,
  },

  // 🗂️ Apps Mapping (File-based Routing)
  apps: {
    dir: './apps',
    extensions: ['.tsx', '.ts', '.jsx', '.js'],
    middleware: true,
    groups: true,
  },

  // 🎨 UI Systems
  ui: {
    css: {
      framework: 'tailwind', // 'tailwind' | 'custom'
      themes: ['glow', 'cyber', 'pastel'],
      defaultTheme: 'glow',
    },
    animations: {
      enabled: true,
      presets: ['scroll', 'orbit', '3d', 'hover'],
    },
  },

  // 🌊 Rendering
  render: {
    mode: 'ssr', // 'ssr' | 'csr' | 'ssg' | 'isr'
    streaming: true,
    rsc: true, // React Server Components
    suspense: true,
  },

  // 🔌 Plugins
  plugins: [],

  // 🛠️ Development
  dev: {
    overlay: true,
    hmr: true,
    analytics: true,
  },

  // 🏗️ Build
  build: {
    outDir: './dist',
    target: 'es2022',
    minify: true,
    sourcemap: true,
  },

  // 🌐 Deployment
  deploy: {
    platform: 'auto', // 'vercel' | 'netlify' | 'cloudflare' | 'auto'
    edge: true,
  },
}

export default config
