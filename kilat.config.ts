/**
 * ⚙️ Kilat.js Advanced Configuration
 * Framework configuration for SpeedRun™ Runtime
 */

import type { BuildConfig, SpeedRunConfig } from './core/types'

interface KilatConfig extends Partial<BuildConfig>, Partial<SpeedRunConfig> {
  framework?: {
    name?: string
    version?: string
    codename?: string
  }
  routing?: {
    basePath?: string
    trailingSlash?: boolean
    caseSensitive?: boolean
  }
  rendering?: {
    defaultMode?: 'ssg' | 'ssr' | 'csr'
    streaming?: boolean
    compression?: boolean
  }
  ui?: {
    css?: {
      framework?: string
      themes?: string[]
      defaultTheme?: string
    }
    animations?: {
      enabled?: boolean
      presets?: string[]
    }
  }
  experimental?: {
    appDir?: boolean
    serverComponents?: boolean
    edgeRuntime?: boolean
  }
}

const config: KilatConfig = {
  // Framework info
  framework: {
    name: 'Kilat.js',
    version: '2.0.0',
    codename: 'SpeedRun™ Advanced'
  },

  // Build configuration
  outDir: '.kilat/dist',
  staticDir: '.kilat/static',
  serverDir: '.kilat/server',
  clientDir: '.kilat/client',
  mode: 'production',
  target: 'hybrid',

  // Server configuration
  runtime: 'bun',
  port: 3000,
  host: 'localhost',
  cors: true,
  compression: true,
  staticFiles: true,
  hotReload: true,

  // Routing settings
  routing: {
    basePath: '',
    trailingSlash: false,
    caseSensitive: false
  },

  // Rendering settings
  rendering: {
    defaultMode: 'ssr',
    streaming: true,
    compression: true
  },

  // UI Systems
  ui: {
    css: {
      framework: 'kilatcss',
      themes: ['default', 'cyber', 'nusantara'],
      defaultTheme: 'default'
    },
    animations: {
      enabled: true,
      presets: ['scroll', 'fade', 'slide', 'scale', 'glow']
    }
  },

  // Experimental features
  experimental: {
    appDir: true,
    serverComponents: false,
    edgeRuntime: false
  }
}

export default config
