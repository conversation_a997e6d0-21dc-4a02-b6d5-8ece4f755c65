export interface KilatConfig {
  runtime: {
    engine: 'bun' | 'node'
    port: number
    host: string
    https: boolean
  }

  apps: {
    dir: string
    extensions: string[]
    middleware: boolean
    groups: boolean
  }

  ui: {
    css: {
      framework: 'tailwind' | 'custom'
      themes: string[]
      defaultTheme: string
    }
    animations: {
      enabled: boolean
      presets: string[]
    }
  }

  render: {
    mode: 'ssr' | 'csr' | 'ssg' | 'isr'
    streaming: boolean
    rsc: boolean
    suspense: boolean
  }

  plugins: KilatPlugin[]

  dev: {
    overlay: boolean
    hmr: boolean
    analytics: boolean
  }

  build: {
    outDir: string
    target: string
    minify: boolean
    sourcemap: boolean
  }

  deploy: {
    platform: 'vercel' | 'netlify' | 'cloudflare' | 'auto'
    edge: boolean
  }
}

export interface KilatPlugin {
  name: string
  version?: string
  description?: string
  author?: string
  module?: string
  builtin?: boolean
  config?: Record<string, any>
  dependencies?: string[]
  hooks?: {
    'plugin:init'?: (context: KilatContext, data?: any) => void | Promise<void>
    'plugin:cleanup'?: (context: Kilat<PERSON>ontext, data?: any) => void | Promise<void>
    'build:start'?: (context: KilatContext, data?: any) => void | Promise<void>
    'build:end'?: (context: KilatContext, data?: any) => void | Promise<void>
    'dev:start'?: (context: KilatContext, data?: any) => void | Promise<void>
    'dev:reload'?: (context: KilatContext, data?: any) => void | Promise<void>
    'server:start'?: (context: KilatContext, data?: any) => void | Promise<void>
    'server:stop'?: (context: KilatContext, data?: any) => void | Promise<void>
    'request:before'?: (context: KilatContext, data?: any) => void | Promise<void>
    'request:after'?: (context: KilatContext, data?: any) => void | Promise<void>
    'render:before'?: (context: KilatContext, data?: any) => void | Promise<void>
    'render:after'?: (context: KilatContext, data?: any) => void | Promise<void>
    [key: string]: ((context: KilatContext, data?: any) => void | Promise<void>) | undefined
  }
}

export interface AppRoute {
  path: string
  component: string
  layout?: string
  middleware?: string[]
  groups?: string[]
  filePath?: string
  metadata?: {
    title?: string
    description?: string
    keywords?: string[]
    canonical?: string
    noindex?: boolean
    priority?: number
    changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  }
}

export interface KilatContext {
  config: KilatConfig
  routes: AppRoute[]
  plugins: KilatPlugin[]
  mode: 'development' | 'production'
}
