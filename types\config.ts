export interface KilatConfig {
  runtime: {
    engine: 'bun' | 'node'
    port: number
    host: string
    https: boolean
  }

  apps: {
    dir: string
    extensions: string[]
    middleware: boolean
    groups: boolean
  }

  ui: {
    css: {
      framework: 'tailwind' | 'custom'
      themes: string[]
      defaultTheme: string
    }
    animations: {
      enabled: boolean
      presets: string[]
    }
  }

  render: {
    mode: 'ssr' | 'csr' | 'ssg' | 'isr'
    streaming: boolean
    rsc: boolean
    suspense: boolean
  }

  plugins: KilatPlugin[]

  dev: {
    overlay: boolean
    hmr: boolean
    analytics: boolean
  }

  build: {
    outDir: string
    target: string
    minify: boolean
    sourcemap: boolean
  }

  deploy: {
    platform: 'vercel' | 'netlify' | 'cloudflare' | 'auto'
    edge: boolean
  }
}

export interface KilatPlugin {
  name: string
  version?: string
  config?: Record<string, any>
  hooks?: {
    'build:start'?: () => void | Promise<void>
    'build:end'?: () => void | Promise<void>
    'dev:start'?: () => void | Promise<void>
    'dev:reload'?: () => void | Promise<void>
  }
}

export interface AppRoute {
  path: string
  component: string
  layout?: string
  middleware?: string[]
  groups?: string[]
}

export interface KilatContext {
  config: KilatConfig
  routes: AppRoute[]
  plugins: KilatPlugin[]
  mode: 'development' | 'production'
}
