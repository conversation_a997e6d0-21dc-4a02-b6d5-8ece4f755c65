/**
 * 🎭 KilatAnim - Animation System
 * Client-side animation controller for Kilat.js
 */

(function() {
  'use strict';

  // Animation observer for scroll-triggered animations
  const animationObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        
        // Add stagger delay for child elements
        const children = entry.target.querySelectorAll('.kilat-fade-in, .kilat-slide-in-left, .kilat-slide-in-right, .kilat-scale-in');
        children.forEach((child, index) => {
          setTimeout(() => {
            child.classList.add('visible');
          }, index * 100);
        });
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  });

  // Initialize animations when DOM is ready
  function initAnimations() {
    // Observe all animation elements
    const animatedElements = document.querySelectorAll('.kilat-fade-in, .kilat-slide-in-left, .kilat-slide-in-right, .kilat-scale-in');
    animatedElements.forEach(el => {
      animationObserver.observe(el);
    });

    // Add hover effects
    const hoverElements = document.querySelectorAll('.kilat-glow-hover');
    hoverElements.forEach(el => {
      el.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.3)';
      });
      
      el.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '';
      });
    });

    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Parallax effect for background elements
    const parallaxElements = document.querySelectorAll('.kilat-hero-float, .kilat-hero-pulse');
    
    function updateParallax() {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;
      
      parallaxElements.forEach(el => {
        el.style.transform = `translateY(${rate}px)`;
      });
    }

    // Throttled scroll handler
    let ticking = false;
    function handleScroll() {
      if (!ticking) {
        requestAnimationFrame(() => {
          updateParallax();
          ticking = false;
        });
        ticking = true;
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Add loading animation
    const loadingElements = document.querySelectorAll('.kilat-loading');
    loadingElements.forEach(el => {
      setTimeout(() => {
        el.style.opacity = '0';
        setTimeout(() => {
          el.style.display = 'none';
        }, 300);
      }, 1000);
    });

    // Performance monitoring
    if (window.performance && window.performance.mark) {
      window.performance.mark('kilat-anim-init-end');
      
      // Measure initialization time
      window.performance.measure('kilat-anim-init', 'kilat-anim-init-start', 'kilat-anim-init-end');
      
      const measure = window.performance.getEntriesByName('kilat-anim-init')[0];
      if (measure && measure.duration > 100) {
        console.warn('KilatAnim initialization took', measure.duration.toFixed(2), 'ms');
      }
    }
  }

  // Theme switcher
  function initThemeSwitcher() {
    // Create theme switcher if it doesn't exist
    if (!document.querySelector('.kilat-theme-switcher')) {
      createThemeSwitcher();
    }

    const themeButtons = document.querySelectorAll('[data-theme]');
    themeButtons.forEach(button => {
      button.addEventListener('click', function() {
        const theme = this.getAttribute('data-theme');
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('kilat-theme', theme);

        // Update active button
        themeButtons.forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');

        console.log('🎨 Theme switched to:', theme);
      });
    });

    // Load saved theme
    const savedTheme = localStorage.getItem('kilat-theme');
    if (savedTheme) {
      document.documentElement.setAttribute('data-theme', savedTheme);
      const activeButton = document.querySelector(`[data-theme="${savedTheme}"]`);
      if (activeButton) {
        activeButton.classList.add('active');
      }
    }
  }

  // Create theme switcher UI
  function createThemeSwitcher() {
    const switcher = document.createElement('div');
    switcher.className = 'kilat-theme-switcher fixed top-4 right-4 z-50 flex gap-2 p-2 kilat-glass rounded-lg';
    switcher.innerHTML = `
      <button data-theme="default" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm transition-all duration-300" title="Default Theme">
        🌙 Default
      </button>
      <button data-theme="cyber" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm transition-all duration-300" title="Cyber Theme">
        🤖 Cyber
      </button>
      <button data-theme="nusantara" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm transition-all duration-300" title="Nusantara Theme">
        🏛️ Nusantara
      </button>
    `;

    // Add styles for active state
    const style = document.createElement('style');
    style.textContent = `
      .kilat-theme-switcher .kilat-btn.active {
        background: var(--kilat-primary, #3b82f6);
        color: white;
        border-color: var(--kilat-primary, #3b82f6);
      }
    `;
    document.head.appendChild(style);
    document.body.appendChild(switcher);
  }

  // Reduced motion support
  function respectReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    function handleReducedMotion() {
      if (prefersReducedMotion.matches) {
        document.documentElement.style.setProperty('--kilat-duration-fast', '0s');
        document.documentElement.style.setProperty('--kilat-duration-normal', '0s');
        document.documentElement.style.setProperty('--kilat-duration-slow', '0s');
      }
    }

    handleReducedMotion();
    prefersReducedMotion.addEventListener('change', handleReducedMotion);
  }

  // Initialize everything
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      if (window.performance && window.performance.mark) {
        window.performance.mark('kilat-anim-init-start');
      }
      
      initAnimations();
      initThemeSwitcher();
      respectReducedMotion();
      
      console.log('🎭 KilatAnim initialized');
    });
  } else {
    initAnimations();
    initThemeSwitcher();
    respectReducedMotion();
    
    console.log('🎭 KilatAnim initialized');
  }

  // Export for global access
  window.KilatAnim = {
    version: '1.0.0',
    initAnimations,
    initThemeSwitcher,
    respectReducedMotion
  };

})();
