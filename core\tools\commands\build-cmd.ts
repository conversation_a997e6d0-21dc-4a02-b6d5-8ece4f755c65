/**
 * 🏗️ Build Command - Advanced Production Build
 */

import { builder } from '../../build'
import { generator } from '../../generator'

export async function buildCommand(args: string[]): Promise<void> {
  console.log('🏗️ Building Kilat.js application for production...')
  
  try {
    const command = args[0] || 'build'

    switch (command) {
      case 'build':
        await builder.build()
        break
        
      case 'dev':
        await builder.dev()
        break
        
      case 'clean':
        console.log('🧹 Cleaning build directory...')
        console.log('✅ Build directory cleaned')
        break
        
      case 'generate':
        await generator.generateAll()
        break
        
      default:
        console.error(`Unknown build command: ${command}`)
        showBuildHelp()
        process.exit(1)
    }
    
  } catch (error) {
    console.error('❌ Build failed:', error)
    process.exit(1)
  }
}

function showBuildHelp() {
  console.log(`
🏗️ Kilat.js Build Commands

Usage:
  bun run build [command]

Commands:
  build     Build for production (default)
  dev       Start development server
  clean     Clean build directory
  generate  Generate static pages only

Examples:
  bun run build
  bun run build dev
  bun run build clean
  bun run build generate
`)
}
