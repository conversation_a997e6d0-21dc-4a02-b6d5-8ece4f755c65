/**
 * 🎨 Kilat.js Advanced Rendering Engine
 * SSG, SSR, CSR support with React 18 features
 */

import React from 'react'
import { renderToString, renderToStaticMarkup } from 'react-dom/server'
import { join, dirname } from 'path'
import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs'
import type { 
  KilatContext, 
  RouteConfig, 
  RenderResult, 
  RenderMode, 
  PageProps,
  LayoutProps,
  GenerateMetadataResult,
  StaticGenerationConfig
} from '../types'
import { router, type RouteMatch, type LayoutChain } from '../router'

export class KilatRenderer {
  private componentCache: Map<string, any> = new Map()
  private metadataCache: Map<string, GenerateMetadataResult> = new Map()

  /**
   * Main render method - handles all render modes
   */
  async render(
    pathname: string, 
    context: KilatContext,
    mode?: RenderMode
  ): Promise<RenderResult> {
    try {
      const routeMatch = router.matchRoute(pathname)
      if (!routeMatch) {
        return this.renderNotFound(pathname, context)
      }

      const { route, params } = routeMatch
      const renderMode = mode || route.renderMode

      // Execute middleware chain
      await this.executeMiddleware(pathname, context)

      switch (renderMode) {
        case 'ssg':
          return await this.renderSSG(route, params, context)
        case 'ssr':
          return await this.renderSSR(route, params, context)
        case 'csr':
          return await this.renderCSR(route, params, context)
        default:
          throw new Error(`Unknown render mode: ${renderMode}`)
      }
    } catch (error) {
      console.error('Render error:', error)
      return this.renderError(error as Error, pathname, context)
    }
  }

  /**
   * Static Site Generation (SSG)
   */
  private async renderSSG(
    route: RouteConfig, 
    params: Record<string, string>,
    context: KilatContext
  ): Promise<RenderResult> {
    const cacheKey = `${route.path}-${JSON.stringify(params)}`
    
    // Check if already generated
    const cachedPath = join('.kilat', 'static', cacheKey + '.html')
    if (existsSync(cachedPath)) {
      const html = readFileSync(cachedPath, 'utf-8')
      return { html, statusCode: 200 }
    }

    // Generate static content
    const result = await this.renderSSR(route, params, context)
    
    // Cache the result
    mkdirSync(dirname(cachedPath), { recursive: true })
    writeFileSync(cachedPath, result.html)
    
    return result
  }

  /**
   * Server-Side Rendering (SSR)
   */
  private async renderSSR(
    route: RouteConfig,
    params: Record<string, string>,
    context: KilatContext
  ): Promise<RenderResult> {
    // Load page component
    const PageComponent = await this.loadComponent(route.component)
    if (!PageComponent) {
      throw new Error(`Failed to load component: ${route.component}`)
    }

    // Get layout chain
    const layouts = router.getLayoutChain(route.path)
    
    // Prepare props
    const pageProps: PageProps = {
      params,
      searchParams: this.parseSearchParams(context.request.url)
    }

    // Generate metadata
    const metadata = await this.generateMetadata(route, pageProps)

    // Render component tree
    const element = await this.buildComponentTree(PageComponent, layouts, pageProps)
    
    // Render to HTML
    const html = this.renderToHTML(element, metadata)
    
    return {
      html,
      metadata,
      statusCode: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=0, must-revalidate'
      }
    }
  }

  /**
   * Client-Side Rendering (CSR)
   */
  private async renderCSR(
    route: RouteConfig,
    params: Record<string, string>,
    context: KilatContext
  ): Promise<RenderResult> {
    // Generate shell HTML with hydration data
    const metadata = await this.generateMetadata(route, { params })
    const html = this.renderShell(route, params, metadata)
    
    return {
      html,
      metadata,
      statusCode: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    }
  }

  /**
   * Load React component dynamically
   */
  private async loadComponent(componentPath: string): Promise<any> {
    if (this.componentCache.has(componentPath)) {
      return this.componentCache.get(componentPath)
    }

    try {
      // Dynamic import with cache busting in development
      const module = await import(componentPath + '?t=' + Date.now())
      const Component = module.default || module
      
      this.componentCache.set(componentPath, Component)
      return Component
    } catch (error) {
      console.error(`Failed to load component ${componentPath}:`, error)
      return null
    }
  }

  /**
   * Build component tree with layouts
   */
  private async buildComponentTree(
    PageComponent: any,
    layouts: LayoutChain[],
    pageProps: PageProps
  ): Promise<React.ReactElement> {
    let element = React.createElement(PageComponent, pageProps)

    // Wrap with layouts (innermost first)
    for (let i = layouts.length - 1; i >= 0; i--) {
      const layout = layouts[i]
      const LayoutComponent = await this.loadComponent(layout.component)
      
      if (LayoutComponent) {
        const layoutProps: LayoutProps = {
          children: element,
          params: pageProps.params
        }
        element = React.createElement(LayoutComponent, layoutProps)
      }
    }

    return element
  }

  /**
   * Generate metadata for page
   */
  private async generateMetadata(
    route: RouteConfig,
    props: PageProps
  ): Promise<GenerateMetadataResult> {
    const cacheKey = `${route.path}-${JSON.stringify(props.params)}`
    
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey)!
    }

    // Check for generateMetadata function
    try {
      const module = await import(route.component)
      if (module.generateMetadata) {
        const metadata = await module.generateMetadata(props)
        this.metadataCache.set(cacheKey, metadata)
        return metadata
      }
    } catch (error) {
      console.warn('Failed to generate metadata:', error)
    }

    // Default metadata
    const defaultMetadata: GenerateMetadataResult = {
      title: 'Kilat.js App',
      description: 'Built with Kilat.js SpeedRun™ Runtime'
    }

    this.metadataCache.set(cacheKey, defaultMetadata)
    return defaultMetadata
  }

  /**
   * Render React element to HTML string
   */
  private renderToHTML(element: React.ReactElement, metadata: GenerateMetadataResult): string {
    const content = renderToString(element)
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>${metadata.title || 'Kilat.js App'}</title>
  ${metadata.description ? `<meta name="description" content="${metadata.description}" />` : ''}
  ${metadata.keywords ? `<meta name="keywords" content="${metadata.keywords.join(', ')}" />` : ''}
  ${metadata.openGraph ? this.renderOpenGraph(metadata.openGraph) : ''}
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat">${content}</div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>`
  }

  /**
   * Render CSR shell
   */
  private renderShell(route: RouteConfig, params: Record<string, string>, metadata: GenerateMetadataResult): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>${metadata.title || 'Kilat.js App'}</title>
  ${metadata.description ? `<meta name="description" content="${metadata.description}" />` : ''}
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
</head>
<body>
  <div id="__kilat">
    <div class="kilat-loading">
      <div class="kilat-spinner"></div>
      <div class="kilat-gradient-text text-xl font-bold ml-4">Loading...</div>
    </div>
  </div>
  
  <!-- Hydration Data -->
  <script>
    window.__KILAT_DATA__ = ${JSON.stringify({ route: route.path, params })};
  </script>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
  <script src="/_kilat/hydrate.js" defer></script>
</body>
</html>`
  }

  /**
   * Render Open Graph meta tags
   */
  private renderOpenGraph(og: any): string {
    let tags = ''
    if (og.title) tags += `<meta property="og:title" content="${og.title}" />\n  `
    if (og.description) tags += `<meta property="og:description" content="${og.description}" />\n  `
    if (og.images) {
      og.images.forEach((image: string) => {
        tags += `<meta property="og:image" content="${image}" />\n  `
      })
    }
    return tags
  }

  /**
   * Execute middleware chain
   */
  private async executeMiddleware(pathname: string, context: KilatContext): Promise<void> {
    const middlewares = router.getMiddleware(pathname)
    
    for (const middlewarePath of middlewares) {
      try {
        const middleware = await import(middlewarePath)
        if (middleware.default) {
          await middleware.default(context)
        }
      } catch (error) {
        console.error(`Middleware error in ${middlewarePath}:`, error)
      }
    }
  }

  /**
   * Parse search parameters
   */
  private parseSearchParams(url: string): Record<string, string | string[]> {
    const urlObj = new URL(url)
    const params: Record<string, string | string[]> = {}
    
    urlObj.searchParams.forEach((value, key) => {
      if (params[key]) {
        if (Array.isArray(params[key])) {
          (params[key] as string[]).push(value)
        } else {
          params[key] = [params[key] as string, value]
        }
      } else {
        params[key] = value
      }
    })
    
    return params
  }

  /**
   * Render 404 page
   */
  private async renderNotFound(pathname: string, context: KilatContext): Promise<RenderResult> {
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>404 - Page Not Found | Kilat.js</title>
  <link rel="stylesheet" href="/_kilat/styles.css" />
</head>
<body>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
    <div class="text-center">
      <h1 class="text-6xl font-bold text-white mb-4">404</h1>
      <p class="text-xl text-gray-300 mb-8">Page not found</p>
      <a href="/" class="kilat-btn kilat-btn-primary px-6 py-3">Go Home</a>
    </div>
  </div>
</body>
</html>`

    return { html, statusCode: 404 }
  }

  /**
   * Render error page
   */
  private async renderError(error: Error, pathname: string, context: KilatContext): Promise<RenderResult> {
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Error | Kilat.js</title>
  <link rel="stylesheet" href="/_kilat/styles.css" />
</head>
<body>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-purple-900 flex items-center justify-center">
    <div class="text-center max-w-md">
      <h1 class="text-4xl font-bold text-white mb-4">Something went wrong</h1>
      <p class="text-gray-300 mb-8">${error.message}</p>
      <a href="/" class="kilat-btn kilat-btn-primary px-6 py-3">Go Home</a>
    </div>
  </div>
</body>
</html>`

    return { html, statusCode: 500 }
  }
}

// Export singleton instance
export const renderer = new KilatRenderer()
