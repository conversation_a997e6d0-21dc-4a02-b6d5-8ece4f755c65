/**
 * 🌟 Glow Theme - Cyberpunk Neon Aesthetic
 */

export const glowTheme = {
  name: 'glow',
  displayName: 'Glow',
  description: 'Cyberpunk neon aesthetic with glowing effects',
  
  colors: {
    // Base colors
    background: {
      primary: '#0a0a0f',
      secondary: '#1a1a2e',
      tertiary: '#16213e',
    },
    
    // Text colors
    text: {
      primary: '#ffffff',
      secondary: '#b8c5d6',
      muted: '#6b7280',
      accent: '#00d4ff',
    },
    
    // Brand colors
    brand: {
      primary: '#00d4ff',
      secondary: '#ff006e',
      tertiary: '#8338ec',
    },
    
    // Semantic colors
    success: '#00ff88',
    warning: '#ffaa00',
    error: '#ff0055',
    info: '#0099ff',
    
    // Glow effects
    glow: {
      blue: '#00d4ff',
      purple: '#8338ec',
      pink: '#ff006e',
      green: '#00ff88',
      orange: '#ffaa00',
    },
  },
  
  // CSS custom properties
  cssVariables: {
    '--theme-bg-primary': '#0a0a0f',
    '--theme-bg-secondary': '#1a1a2e',
    '--theme-bg-tertiary': '#16213e',
    '--theme-text-primary': '#ffffff',
    '--theme-text-secondary': '#b8c5d6',
    '--theme-text-muted': '#6b7280',
    '--theme-text-accent': '#00d4ff',
    '--theme-brand-primary': '#00d4ff',
    '--theme-brand-secondary': '#ff006e',
    '--theme-brand-tertiary': '#8338ec',
    '--theme-success': '#00ff88',
    '--theme-warning': '#ffaa00',
    '--theme-error': '#ff0055',
    '--theme-info': '#0099ff',
    '--theme-glow-blue': '#00d4ff',
    '--theme-glow-purple': '#8338ec',
    '--theme-glow-pink': '#ff006e',
    '--theme-glow-green': '#00ff88',
    '--theme-glow-orange': '#ffaa00',
  },
  
  // Component styles
  components: {
    button: {
      primary: 'bg-brand-primary text-black hover:shadow-[0_0_20px_var(--theme-glow-blue)]',
      secondary: 'bg-brand-secondary text-white hover:shadow-[0_0_20px_var(--theme-glow-pink)]',
      ghost: 'text-brand-primary border border-brand-primary hover:bg-brand-primary hover:text-black',
    },
    
    card: {
      default: 'bg-bg-secondary border border-brand-primary/20 shadow-[0_0_10px_var(--theme-glow-blue)/20]',
      hover: 'hover:shadow-[0_0_20px_var(--theme-glow-blue)/40] hover:border-brand-primary/40',
    },
    
    input: {
      default: 'bg-bg-tertiary border border-brand-primary/30 focus:border-brand-primary focus:shadow-[0_0_10px_var(--theme-glow-blue)/50]',
    },
  },
  
  // Animation presets
  animations: {
    glow: 'animate-kilat-glow',
    float: 'animate-kilat-float',
    pulse: 'animate-kilat-pulse',
  },
}

export default glowTheme
