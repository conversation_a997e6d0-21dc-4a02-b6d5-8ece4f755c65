/**
 * 🎨 Kilat.js Styles - Complete CSS Framework
 * Integrated KilatCSS with Tail<PERSON> and custom animations
 */

/* KilatCSS - Native CSS Framework (No External Dependencies) */
/* Built-in Tailwind-like utilities and custom Kilat.js styling */

/* Native Font Stack */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300 900;
  font-display: swap;
  src: local('Inter'), local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont');
}

/* CSS Custom Properties */
:root {
  /* Colors */
  --kilat-primary: #3b82f6;
  --kilat-secondary: #8b5cf6;
  --kilat-accent: #06b6d4;
  --kilat-success: #10b981;
  --kilat-warning: #f59e0b;
  --kilat-error: #ef4444;
  
  /* Background Colors */
  --kilat-bg-primary: #0f172a;
  --kilat-bg-secondary: #1e293b;
  --kilat-bg-tertiary: #334155;
  
  /* Text Colors */
  --kilat-text-primary: #f8fafc;
  --kilat-text-secondary: #e2e8f0;
  --kilat-text-muted: #94a3b8;
  --kilat-text-disabled: #64748b;
  
  /* Border Colors */
  --kilat-border-primary: #475569;
  --kilat-border-secondary: #64748b;
  
  /* Shadows */
  --kilat-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --kilat-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --kilat-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --kilat-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --kilat-shadow-glow: 0 0 30px rgba(59, 130, 246, 0.3);
  
  /* Spacing */
  --kilat-space-xs: 0.25rem;
  --kilat-space-sm: 0.5rem;
  --kilat-space-md: 1rem;
  --kilat-space-lg: 1.5rem;
  --kilat-space-xl: 2rem;
  --kilat-space-2xl: 3rem;
  
  /* Border Radius */
  --kilat-radius-sm: 0.25rem;
  --kilat-radius-md: 0.5rem;
  --kilat-radius-lg: 0.75rem;
  --kilat-radius-xl: 1rem;
  
  /* Animation Durations */
  --kilat-duration-fast: 0.15s;
  --kilat-duration-normal: 0.3s;
  --kilat-duration-slow: 0.5s;
  
  /* Z-Index */
  --kilat-z-dropdown: 1000;
  --kilat-z-sticky: 1020;
  --kilat-z-fixed: 1030;
  --kilat-z-modal: 1040;
  --kilat-z-popover: 1050;
  --kilat-z-tooltip: 1060;
}

/* Theme Variants */
[data-theme="cyber"] {
  --kilat-primary: #00ff88;
  --kilat-secondary: #ff0080;
  --kilat-accent: #00d4ff;
  --kilat-bg-primary: #000011;
  --kilat-bg-secondary: #001122;
  --kilat-shadow-glow: 0 0 30px rgba(0, 255, 136, 0.4);
}

[data-theme="nusantara"] {
  --kilat-primary: #d97706;
  --kilat-secondary: #dc2626;
  --kilat-accent: #059669;
  --kilat-bg-primary: #1c1917;
  --kilat-bg-secondary: #292524;
  --kilat-shadow-glow: 0 0 30px rgba(217, 119, 6, 0.4);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--kilat-bg-primary);
  color: var(--kilat-text-primary);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin: 0 0 var(--kilat-space-md) 0;
  color: var(--kilat-text-primary);
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.75rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 600; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

p {
  margin: 0 0 var(--kilat-space-md) 0;
  color: var(--kilat-text-secondary);
  line-height: 1.6;
}

a {
  color: var(--kilat-primary);
  text-decoration: none;
  transition: all var(--kilat-duration-normal) ease;
}

a:hover {
  color: var(--kilat-accent);
  text-shadow: 0 0 8px currentColor;
}

/* Layout Components */
.kilat-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--kilat-space-md);
}

.kilat-section {
  padding: var(--kilat-space-2xl) 0;
}

/* Button Components */
.kilat-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--kilat-space-md) var(--kilat-space-lg);
  border: none;
  border-radius: var(--kilat-radius-lg);
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--kilat-duration-normal) ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.kilat-btn-primary {
  background: var(--kilat-primary);
  color: white;
  box-shadow: var(--kilat-shadow-md);
}

.kilat-btn-primary:hover {
  background: var(--kilat-secondary);
  transform: translateY(-2px);
  box-shadow: var(--kilat-shadow-glow);
}

.kilat-btn-secondary {
  background: transparent;
  color: var(--kilat-primary);
  border: 2px solid var(--kilat-primary);
}

.kilat-btn-secondary:hover {
  background: var(--kilat-primary);
  color: white;
  box-shadow: var(--kilat-shadow-glow);
}

.kilat-btn-outline {
  background: transparent;
  color: var(--kilat-text-primary);
  border: 2px solid var(--kilat-border-primary);
}

.kilat-btn-outline:hover {
  border-color: var(--kilat-primary);
  color: var(--kilat-primary);
  box-shadow: var(--kilat-shadow-glow);
}

/* Card Components */
.kilat-card {
  background: var(--kilat-bg-secondary);
  border: 1px solid var(--kilat-border-primary);
  border-radius: var(--kilat-radius-xl);
  padding: var(--kilat-space-lg);
  transition: all var(--kilat-duration-normal) ease;
  box-shadow: var(--kilat-shadow-md);
}

.kilat-card:hover {
  transform: translateY(-4px);
  border-color: var(--kilat-primary);
  box-shadow: var(--kilat-shadow-glow);
}

/* Glass Morphism */
.kilat-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--kilat-radius-xl);
}

/* Gradient Effects */
.kilat-gradient-bg {
  background: linear-gradient(135deg, var(--kilat-primary), var(--kilat-secondary));
}

.kilat-gradient-text {
  background: linear-gradient(135deg, var(--kilat-primary), var(--kilat-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Glow Effects */
.kilat-glow {
  box-shadow: var(--kilat-shadow-glow);
}

.kilat-glow-hover:hover {
  box-shadow: var(--kilat-shadow-glow);
  transform: translateY(-2px);
}

.kilat-text-glow {
  text-shadow: 0 0 10px currentColor;
}

/* Animation Classes */
.kilat-fade-in {
  opacity: 0;
  animation: kilatFadeIn 0.6s ease-out forwards;
}

.kilat-slide-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: kilatSlideInUp 0.6s ease-out forwards;
}

.kilat-slide-in-left {
  opacity: 0;
  transform: translateX(-30px);
  animation: kilatSlideInLeft 0.6s ease-out forwards;
}

.kilat-slide-in-right {
  opacity: 0;
  transform: translateX(30px);
  animation: kilatSlideInRight 0.6s ease-out forwards;
}

.kilat-scale-in {
  opacity: 0;
  transform: scale(0.8);
  animation: kilatScaleIn 0.6s ease-out forwards;
}

/* Background Animations */
.kilat-hero-float {
  animation: kilatFloat 6s ease-in-out infinite;
}

.kilat-hero-pulse {
  animation: kilatPulse 4s ease-in-out infinite;
}

/* Keyframes */
@keyframes kilatFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes kilatSlideInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes kilatSlideInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes kilatSlideInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes kilatScaleIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes kilatFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes kilatPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

/* Native Tailwind-like Utilities */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

/* Flexbox */
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1 1 0%; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Spacing */
.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.py-24 { padding-top: 6rem; padding-bottom: 6rem; }

.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mb-20 { margin-bottom: 5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.mt-16 { margin-top: 4rem; }
.mt-20 { margin-top: 5rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-4 { margin-left: 1rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-4 { margin-right: 1rem; }

/* Sizing */
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }
.w-40 { width: 10rem; }
.w-48 { width: 12rem; }
.w-64 { width: 16rem; }
.w-72 { width: 18rem; }
.w-96 { width: 24rem; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }

.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-24 { height: 6rem; }
.h-32 { height: 8rem; }
.h-40 { height: 10rem; }
.h-48 { height: 12rem; }
.h-64 { height: 16rem; }
.h-72 { height: 18rem; }
.h-96 { height: 24rem; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }

.min-h-screen { min-height: 100vh; }
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.top-4 { top: 1rem; }
.right-0 { right: 0; }
.right-4 { right: 1rem; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.left-4 { left: 1rem; }

/* Z-Index */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* Text */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }
.text-7xl { font-size: 4.5rem; line-height: 1; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

/* Colors */
.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-100 { color: #f7fafc; }
.text-gray-200 { color: #edf2f7; }
.text-gray-300 { color: #e2e8f0; }
.text-gray-400 { color: #cbd5e0; }
.text-gray-500 { color: #a0aec0; }
.text-gray-600 { color: #718096; }
.text-gray-700 { color: #4a5568; }
.text-gray-800 { color: #2d3748; }
.text-gray-900 { color: #1a202c; }

.text-blue-400 { color: #63b3ed; }
.text-blue-500 { color: #4299e1; }
.text-blue-600 { color: #3182ce; }
.text-purple-400 { color: #b794f6; }
.text-purple-500 { color: #9f7aea; }
.text-green-400 { color: #68d391; }
.text-green-500 { color: #48bb78; }
.text-yellow-400 { color: #f6e05e; }
.text-red-400 { color: #fc8181; }

/* Background Colors */
.bg-transparent { background-color: transparent; }
.bg-white { background-color: #ffffff; }
.bg-black { background-color: #000000; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }

.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-purple-500 { background-color: #8b5cf6; }
.bg-purple-600 { background-color: #7c3aed; }

/* Gradients */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.from-gray-900 { --tw-gradient-from: #111827; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(17, 24, 39, 0)); }
.via-blue-900 { --tw-gradient-stops: var(--tw-gradient-from), #1e3a8a, var(--tw-gradient-to, rgba(30, 58, 138, 0)); }
.to-purple-900 { --tw-gradient-to: #581c87; }
.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0)); }
.to-purple-500 { --tw-gradient-to: #8b5cf6; }

/* Borders */
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-700 { border-color: #374151; }
.border-white { border-color: #ffffff; }

/* Border Radius */
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

/* Transitions */
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }

/* Transform */
.transform { transform: var(--tw-transform); }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.-translate-y-2 { --tw-translate-y: -0.5rem; transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.hover\:-translate-y-2:hover { --tw-translate-y: -0.5rem; transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Cursor */
.cursor-pointer { cursor: pointer; }

/* Advanced Theme Switcher */
.kilat-theme-switcher .kilat-theme-btn {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.kilat-theme-switcher .kilat-theme-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.kilat-theme-switcher .kilat-theme-btn.active {
  border-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 0 25px rgba(255, 255, 255, 0.4);
  transform: scale(1.15);
}

.kilat-theme-switcher .kilat-theme-btn.active::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Floating Header Adjustments */
body {
  padding-top: 5rem; /* Space for floating header */
}

/* Enhanced Glass Effect */
.kilat-glass {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Footer Specific Styles */
footer .kilat-glass {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Minimalist Footer Enhancements */
footer {
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
}

footer a {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

footer a:hover {
  transform: translateY(-1px);
}

/* Footer Typography */
footer h3 {
  letter-spacing: 0.025em;
}

footer p, footer a {
  line-height: 1.4;
}

/* Footer Grid Alignment */
footer .grid {
  align-items: start;
}

/* Footer Social Icons */
footer .kilat-glass:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px) scale(1.05);
}

/* Performance Indicator Animation */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.5);
  }
  50% {
    opacity: 0.7;
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.8);
  }
}

.animate-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced Glow Effects */
.kilat-glow-hover:hover {
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(139, 92, 246, 0.2),
    inset 0 0 30px rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  body { padding-top: 4rem; }
  .kilat-container { padding: 0 var(--kilat-space-sm); }
  .kilat-btn { padding: var(--kilat-space-sm) var(--kilat-space-md); font-size: 0.875rem; }
  .kilat-card { padding: var(--kilat-space-md); }
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }

  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:col-span-2 { grid-column: span 2 / span 2; }
  .md\:text-5xl { font-size: 3rem; line-height: 1; }
  .md\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .md\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .md\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .md\:mt-0 { margin-top: 0; }
  .md\:ml-4 { margin-left: 1rem; }
  .md\:flex-row { flex-direction: row; }

  /* Mobile theme switcher */
  .kilat-theme-switcher {
    flex-direction: column;
    gap: 0.5rem;
  }

  /* Mobile footer adjustments */
  footer .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
  }

  footer .lg\:col-span-1 {
    grid-column: span 1;
  }
}

@media (max-width: 480px) {
  body { padding-top: 3.5rem; }
  .kilat-container { padding: 0 var(--kilat-space-xs); }
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }

  .sm\:flex { display: flex; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .sm\:py-32 { padding-top: 8rem; padding-bottom: 8rem; }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .kilat-fade-in,
  .kilat-slide-in-up,
  .kilat-slide-in-left,
  .kilat-slide-in-right,
  .kilat-scale-in,
  .kilat-hero-float,
  .kilat-hero-pulse {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Focus States */
.kilat-btn:focus,
.kilat-card:focus {
  outline: 2px solid var(--kilat-primary);
  outline-offset: 2px;
}

/* Loading States */
.kilat-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--kilat-bg-primary);
}

.kilat-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--kilat-border-primary);
  border-top: 4px solid var(--kilat-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
