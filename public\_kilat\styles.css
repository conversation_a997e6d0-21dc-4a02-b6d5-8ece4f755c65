/**
 * 🎨 Kilat.js Styles - Complete CSS Framework
 * Integrated KilatCSS with Tail<PERSON> and custom animations
 */

/* Import Tailwind CSS */
@import url('https://cdn.tailwindcss.com/3.3.0');

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Custom Properties */
:root {
  /* Colors */
  --kilat-primary: #3b82f6;
  --kilat-secondary: #8b5cf6;
  --kilat-accent: #06b6d4;
  --kilat-success: #10b981;
  --kilat-warning: #f59e0b;
  --kilat-error: #ef4444;
  
  /* Background Colors */
  --kilat-bg-primary: #0f172a;
  --kilat-bg-secondary: #1e293b;
  --kilat-bg-tertiary: #334155;
  
  /* Text Colors */
  --kilat-text-primary: #f8fafc;
  --kilat-text-secondary: #e2e8f0;
  --kilat-text-muted: #94a3b8;
  --kilat-text-disabled: #64748b;
  
  /* Border Colors */
  --kilat-border-primary: #475569;
  --kilat-border-secondary: #64748b;
  
  /* Shadows */
  --kilat-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --kilat-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --kilat-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --kilat-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --kilat-shadow-glow: 0 0 30px rgba(59, 130, 246, 0.3);
  
  /* Spacing */
  --kilat-space-xs: 0.25rem;
  --kilat-space-sm: 0.5rem;
  --kilat-space-md: 1rem;
  --kilat-space-lg: 1.5rem;
  --kilat-space-xl: 2rem;
  --kilat-space-2xl: 3rem;
  
  /* Border Radius */
  --kilat-radius-sm: 0.25rem;
  --kilat-radius-md: 0.5rem;
  --kilat-radius-lg: 0.75rem;
  --kilat-radius-xl: 1rem;
  
  /* Animation Durations */
  --kilat-duration-fast: 0.15s;
  --kilat-duration-normal: 0.3s;
  --kilat-duration-slow: 0.5s;
  
  /* Z-Index */
  --kilat-z-dropdown: 1000;
  --kilat-z-sticky: 1020;
  --kilat-z-fixed: 1030;
  --kilat-z-modal: 1040;
  --kilat-z-popover: 1050;
  --kilat-z-tooltip: 1060;
}

/* Theme Variants */
[data-theme="cyber"] {
  --kilat-primary: #00ff88;
  --kilat-secondary: #ff0080;
  --kilat-accent: #00d4ff;
  --kilat-bg-primary: #000011;
  --kilat-bg-secondary: #001122;
  --kilat-shadow-glow: 0 0 30px rgba(0, 255, 136, 0.4);
}

[data-theme="nusantara"] {
  --kilat-primary: #d97706;
  --kilat-secondary: #dc2626;
  --kilat-accent: #059669;
  --kilat-bg-primary: #1c1917;
  --kilat-bg-secondary: #292524;
  --kilat-shadow-glow: 0 0 30px rgba(217, 119, 6, 0.4);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--kilat-bg-primary);
  color: var(--kilat-text-primary);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin: 0 0 var(--kilat-space-md) 0;
  color: var(--kilat-text-primary);
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.75rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 600; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

p {
  margin: 0 0 var(--kilat-space-md) 0;
  color: var(--kilat-text-secondary);
  line-height: 1.6;
}

a {
  color: var(--kilat-primary);
  text-decoration: none;
  transition: all var(--kilat-duration-normal) ease;
}

a:hover {
  color: var(--kilat-accent);
  text-shadow: 0 0 8px currentColor;
}

/* Layout Components */
.kilat-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--kilat-space-md);
}

.kilat-section {
  padding: var(--kilat-space-2xl) 0;
}

/* Button Components */
.kilat-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--kilat-space-md) var(--kilat-space-lg);
  border: none;
  border-radius: var(--kilat-radius-lg);
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--kilat-duration-normal) ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.kilat-btn-primary {
  background: var(--kilat-primary);
  color: white;
  box-shadow: var(--kilat-shadow-md);
}

.kilat-btn-primary:hover {
  background: var(--kilat-secondary);
  transform: translateY(-2px);
  box-shadow: var(--kilat-shadow-glow);
}

.kilat-btn-secondary {
  background: transparent;
  color: var(--kilat-primary);
  border: 2px solid var(--kilat-primary);
}

.kilat-btn-secondary:hover {
  background: var(--kilat-primary);
  color: white;
  box-shadow: var(--kilat-shadow-glow);
}

.kilat-btn-outline {
  background: transparent;
  color: var(--kilat-text-primary);
  border: 2px solid var(--kilat-border-primary);
}

.kilat-btn-outline:hover {
  border-color: var(--kilat-primary);
  color: var(--kilat-primary);
  box-shadow: var(--kilat-shadow-glow);
}

/* Card Components */
.kilat-card {
  background: var(--kilat-bg-secondary);
  border: 1px solid var(--kilat-border-primary);
  border-radius: var(--kilat-radius-xl);
  padding: var(--kilat-space-lg);
  transition: all var(--kilat-duration-normal) ease;
  box-shadow: var(--kilat-shadow-md);
}

.kilat-card:hover {
  transform: translateY(-4px);
  border-color: var(--kilat-primary);
  box-shadow: var(--kilat-shadow-glow);
}

/* Glass Morphism */
.kilat-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--kilat-radius-xl);
}

/* Gradient Effects */
.kilat-gradient-bg {
  background: linear-gradient(135deg, var(--kilat-primary), var(--kilat-secondary));
}

.kilat-gradient-text {
  background: linear-gradient(135deg, var(--kilat-primary), var(--kilat-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Glow Effects */
.kilat-glow {
  box-shadow: var(--kilat-shadow-glow);
}

.kilat-glow-hover:hover {
  box-shadow: var(--kilat-shadow-glow);
  transform: translateY(-2px);
}

.kilat-text-glow {
  text-shadow: 0 0 10px currentColor;
}

/* Animation Classes */
.kilat-fade-in {
  opacity: 0;
  animation: kilatFadeIn 0.6s ease-out forwards;
}

.kilat-slide-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: kilatSlideInUp 0.6s ease-out forwards;
}

.kilat-slide-in-left {
  opacity: 0;
  transform: translateX(-30px);
  animation: kilatSlideInLeft 0.6s ease-out forwards;
}

.kilat-slide-in-right {
  opacity: 0;
  transform: translateX(30px);
  animation: kilatSlideInRight 0.6s ease-out forwards;
}

.kilat-scale-in {
  opacity: 0;
  transform: scale(0.8);
  animation: kilatScaleIn 0.6s ease-out forwards;
}

/* Background Animations */
.kilat-hero-float {
  animation: kilatFloat 6s ease-in-out infinite;
}

.kilat-hero-pulse {
  animation: kilatPulse 4s ease-in-out infinite;
}

/* Keyframes */
@keyframes kilatFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes kilatSlideInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes kilatSlideInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes kilatSlideInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes kilatScaleIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes kilatFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes kilatPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .kilat-container { padding: 0 var(--kilat-space-sm); }
  .kilat-btn { padding: var(--kilat-space-sm) var(--kilat-space-md); font-size: 0.875rem; }
  .kilat-card { padding: var(--kilat-space-md); }
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}

@media (max-width: 480px) {
  .kilat-container { padding: 0 var(--kilat-space-xs); }
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .kilat-fade-in,
  .kilat-slide-in-up,
  .kilat-slide-in-left,
  .kilat-slide-in-right,
  .kilat-scale-in,
  .kilat-hero-float,
  .kilat-hero-pulse {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Focus States */
.kilat-btn:focus,
.kilat-card:focus {
  outline: 2px solid var(--kilat-primary);
  outline-offset: 2px;
}

/* Loading States */
.kilat-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--kilat-bg-primary);
}

.kilat-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--kilat-border-primary);
  border-top: 4px solid var(--kilat-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
