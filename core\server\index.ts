/**
 * 🚀 Kilat.js Advanced Server - SpeedRun™ Runtime
 * Next.js App Router-like server with SSG, SSR, CSR support
 */

import { serve } from 'bun'
import { join } from 'path'
import { existsSync } from 'fs'
import { router } from '../router'
import { renderer } from '../renderer'
import { serveStatic } from '../kernel/static'
import type { KilatContext, SpeedRunConfig } from '../types'

export class KilatServer {
  private config: SpeedRunConfig

  constructor(config: Partial<SpeedRunConfig> = {}) {
    this.config = {
      runtime: 'bun',
      port: 3000,
      host: 'localhost',
      cors: true,
      compression: true,
      staticFiles: true,
      hotReload: false,
      ...config
    }
  }

  /**
   * Start the server
   */
  async start(): Promise<void> {
    console.log('🚀 Starting Kilat.js SpeedRun™ Server...')
    
    // Initialize router
    console.log('🔍 Initializing advanced router...')
    
    const server = serve({
      port: this.config.port,
      hostname: this.config.host,
      fetch: this.handleRequest.bind(this),
      development: this.config.hotReload
    })

    console.log(`✅ Server running on http://${this.config.host}:${this.config.port}`)
    console.log(`🎯 Runtime: ${this.config.runtime}`)
    console.log(`📊 Routes: ${router.getAllRoutes().length}`)
    
    if (this.config.hotReload) {
      console.log('🔥 Hot reload enabled')
    }
  }

  /**
   * Handle incoming requests
   */
  private async handleRequest(request: Request): Promise<Response> {
    const url = new URL(request.url)
    const pathname = url.pathname
    const method = request.method

    try {
      // Add CORS headers if enabled
      const headers: Record<string, string> = {}
      if (this.config.cors) {
        headers['Access-Control-Allow-Origin'] = '*'
        headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
      }

      // Handle preflight requests
      if (method === 'OPTIONS') {
        return new Response(null, { status: 200, headers })
      }

      // Serve static files
      if (pathname.startsWith('/_kilat/') || pathname.startsWith('/public/')) {
        return await this.handleStaticFile(pathname)
      }

      // Handle API routes
      if (pathname.startsWith('/api/')) {
        return await this.handleApiRoute(pathname, request, headers)
      }

      // Handle page routes
      return await this.handlePageRoute(pathname, request, headers)

    } catch (error) {
      console.error('Server error:', error)
      return this.createErrorResponse(error as Error)
    }
  }

  /**
   * Handle static file requests
   */
  private async handleStaticFile(pathname: string): Promise<Response> {
    try {
      const context: KilatContext = {
        request: new Request(`http://localhost${pathname}`),
        pathname
      }
      
      return await serveStatic(pathname, context)
    } catch (error) {
      return new Response('Static file not found', { status: 404 })
    }
  }

  /**
   * Handle API route requests
   */
  private async handleApiRoute(
    pathname: string, 
    request: Request, 
    headers: Record<string, string>
  ): Promise<Response> {
    const routeMatch = router.matchRoute(pathname)
    
    if (!routeMatch || routeMatch.route.type !== 'api') {
      return new Response('API route not found', { 
        status: 404,
        headers: { ...headers, 'Content-Type': 'application/json' }
      })
    }

    try {
      // Load API handler
      const module = await import(routeMatch.route.component + '?t=' + Date.now())
      const method = request.method.toUpperCase()
      const handler = module[method] || module.default

      if (!handler) {
        return new Response(JSON.stringify({ error: 'Method not allowed' }), {
          status: 405,
          headers: { ...headers, 'Content-Type': 'application/json' }
        })
      }

      // Execute handler
      const response = await handler(request, routeMatch.params)
      
      // Add headers to response
      if (response instanceof Response) {
        Object.entries(headers).forEach(([key, value]) => {
          response.headers.set(key, value)
        })
        return response
      }

      return response

    } catch (error) {
      console.error('API route error:', error)
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...headers, 'Content-Type': 'application/json' }
      })
    }
  }

  /**
   * Handle page route requests
   */
  private async handlePageRoute(
    pathname: string,
    request: Request,
    headers: Record<string, string>
  ): Promise<Response> {
    const context: KilatContext = {
      request,
      pathname,
      searchParams: new URL(request.url).searchParams,
      headers: Object.fromEntries(request.headers.entries())
    }

    try {
      // Render page using advanced renderer
      const result = await renderer.render(pathname, context)
      
      return new Response(result.html, {
        status: result.statusCode || 200,
        headers: {
          ...headers,
          'Content-Type': 'text/html; charset=utf-8',
          ...result.headers
        }
      })

    } catch (error) {
      console.error('Page render error:', error)
      return this.createErrorResponse(error as Error)
    }
  }

  /**
   * Create error response
   */
  private createErrorResponse(error: Error): Response {
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Server Error | Kilat.js</title>
  <link rel="stylesheet" href="/_kilat/styles.css" />
</head>
<body>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-purple-900 flex items-center justify-center">
    <div class="text-center max-w-md">
      <h1 class="text-4xl font-bold text-white mb-4">Server Error</h1>
      <p class="text-gray-300 mb-8">${error.message}</p>
      <a href="/" class="kilat-btn kilat-btn-primary px-6 py-3">Go Home</a>
    </div>
  </div>
</body>
</html>`

    return new Response(html, {
      status: 500,
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    })
  }

  /**
   * Stop the server
   */
  async stop(): Promise<void> {
    console.log('🛑 Stopping Kilat.js server...')
  }

  /**
   * Get server statistics
   */
  getStats(): any {
    const routes = router.getAllRoutes()
    return {
      routes: routes.length,
      staticRoutes: router.getStaticRoutes().length,
      config: this.config
    }
  }
}

/**
 * Create and start server
 */
export async function createKilatServer(config?: Partial<SpeedRunConfig>): Promise<KilatServer> {
  const server = new KilatServer(config)
  await server.start()
  return server
}

// Export singleton instance
export const server = new KilatServer()
