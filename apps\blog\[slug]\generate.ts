/**
 * ⚡ Static Generation Config for Blog Posts
 * Generate static pages for all blog posts at build time
 */

import type { GenerateStaticParamsResult } from '../../../core/types'

// Mock blog data (in real app, fetch from CMS/database)
const blogSlugs = [
  'getting-started',
  'advanced-routing', 
  'performance-optimization',
  'building-with-kilat',
  'deployment-guide',
  'best-practices'
]

/**
 * Generate static params for all blog posts
 */
export async function generateStaticParams(): Promise<GenerateStaticParamsResult[]> {
  console.log('📝 Generating static params for blog posts...')
  
  // In a real application, you might fetch this from a CMS or database
  // const posts = await fetchBlogPosts()
  // return posts.map(post => ({ params: { slug: post.slug } }))
  
  return blogSlugs.map(slug => ({
    params: { slug }
  }))
}

/**
 * Static generation configuration
 */
export const config = {
  // Generate all paths at build time
  fallback: false,
  
  // Revalidate every hour in production (ISR)
  revalidate: 3600
}
