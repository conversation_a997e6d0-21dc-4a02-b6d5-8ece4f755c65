/**
 * 🧪 Runtime Tests
 */

import { describe, it, expect, beforeAll, afterAll } from 'bun:test'
import { createSpeedRunRuntime } from '../core/kernel/runtime'
import type { KilatConfig } from '../types/config'

describe('SpeedRun™ Runtime', () => {
  let runtime: any
  
  const testConfig: KilatConfig = {
    runtime: {
      engine: 'bun',
      port: 3001,
      host: 'localhost',
      https: false,
    },
    apps: {
      dir: './apps',
      extensions: ['.tsx', '.ts'],
      middleware: true,
      groups: true,
    },
    ui: {
      css: {
        framework: 'tailwind',
        themes: ['glow'],
        defaultTheme: 'glow',
      },
      animations: {
        enabled: true,
        presets: ['scroll'],
      },
    },
    render: {
      mode: 'ssr',
      streaming: true,
      rsc: true,
      suspense: true,
    },
    plugins: [],
    dev: {
      overlay: true,
      hmr: true,
      analytics: true,
    },
    build: {
      outDir: './dist',
      target: 'es2022',
      minify: true,
      sourcemap: true,
    },
    deploy: {
      platform: 'auto',
      edge: true,
    },
  }
  
  beforeAll(async () => {
    runtime = await createSpeedRunRuntime(testConfig)
  })
  
  afterAll(async () => {
    if (runtime) {
      await runtime.stop()
    }
  })
  
  it('should create runtime instance', () => {
    expect(runtime).toBeDefined()
    expect(typeof runtime.start).toBe('function')
    expect(typeof runtime.stop).toBe('function')
    expect(typeof runtime.getStatus).toBe('function')
  })
  
  it('should have correct initial status', () => {
    const status = runtime.getStatus()
    
    expect(status.isRunning).toBe(false)
    expect(status.engine).toBe('bun')
    expect(status.port).toBe(3001)
    expect(status.host).toBe('localhost')
  })
  
  it('should start and stop runtime', async () => {
    // Start runtime
    await runtime.start()
    
    let status = runtime.getStatus()
    expect(status.isRunning).toBe(true)
    
    // Stop runtime
    await runtime.stop()
    
    status = runtime.getStatus()
    expect(status.isRunning).toBe(false)
  })
})

describe('Router', () => {
  it('should match routes correctly', async () => {
    const { matchRoute } = await import('../core/kernel/router')
    
    const routes = [
      { path: '/', component: 'page.tsx' },
      { path: '/about', component: 'about/page.tsx' },
      { path: '/users/:id', component: 'users/[id]/page.tsx' },
      { path: '/posts/*slug', component: 'posts/[...slug]/page.tsx' },
    ]
    
    // Test exact match
    const homeMatch = matchRoute('/', routes)
    expect(homeMatch).toBeDefined()
    expect(homeMatch?.route.path).toBe('/')
    
    // Test static route
    const aboutMatch = matchRoute('/about', routes)
    expect(aboutMatch).toBeDefined()
    expect(aboutMatch?.route.path).toBe('/about')
    
    // Test dynamic route
    const userMatch = matchRoute('/users/123', routes)
    expect(userMatch).toBeDefined()
    expect(userMatch?.route.path).toBe('/users/:id')
    expect(userMatch?.params.id).toBe('123')
    
    // Test catch-all route
    const postMatch = matchRoute('/posts/2023/hello-world', routes)
    expect(postMatch).toBeDefined()
    expect(postMatch?.route.path).toBe('/posts/*slug')
    expect(postMatch?.params.slug).toBe('2023/hello-world')
    
    // Test no match
    const noMatch = matchRoute('/nonexistent', routes)
    expect(noMatch).toBeNull()
  })
})

describe('Cache Manager', () => {
  it('should cache and retrieve values', async () => {
    const { initializeCache, getCache } = await import('../core/systems/cache/manager')
    
    // Initialize cache
    await initializeCache({
      config: testConfig,
      routes: [],
      plugins: [],
      mode: 'development'
    })
    
    const cache = getCache()
    
    // Set and get value
    cache.set('test-key', 'test-value', 60)
    const result = cache.get('test-key')
    
    expect(result).toBeDefined()
    expect(result.value).toBe('test-value')
    expect(result.isStale).toBe(false)
    
    // Test expiration
    cache.set('expired-key', 'expired-value', 0)
    
    // Wait a bit for expiration
    await new Promise(resolve => setTimeout(resolve, 10))
    
    const expiredResult = cache.get('expired-key')
    expect(expiredResult).toBeUndefined()
  })
})

describe('Plugin System', () => {
  it('should load and execute plugins', async () => {
    const { initializePlugins } = await import('../core/systems/plugin/manager')
    
    let hookExecuted = false
    
    const testPlugin = {
      name: 'test-plugin',
      version: '1.0.0',
      hooks: {
        'plugin:init': async () => {
          hookExecuted = true
        }
      }
    }
    
    const context = {
      config: testConfig,
      routes: [],
      plugins: [],
      mode: 'development' as const
    }
    
    const plugins = await initializePlugins([testPlugin], context)
    
    expect(plugins).toHaveLength(1)
    expect(plugins[0].name).toBe('test-plugin')
    expect(hookExecuted).toBe(true)
  })
})
