{"name": "kilat.js", "version": "1.0.0", "description": "⚡ Kilat.js - Modern Fullstack Framework with SpeedRun™ Runtime", "type": "module", "main": "dist/index.js", "bin": {"kilat": "./kilat.cli.js"}, "scripts": {"dev": "bun run kilat.cli.ts dev", "build": "bun run kilat.cli.ts build", "start": "bun run kilat.cli.ts start", "quick-start": "bun run scripts/start.ts", "test": "bun test", "test:watch": "bun test --watch", "test:e2e": "playwright test", "test:all": "bun test && playwright test", "generate": "bun run kilat.cli.ts generate", "graph": "bun run kilat.cli.ts graph", "upgrade": "bun run kilat.cli.ts upgrade", "export": "bun run kilat.cli.ts export", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "keywords": ["framework", "fullstack", "bun", "typescript", "react", "ssr", "rsc", "streaming", "file-based-routing", "speedrun"], "author": "Kilat.js Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.3.0", "tailwindcss": "^3.4.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/aspect-ratio": "^0.4.2", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "framer-motion": "^10.16.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.2.0", "chokidar": "^3.5.3", "ws": "^8.16.0"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^20.10.0", "@types/ws": "^8.5.10", "bun-types": "latest", "playwright": "^1.40.0", "@playwright/test": "^1.40.0", "prettier": "^3.1.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "peerDependencies": {"bun": ">=1.0.0"}, "engines": {"bun": ">=1.0.0", "node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/kangpcode/kilat.js.git"}, "bugs": {"url": "https://github.com/kangpcode/kilat.js/issues"}, "homepage": "https://kilat-js.pcode.my.id"}