/**
 * 🎨 KilatCSS - Production Styles
 * Optimized CSS for Kilat.js applications
 */

/* Import KilatCSS globals */
@import url('../../core/systems/css/globals.css');

/* Additional production optimizations */
.kilat-optimized {
  will-change: transform;
  transform: translateZ(0);
}

/* Smooth scrolling for all browsers */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.kilat-btn:focus,
.kilat-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .kilat-no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .kilat-card {
    border-width: 2px;
  }
  
  .kilat-btn {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .kilat-fade-in,
  .kilat-slide-in-left,
  .kilat-slide-in-right,
  .kilat-scale-in {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .kilat-hero-float,
  .kilat-hero-pulse {
    animation: none;
  }
}

/* Dark mode utilities */
@media (prefers-color-scheme: dark) {
  :root {
    --kilat-background: #0f172a;
    --kilat-surface: #1e293b;
    --kilat-text: #f8fafc;
  }
}

/* Light mode utilities */
@media (prefers-color-scheme: light) {
  :root {
    --kilat-background: #ffffff;
    --kilat-surface: #f8fafc;
    --kilat-text: #1e293b;
  }
}
