<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js SpeedRun™ Runtime" />
  
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><section class="py-20 sm:py-32"><div class="kilat-container"><div class="max-w-md mx-auto"><div class="text-center mb-8 kilat-fade-in"><div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4"><span class="text-white text-2xl">🔑</span></div><h1 class="text-3xl font-bold text-white mb-2">Welcome Back</h1><p class="text-gray-400">Sign in to access your Kilat.js dashboard</p></div><div class="kilat-card kilat-glass p-8 kilat-fade-in" style="animation-delay:0.2s"><form class="space-y-6" data-kilat-form="true" action="/api/auth/login" method="POST"><div><label for="email" class="block text-sm font-medium text-white mb-2">Email Address</label><input type="email" id="email" name="email" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" placeholder="Enter your email"/></div><div><label for="password" class="block text-sm font-medium text-white mb-2">Password</label><input type="password" id="password" name="password" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" placeholder="Enter your password"/></div><div class="flex items-center justify-between"><label class="flex items-center"><input type="checkbox" name="remember" class="w-4 h-4 text-blue-500 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2"/><span class="ml-2 text-sm text-gray-300">Remember me</span></label><a href="/forgot-password" class="text-sm text-blue-400 hover:text-blue-300 transition-colors">Forgot password?</a></div><button type="submit" data-original-text="Sign In" class="w-full kilat-btn kilat-btn-primary py-3 text-lg font-semibold kilat-glow-hover transition-all duration-300"><span class="mr-2">🚀</span>Sign In</button><div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-white/20"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-gray-900 text-gray-400">Or continue with</span></div></div><div class="grid grid-cols-2 gap-4"><button type="button" class="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"><span>🐙</span><span>GitHub</span></button><button type="button" class="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"><span>🌐</span><span>Google</span></button></div></form></div><div class="text-center mt-8 kilat-fade-in" style="animation-delay:0.4s"><p class="text-gray-400">Don&#x27;t have an account?<!-- --> <a href="/register" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">Create one here</a></p></div><div class="mt-8 kilat-card kilat-glass p-4 kilat-fade-in" style="animation-delay:0.6s"><h3 class="text-white font-medium mb-2">Demo Credentials</h3><div class="text-sm text-gray-300 space-y-1"><p><strong>Email:</strong> <EMAIL></p><p><strong>Password:</strong> demo123</p></div></div></div></div></section><section class="py-16"><div class="kilat-container"><div class="text-center mb-12"><h2 class="text-3xl font-bold text-white mb-4">Why Choose Kilat.js?</h2><p class="text-gray-400 max-w-2xl mx-auto">Experience the power of modern web development with our lightning-fast framework</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8"><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:0.8s"><div class="text-3xl mb-4">⚡</div><h3 class="text-lg font-bold text-white mb-2">Lightning Fast</h3><p class="text-gray-300 text-sm">SpeedRun™ Runtime delivers unmatched performance</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:1.0s"><div class="text-3xl mb-4">🛠️</div><h3 class="text-lg font-bold text-white mb-2">Developer Friendly</h3><p class="text-gray-300 text-sm">Zero configuration, maximum productivity</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:1.2s"><div class="text-3xl mb-4">🚀</div><h3 class="text-lg font-bold text-white mb-2">Production Ready</h3><p class="text-gray-300 text-sm">Built-in optimizations and best practices</p></div></div></div></section></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>