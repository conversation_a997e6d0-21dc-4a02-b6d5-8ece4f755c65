<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js SpeedRun™ Runtime" />
  
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><div class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-x-hidden"><div class="fixed inset-0 pointer-events-none z-0"><div class="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl kilat-hero-float"></div><div class="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl kilat-hero-pulse"></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"></div></div><header class="relative z-50 kilat-glass border-b border-white/10"><div class="kilat-container"><div class="flex items-center justify-between h-16"><a href="/" class="flex items-center space-x-2 kilat-glow-hover transition-all duration-300"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></a><nav class="hidden md:flex items-center space-x-8"><a href="/" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🛠️ API</a></nav><div class="flex items-center space-x-4"><div class="flex items-center space-x-3"><a href="/login" class="kilat-btn kilat-btn-outline px-4 py-2 text-sm">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary px-4 py-2 text-sm">✨ Register</a></div><button class="md:hidden kilat-btn kilat-btn-outline px-3 py-2"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></div><div class="md:hidden border-t border-white/10 py-4 hidden" id="mobile-menu"><div class="flex flex-col space-y-2"><a href="/" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🛠️ API</a><div class="flex flex-col space-y-2 pt-4 border-t border-white/10"><a href="/login" class="kilat-btn kilat-btn-outline text-center">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary text-center">✨ Register</a></div></div></div></div></header><main class="relative z-10"><section class="py-20 sm:py-32"><div class="kilat-container"><div class="max-w-md mx-auto"><div class="text-center mb-8 kilat-fade-in"><div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4"><span class="text-white text-2xl">🔑</span></div><h1 class="text-3xl font-bold text-white mb-2">Welcome Back</h1><p class="text-gray-400">Sign in to access your Kilat.js dashboard</p></div><div class="kilat-card kilat-glass p-8 kilat-fade-in" style="animation-delay:0.2s"><form class="space-y-6" data-kilat-form="true" action="/api/auth/login" method="POST"><div><label for="email" class="block text-sm font-medium text-white mb-2">Email Address</label><input type="email" id="email" name="email" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" placeholder="Enter your email"/></div><div><label for="password" class="block text-sm font-medium text-white mb-2">Password</label><input type="password" id="password" name="password" required="" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300" placeholder="Enter your password"/></div><div class="flex items-center justify-between"><label class="flex items-center"><input type="checkbox" name="remember" class="w-4 h-4 text-blue-500 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2"/><span class="ml-2 text-sm text-gray-300">Remember me</span></label><a href="/forgot-password" class="text-sm text-blue-400 hover:text-blue-300 transition-colors">Forgot password?</a></div><button type="submit" data-original-text="Sign In" class="w-full kilat-btn kilat-btn-primary py-3 text-lg font-semibold kilat-glow-hover transition-all duration-300"><span class="mr-2">🚀</span>Sign In</button><div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-white/20"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-gray-900 text-gray-400">Or continue with</span></div></div><div class="grid grid-cols-2 gap-4"><button type="button" class="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"><span>🐙</span><span>GitHub</span></button><button type="button" class="kilat-btn kilat-btn-outline py-3 flex items-center justify-center space-x-2"><span>🌐</span><span>Google</span></button></div></form></div><div class="text-center mt-8 kilat-fade-in" style="animation-delay:0.4s"><p class="text-gray-400">Don&#x27;t have an account?<!-- --> <a href="/register" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">Create one here</a></p></div><div class="mt-8 kilat-card kilat-glass p-4 kilat-fade-in" style="animation-delay:0.6s"><h3 class="text-white font-medium mb-2">Demo Credentials</h3><div class="text-sm text-gray-300 space-y-1"><p><strong>Email:</strong> <EMAIL></p><p><strong>Password:</strong> demo123</p></div></div></div></div></section><section class="py-16"><div class="kilat-container"><div class="text-center mb-12"><h2 class="text-3xl font-bold text-white mb-4">Why Choose Kilat.js?</h2><p class="text-gray-400 max-w-2xl mx-auto">Experience the power of modern web development with our lightning-fast framework</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8"><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:0.8s"><div class="text-3xl mb-4">⚡</div><h3 class="text-lg font-bold text-white mb-2">Lightning Fast</h3><p class="text-gray-300 text-sm">SpeedRun™ Runtime delivers unmatched performance</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:1.0s"><div class="text-3xl mb-4">🛠️</div><h3 class="text-lg font-bold text-white mb-2">Developer Friendly</h3><p class="text-gray-300 text-sm">Zero configuration, maximum productivity</p></div><div class="kilat-card kilat-glass p-6 text-center kilat-fade-in" style="animation-delay:1.2s"><div class="text-3xl mb-4">🚀</div><h3 class="text-lg font-bold text-white mb-2">Production Ready</h3><p class="text-gray-300 text-sm">Built-in optimizations and best practices</p></div></div></div></section></main><footer class="relative z-10 mt-20 border-t border-white/10 kilat-glass"><div class="kilat-container py-12"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="col-span-1 md:col-span-2"><div class="flex items-center space-x-2 mb-4"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></div><p class="text-gray-400 max-w-md mb-6">Modern fullstack framework with SpeedRun™ Runtime. Built for speed, developer experience, and production-ready applications.</p><div class="flex items-center space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">📧</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐙</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐦</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">💬</span></a></div></div><div><h3 class="text-white font-semibold mb-4">Quick Links</h3><ul class="space-y-2"><li><a href="/" class="text-gray-400 hover:text-white transition-colors">🏠 Home</a></li><li><a href="/about" class="text-gray-400 hover:text-white transition-colors">📄 About</a></li><li><a href="/dashboard" class="text-gray-400 hover:text-white transition-colors">📊 Dashboard</a></li><li><a href="/api/users" class="text-gray-400 hover:text-white transition-colors">🛠️ API</a></li><li><a href="/login" class="text-gray-400 hover:text-white transition-colors">🔑 Login</a></li></ul></div><div><h3 class="text-white font-semibold mb-4">Resources</h3><ul class="space-y-2"><li><a href="#" class="text-gray-400 hover:text-white transition-colors">📚 Documentation</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🚀 Getting Started</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">💡 Examples</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🎓 Tutorials</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">👥 Community</a></li></ul></div></div><div class="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row items-center justify-between"><p class="text-gray-400 text-sm">© 2024 Kilat.js. Built with ❤️ using SpeedRun™ Runtime.</p><div class="flex items-center space-x-6 mt-4 md:mt-0"><span class="text-gray-400 text-sm">Powered by</span><div class="flex items-center space-x-4"><div class="flex items-center space-x-2"><span class="text-yellow-400">🟡</span><span class="text-gray-300 text-sm font-medium">Bun.js</span></div><div class="flex items-center space-x-2"><span class="text-blue-400">⚛️</span><span class="text-gray-300 text-sm font-medium">React 18</span></div><div class="flex items-center space-x-2"><span class="text-purple-400">⚡</span><span class="text-gray-300 text-sm font-medium">TypeScript</span></div></div></div></div><div class="mt-8 text-center"><div class="inline-flex items-center px-4 py-2 kilat-glass rounded-full text-sm text-gray-300"><span class="mr-2">⚡</span>SpeedRun™ Runtime - Lightning Fast Performance<span class="ml-2 text-green-400">●</span></div></div></div></footer></div></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>