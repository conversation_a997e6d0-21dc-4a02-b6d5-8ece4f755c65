/**
 * 📝 Dynamic Blog Post Page
 * Example of dynamic routing with SSG
 */

import React from 'react'
import type { PageProps, GenerateMetadataResult } from '../../../core/types'

interface BlogPost {
  slug: string
  title: string
  content: string
  author: string
  date: string
  tags: string[]
}

// Mock blog data
const blogPosts: BlogPost[] = [
  {
    slug: 'getting-started',
    title: 'Getting Started with Kilat.js',
    content: 'Learn how to build lightning-fast applications with Kilat.js framework...',
    author: 'Kilat Team',
    date: '2024-01-15',
    tags: ['tutorial', 'getting-started']
  },
  {
    slug: 'advanced-routing',
    title: 'Advanced Routing in Kilat.js',
    content: 'Explore the powerful routing system with SSG, SSR, and CSR support...',
    author: 'Kilat Team',
    date: '2024-01-20',
    tags: ['routing', 'advanced']
  },
  {
    slug: 'performance-optimization',
    title: 'Performance Optimization with SpeedRun™',
    content: 'Discover how SpeedRun™ Runtime delivers unmatched performance...',
    author: 'Kilat Team',
    date: '2024-01-25',
    tags: ['performance', 'optimization']
  }
]

export default function BlogPostPage({ params }: PageProps) {
  const { slug } = params || {}
  const post = blogPosts.find(p => p.slug === slug)

  if (!post) {
    return (
      <section className="py-20">
        <div className="kilat-container text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Post Not Found</h1>
          <p className="text-gray-400 mb-8">The blog post you're looking for doesn't exist.</p>
          <a href="/blog" className="kilat-btn kilat-btn-primary px-6 py-3">
            Back to Blog
          </a>
        </div>
      </section>
    )
  }

  return (
    <>
      {/* Blog Post Header */}
      <section className="py-20">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumb */}
            <nav className="mb-8 kilat-fade-in">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <a href="/" className="hover:text-white transition-colors">Home</a>
                <span>/</span>
                <a href="/blog" className="hover:text-white transition-colors">Blog</a>
                <span>/</span>
                <span className="text-white">{post.title}</span>
              </div>
            </nav>

            {/* Post Header */}
            <header className="mb-12 kilat-fade-in" style={{ animationDelay: '0.2s' }}>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                {post.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-6 text-gray-400">
                <div className="flex items-center space-x-2">
                  <span className="text-blue-400">👤</span>
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-400">📅</span>
                  <span>{new Date(post.date).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-purple-400">🏷️</span>
                  <div className="flex gap-2">
                    {post.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 bg-white/10 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </header>
          </div>
        </div>
      </section>

      {/* Blog Post Content */}
      <section className="pb-20">
        <div className="kilat-container">
          <div className="max-w-4xl mx-auto">
            <article className="kilat-card kilat-glass p-8 kilat-fade-in" style={{ animationDelay: '0.4s' }}>
              <div className="prose prose-invert max-w-none">
                <p className="text-lg text-gray-300 leading-relaxed">
                  {post.content}
                </p>
                
                <h2 className="text-2xl font-bold text-white mt-8 mb-4">Key Features</h2>
                <ul className="text-gray-300 space-y-2">
                  <li>⚡ Lightning-fast performance with SpeedRun™ Runtime</li>
                  <li>🎨 Beautiful UI with KilatCSS framework</li>
                  <li>🎭 Smooth animations with KilatAnim</li>
                  <li>📱 Mobile-first responsive design</li>
                  <li>🔒 Built-in security best practices</li>
                </ul>

                <h2 className="text-2xl font-bold text-white mt-8 mb-4">Getting Started</h2>
                <div className="bg-gray-800 rounded-lg p-4 my-6">
                  <code className="text-green-400">
                    bun create kilat-app my-app<br/>
                    cd my-app<br/>
                    bun run dev
                  </code>
                </div>

                <p className="text-gray-300">
                  Start building amazing applications with Kilat.js today!
                </p>
              </div>
            </article>

            {/* Navigation */}
            <div className="mt-12 flex justify-between items-center kilat-fade-in" style={{ animationDelay: '0.6s' }}>
              <a href="/blog" className="kilat-btn kilat-btn-outline px-6 py-3">
                ← Back to Blog
              </a>
              <div className="flex space-x-4">
                <button className="kilat-btn kilat-btn-outline px-4 py-2 text-sm">
                  Share
                </button>
                <button className="kilat-btn kilat-btn-outline px-4 py-2 text-sm">
                  Bookmark
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<GenerateMetadataResult> {
  const { slug } = params || {}
  const post = blogPosts.find(p => p.slug === slug)

  if (!post) {
    return {
      title: 'Post Not Found | Kilat.js Blog',
      description: 'The blog post you are looking for could not be found.'
    }
  }

  return {
    title: `${post.title} | Kilat.js Blog`,
    description: post.content.substring(0, 160) + '...',
    keywords: ['kilat.js', 'blog', ...post.tags],
    openGraph: {
      title: post.title,
      description: post.content.substring(0, 160) + '...',
      images: [`/api/og?title=${encodeURIComponent(post.title)}`]
    }
  }
}
