/**
 * 🚀 Preload System - Resource Preloading and Prefetching
 */

import type { KilatContext } from '../../../types/config'

export interface PreloadResource {
  type: 'script' | 'style' | 'font' | 'image' | 'fetch' | 'dns-prefetch' | 'preconnect'
  href: string
  as?: string
  crossOrigin?: 'anonymous' | 'use-credentials'
  media?: string
  importance?: 'auto' | 'high' | 'low'
  nonce?: string
}

export interface PreloadOptions {
  priority?: 'critical' | 'high' | 'medium' | 'low'
  inHead?: boolean
  disabled?: boolean
}

class PreloadManager {
  private resources: Map<string, PreloadResource & PreloadOptions> = new Map()
  private routeResources: Map<string, Set<string>> = new Map()
  
  /**
   * Register resource for preloading
   */
  register(
    resource: PreloadResource,
    options: PreloadOptions = {},
    routes: string[] = []
  ): void {
    const key = this.getResourceKey(resource)
    
    this.resources.set(key, {
      ...resource,
      ...options
    })
    
    // Associate resource with routes
    if (routes.length > 0) {
      for (const route of routes) {
        if (!this.routeResources.has(route)) {
          this.routeResources.set(route, new Set())
        }
        
        this.routeResources.get(route)!.add(key)
      }
    }
  }
  
  /**
   * Get resources for a specific route
   */
  getResourcesForRoute(route: string): (PreloadResource & PreloadOptions)[] {
    const resources: (PreloadResource & PreloadOptions)[] = []
    
    // Get resources specifically registered for this route
    const routeResourceKeys = this.routeResources.get(route)
    if (routeResourceKeys) {
      for (const key of routeResourceKeys) {
        const resource = this.resources.get(key)
        if (resource && !resource.disabled) {
          resources.push(resource)
        }
      }
    }
    
    // Get global resources (not associated with any route)
    for (const [key, resource] of this.resources.entries()) {
      if (!resource.disabled && !this.isResourceAssociatedWithAnyRoute(key)) {
        resources.push(resource)
      }
    }
    
    // Sort by priority
    return resources.sort((a, b) => {
      const priorityOrder = {
        critical: 0,
        high: 1,
        medium: 2,
        low: 3
      }
      
      const aPriority = a.priority || 'medium'
      const bPriority = b.priority || 'medium'
      
      return priorityOrder[aPriority] - priorityOrder[bPriority]
    })
  }
  
  /**
   * Check if resource is associated with any route
   */
  private isResourceAssociatedWithAnyRoute(resourceKey: string): boolean {
    for (const resourceKeys of this.routeResources.values()) {
      if (resourceKeys.has(resourceKey)) {
        return true
      }
    }
    
    return false
  }
  
  /**
   * Generate preload tags for HTML head
   */
  generateHeadTags(route: string): string {
    const resources = this.getResourcesForRoute(route)
      .filter(resource => resource.inHead !== false)
    
    let tags = ''
    
    for (const resource of resources) {
      switch (resource.type) {
        case 'script':
          tags += `<link rel="preload" href="${resource.href}" as="script"${this.getAdditionalAttributes(resource)}>\n`
          break
        case 'style':
          tags += `<link rel="preload" href="${resource.href}" as="style"${this.getAdditionalAttributes(resource)}>\n`
          break
        case 'font':
          tags += `<link rel="preload" href="${resource.href}" as="font" crossorigin="anonymous"${this.getAdditionalAttributes(resource)}>\n`
          break
        case 'image':
          tags += `<link rel="preload" href="${resource.href}" as="image"${this.getAdditionalAttributes(resource)}>\n`
          break
        case 'fetch':
          tags += `<link rel="prefetch" href="${resource.href}"${this.getAdditionalAttributes(resource)}>\n`
          break
        case 'dns-prefetch':
          tags += `<link rel="dns-prefetch" href="${resource.href}">\n`
          break
        case 'preconnect':
          tags += `<link rel="preconnect" href="${resource.href}" crossorigin="anonymous">\n`
          break
      }
    }
    
    return tags
  }
  
  /**
   * Generate preload script for client-side navigation
   */
  generateClientScript(route: string): string {
    const resources = this.getResourcesForRoute(route)
    
    return `
<script>
  // Kilat.js Preload System
  (function() {
    const resources = ${JSON.stringify(resources)};
    
    // Preload resources
    function preloadResources() {
      resources.forEach(resource => {
        if (resource.type === 'script') {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'script';
          link.href = resource.href;
          if (resource.crossOrigin) link.crossOrigin = resource.crossOrigin;
          document.head.appendChild(link);
        } else if (resource.type === 'style') {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'style';
          link.href = resource.href;
          document.head.appendChild(link);
        }
      });
    }
    
    // Prefetch on idle
    if ('requestIdleCallback' in window) {
      requestIdleCallback(preloadResources);
    } else {
      setTimeout(preloadResources, 1000);
    }
    
    // Preload on mouse hover for navigation
    document.addEventListener('mouseover', function(e) {
      const target = e.target.closest('a');
      if (target && target.href && target.href.startsWith(window.location.origin)) {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = target.href;
        document.head.appendChild(link);
      }
    }, { passive: true });
  })();
</script>
    `
  }
  
  /**
   * Get additional HTML attributes
   */
  private getAdditionalAttributes(resource: PreloadResource & PreloadOptions): string {
    let attributes = ''
    
    if (resource.crossOrigin) {
      attributes += ` crossorigin="${resource.crossOrigin}"`
    }
    
    if (resource.media) {
      attributes += ` media="${resource.media}"`
    }
    
    if (resource.importance) {
      attributes += ` importance="${resource.importance}"`
    }
    
    if (resource.nonce) {
      attributes += ` nonce="${resource.nonce}"`
    }
    
    return attributes
  }
  
  /**
   * Get resource key
   */
  private getResourceKey(resource: PreloadResource): string {
    return `${resource.type}:${resource.href}`
  }
  
  /**
   * Clear all resources
   */
  clear(): void {
    this.resources.clear()
    this.routeResources.clear()
  }
}

// Global preload manager
const preloadManager = new PreloadManager()

/**
 * Register resource for preloading
 */
export function registerPreload(
  resource: PreloadResource,
  options: PreloadOptions = {},
  routes: string[] = []
): void {
  preloadManager.register(resource, options, routes)
}

/**
 * Generate preload tags for HTML head
 */
export function generatePreloadTags(route: string): string {
  return preloadManager.generateHeadTags(route)
}

/**
 * Generate preload script for client-side navigation
 */
export function generatePreloadScript(route: string): string {
  return preloadManager.generateClientScript(route)
}

/**
 * Get preload manager
 */
export function getPreloadManager(): PreloadManager {
  return preloadManager
}

/**
 * Register common resources
 */
export function registerCommonResources(context: KilatContext): void {
  // Register framework scripts
  registerPreload(
    { type: 'script', href: '/_kilat/runtime.js' },
    { priority: 'critical', inHead: true }
  )
  
  registerPreload(
    { type: 'script', href: '/_kilat/client.js' },
    { priority: 'critical', inHead: true }
  )
  
  // Register framework styles
  registerPreload(
    { type: 'style', href: '/_kilat/styles.css' },
    { priority: 'critical', inHead: true }
  )
  
  // Register common fonts
  registerPreload(
    { type: 'font', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' },
    { priority: 'high' }
  )
  
  // Preconnect to common domains
  registerPreload(
    { type: 'preconnect', href: 'https://fonts.googleapis.com' },
    { priority: 'high', inHead: true }
  )
  
  registerPreload(
    { type: 'preconnect', href: 'https://fonts.gstatic.com' },
    { priority: 'high', inHead: true }
  )
}
