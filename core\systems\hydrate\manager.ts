/**
 * 💧 Hydration Manager - Client-side Hydration System
 */

import type { KilatContext } from '../../../types/config'

export interface HydrationOptions {
  strategy: 'immediate' | 'lazy' | 'partial' | 'progressive'
  priority: 'high' | 'medium' | 'low'
  threshold?: number
  delay?: number
}

export interface HydrationTarget {
  id: string
  component: string
  props: Record<string, any>
  options: HydrationOptions
}

class HydrationManager {
  private targets: Map<string, HydrationTarget> = new Map()
  private hydrated: Set<string> = new Set()
  private observer?: IntersectionObserver
  
  constructor() {
    this.setupIntersectionObserver()
  }
  
  /**
   * Register component for hydration
   */
  register(target: HydrationTarget): void {
    this.targets.set(target.id, target)
    
    switch (target.options.strategy) {
      case 'immediate':
        this.hydrateImmediate(target)
        break
      case 'lazy':
        this.hydrateLazy(target)
        break
      case 'partial':
        this.hydratePartial(target)
        break
      case 'progressive':
        this.hydrateProgressive(target)
        break
    }
  }
  
  /**
   * Hydrate component immediately
   */
  private async hydrateImmediate(target: HydrationTarget): Promise<void> {
    if (target.options.delay) {
      await new Promise(resolve => setTimeout(resolve, target.options.delay))
    }
    
    await this.hydrateComponent(target)
  }
  
  /**
   * Hydrate component when it enters viewport
   */
  private hydrateLazy(target: HydrationTarget): void {
    const element = document.getElementById(target.id)
    if (!element) return
    
    if (this.observer) {
      this.observer.observe(element)
    }
  }
  
  /**
   * Hydrate only interactive parts of component
   */
  private async hydratePartial(target: HydrationTarget): Promise<void> {
    const element = document.getElementById(target.id)
    if (!element) return
    
    // Find interactive elements
    const interactiveElements = element.querySelectorAll('[data-interactive]')
    
    for (const interactiveElement of interactiveElements) {
      await this.hydrateElement(interactiveElement as HTMLElement, target)
    }
  }
  
  /**
   * Hydrate component progressively based on priority
   */
  private async hydrateProgressive(target: HydrationTarget): Promise<void> {
    const delay = this.getPriorityDelay(target.options.priority)
    
    await new Promise(resolve => setTimeout(resolve, delay))
    await this.hydrateComponent(target)
  }
  
  /**
   * Hydrate a specific component
   */
  private async hydrateComponent(target: HydrationTarget): Promise<void> {
    if (this.hydrated.has(target.id)) return
    
    try {
      const element = document.getElementById(target.id)
      if (!element) return
      
      // Load component module
      const componentModule = await import(target.component)
      const Component = componentModule.default || componentModule
      
      // Create React element
      const reactElement = React.createElement(Component, target.props)
      
      // Hydrate with React
      const { hydrateRoot } = await import('react-dom/client')
      hydrateRoot(element, reactElement)
      
      this.hydrated.add(target.id)
      
      console.log(`💧 Hydrated component: ${target.id}`)
      
    } catch (error) {
      console.error(`❌ Hydration error for ${target.id}:`, error)
    }
  }
  
  /**
   * Hydrate a specific element
   */
  private async hydrateElement(element: HTMLElement, target: HydrationTarget): Promise<void> {
    const elementId = element.id || `${target.id}-${Math.random().toString(36).substring(2, 9)}`
    
    if (this.hydrated.has(elementId)) return
    
    try {
      // Load component module
      const componentModule = await import(target.component)
      const Component = componentModule.default || componentModule
      
      // Create React element for this specific element
      const reactElement = React.createElement(Component, {
        ...target.props,
        elementId
      })
      
      // Hydrate with React
      const { hydrateRoot } = await import('react-dom/client')
      hydrateRoot(element, reactElement)
      
      this.hydrated.add(elementId)
      
    } catch (error) {
      console.error(`❌ Element hydration error for ${elementId}:`, error)
    }
  }
  
  /**
   * Setup intersection observer for lazy hydration
   */
  private setupIntersectionObserver(): void {
    if (typeof IntersectionObserver === 'undefined') return
    
    this.observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          if (entry.isIntersecting) {
            const targetId = entry.target.id
            const target = this.targets.get(targetId)
            
            if (target && !this.hydrated.has(targetId)) {
              this.hydrateComponent(target)
              this.observer?.unobserve(entry.target)
            }
          }
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    )
  }
  
  /**
   * Get delay based on priority
   */
  private getPriorityDelay(priority: 'high' | 'medium' | 'low'): number {
    switch (priority) {
      case 'high': return 0
      case 'medium': return 100
      case 'low': return 500
      default: return 0
    }
  }
  
  /**
   * Check if component is hydrated
   */
  isHydrated(id: string): boolean {
    return this.hydrated.has(id)
  }
  
  /**
   * Get hydration stats
   */
  getStats() {
    return {
      total: this.targets.size,
      hydrated: this.hydrated.size,
      pending: this.targets.size - this.hydrated.size
    }
  }
}

let hydrationManager: HydrationManager

/**
 * Initialize hydration manager
 */
export function initializeHydration(): HydrationManager {
  if (!hydrationManager) {
    hydrationManager = new HydrationManager()
  }
  return hydrationManager
}

/**
 * Get hydration manager instance
 */
export function getHydrationManager(): HydrationManager | undefined {
  return hydrationManager
}

/**
 * Register component for hydration
 */
export function registerForHydration(target: HydrationTarget): void {
  if (!hydrationManager) {
    hydrationManager = initializeHydration()
  }
  hydrationManager.register(target)
}

/**
 * Create hydration script for client
 */
export function createHydrationScript(targets: HydrationTarget[]): string {
  return `
    <script>
      window.__KILAT_HYDRATION_TARGETS__ = ${JSON.stringify(targets)};
      
      // Initialize hydration when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeHydration);
      } else {
        initializeHydration();
      }
      
      function initializeHydration() {
        const targets = window.__KILAT_HYDRATION_TARGETS__ || [];
        
        for (const target of targets) {
          window.__KILAT_HYDRATION__.register(target);
        }
      }
    </script>
  `
}
