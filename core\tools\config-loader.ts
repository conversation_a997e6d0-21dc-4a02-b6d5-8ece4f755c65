/**
 * ⚙️ Configuration Loader
 */

import { existsSync } from 'fs'
import { join } from 'path'
import type { KilatConfig } from '../../types/config'
import { loadModule } from '../kernel/esm-loader'

/**
 * Load Kilat.js configuration
 */
export async function loadConfig(): Promise<KilatConfig> {
  const configPaths = [
    'kilat.config.ts',
    'kilat.config.js',
    'kilat.config.mjs'
  ]
  
  for (const configPath of configPaths) {
    const fullPath = join(process.cwd(), configPath)
    
    if (existsSync(fullPath)) {
      try {
        const configModule = await loadModule(fullPath)
        const config = configModule.default || configModule
        
        // Validate and merge with defaults
        return mergeWithDefaults(config)
      } catch (error) {
        console.error(`❌ Failed to load config from ${configPath}:`, error)
        throw error
      }
    }
  }
  
  // Return default configuration if no config file found
  console.log('⚠️ No configuration file found, using defaults')
  return getDefaultConfig()
}

/**
 * Get default configuration
 */
function getDefaultConfig(): KilatConfig {
  return {
    runtime: {
      engine: 'bun',
      port: 3000,
      host: 'localhost',
      https: false,
    },
    apps: {
      dir: './apps',
      extensions: ['.tsx', '.ts', '.jsx', '.js'],
      middleware: true,
      groups: true,
    },
    ui: {
      css: {
        framework: 'tailwind',
        themes: ['glow', 'cyber', 'pastel'],
        defaultTheme: 'glow',
      },
      animations: {
        enabled: true,
        presets: ['scroll', 'orbit', '3d', 'hover'],
      },
    },
    render: {
      mode: 'ssr',
      streaming: true,
      rsc: true,
      suspense: true,
    },
    plugins: [],
    dev: {
      overlay: true,
      hmr: true,
      analytics: true,
    },
    build: {
      outDir: './dist',
      target: 'es2022',
      minify: true,
      sourcemap: true,
    },
    deploy: {
      platform: 'auto',
      edge: true,
    },
  }
}

/**
 * Merge user config with defaults
 */
function mergeWithDefaults(userConfig: Partial<KilatConfig>): KilatConfig {
  const defaultConfig = getDefaultConfig()
  
  return {
    runtime: { ...defaultConfig.runtime, ...userConfig.runtime },
    apps: { ...defaultConfig.apps, ...userConfig.apps },
    ui: {
      css: { ...defaultConfig.ui.css, ...userConfig.ui?.css },
      animations: { ...defaultConfig.ui.animations, ...userConfig.ui?.animations },
    },
    render: { ...defaultConfig.render, ...userConfig.render },
    plugins: userConfig.plugins || defaultConfig.plugins,
    dev: { ...defaultConfig.dev, ...userConfig.dev },
    build: { ...defaultConfig.build, ...userConfig.build },
    deploy: { ...defaultConfig.deploy, ...userConfig.deploy },
  }
}
