/**
 * 🔥 HMR Plugin - Hot Module Replacement
 */

import type { KilatPlugin, KilatContext } from '../../../../types/config'

class HMRServer {
  private clients: Set<WebSocket> = new Set()
  private server: any
  private watchers: Map<string, any> = new Map()
  
  async start(port: number = 3001): Promise<void> {
    if (typeof Bun !== 'undefined') {
      // Bun WebSocket server
      this.server = Bun.serve({
        port,
        fetch: this.handleRequest.bind(this),
        websocket: {
          message: this.handleMessage.bind(this),
          open: this.handleOpen.bind(this),
          close: this.handleClose.bind(this),
        }
      })
    } else {
      // Node.js WebSocket server fallback
      const { WebSocketServer } = await import('ws')
      this.server = new WebSocketServer({ port })
      
      this.server.on('connection', (ws: WebSocket) => {
        this.clients.add(ws)
        ws.on('close', () => this.clients.delete(ws))
      })
    }
    
    console.log(`🔥 HMR server started on port ${port}`)
  }
  
  async stop(): Promise<void> {
    if (this.server) {
      if (typeof this.server.stop === 'function') {
        this.server.stop()
      } else {
        this.server.close()
      }
    }
    
    // Stop all watchers
    for (const watcher of this.watchers.values()) {
      if (watcher.close) {
        watcher.close()
      }
    }
    
    this.watchers.clear()
    this.clients.clear()
  }
  
  private handleRequest(req: Request): Response {
    const url = new URL(req.url)
    
    if (url.pathname === '/hmr') {
      return new Response('HMR WebSocket endpoint', {
        status: 101,
        headers: {
          'Upgrade': 'websocket',
          'Connection': 'Upgrade',
        }
      })
    }
    
    return new Response('Not Found', { status: 404 })
  }
  
  private handleMessage(ws: WebSocket, message: string): void {
    try {
      const data = JSON.parse(message)
      console.log('🔥 HMR message:', data)
    } catch (error) {
      console.error('❌ HMR message error:', error)
    }
  }
  
  private handleOpen(ws: WebSocket): void {
    this.clients.add(ws)
    console.log('🔥 HMR client connected')
  }
  
  private handleClose(ws: WebSocket): void {
    this.clients.delete(ws)
    console.log('🔥 HMR client disconnected')
  }
  
  broadcast(message: any): void {
    const data = JSON.stringify(message)
    
    for (const client of this.clients) {
      try {
        if (client.readyState === 1) { // WebSocket.OPEN
          client.send(data)
        }
      } catch (error) {
        console.error('❌ HMR broadcast error:', error)
        this.clients.delete(client)
      }
    }
  }
  
  async watchFiles(patterns: string[]): Promise<void> {
    try {
      const chokidar = await import('chokidar')
      
      for (const pattern of patterns) {
        const watcher = chokidar.watch(pattern, {
          ignored: /node_modules|\.git|dist|\.kilat/,
          ignoreInitial: true
        })
        
        watcher.on('change', (path: string) => {
          console.log(`🔥 File changed: ${path}`)
          this.broadcast({
            type: 'file-changed',
            path,
            timestamp: Date.now()
          })
        })
        
        watcher.on('add', (path: string) => {
          console.log(`🔥 File added: ${path}`)
          this.broadcast({
            type: 'file-added',
            path,
            timestamp: Date.now()
          })
        })
        
        watcher.on('unlink', (path: string) => {
          console.log(`🔥 File removed: ${path}`)
          this.broadcast({
            type: 'file-removed',
            path,
            timestamp: Date.now()
          })
        })
        
        this.watchers.set(pattern, watcher)
      }
    } catch (error) {
      console.warn('⚠️ File watching not available:', error)
    }
  }
}

let hmrServer: HMRServer

const hmrPlugin: KilatPlugin = {
  name: 'hmr',
  version: '1.0.0',
  description: 'Hot Module Replacement for development',
  author: 'Kilat.js Team',
  builtin: true,
  
  config: {
    enabled: true,
    port: 3001,
    watchPatterns: [
      'apps/**/*',
      'components/**/*',
      'core/**/*',
      'public/**/*'
    ]
  },
  
  hooks: {
    'plugin:init': async (context: KilatContext) => {
      if (context.mode === 'development') {
        hmrServer = new HMRServer()
        console.log('🔥 HMR plugin initialized')
      }
    },
    
    'dev:start': async (context: KilatContext) => {
      if (context.mode === 'development' && hmrServer) {
        const config = context.config.plugins.find(p => p.name === 'hmr')?.config || {}
        
        await hmrServer.start(config.port || 3001)
        
        if (config.watchPatterns) {
          await hmrServer.watchFiles(config.watchPatterns)
        }
      }
    },
    
    'dev:reload': async (context: KilatContext) => {
      if (hmrServer) {
        hmrServer.broadcast({
          type: 'reload',
          timestamp: Date.now()
        })
      }
    },
    
    'server:stop': async (context: KilatContext) => {
      if (hmrServer) {
        await hmrServer.stop()
      }
    }
  }
}

/**
 * Get HMR server instance
 */
export function getHMRServer(): HMRServer | undefined {
  return hmrServer
}

/**
 * Trigger HMR reload
 */
export function triggerReload(): void {
  if (hmrServer) {
    hmrServer.broadcast({
      type: 'reload',
      timestamp: Date.now()
    })
  }
}

export default hmrPlugin
