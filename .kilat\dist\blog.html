<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Blog | Kilat.js Framework</title>
  <meta name="description" content="Tutorials, guides, and insights about building lightning-fast web applications with Kilat.js framework and SpeedRun™ Runtime." />
  <meta name="keywords" content="kilat.js, blog, tutorials, web development, performance" />
  <meta property="og:title" content="Kilat.js Blog - Tutorials & Guides" />
  <meta property="og:description" content="Learn how to build lightning-fast web applications with Kilat.js framework." />
  <meta property="og:image" content="/api/og?title=Kilat.js Blog" />
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><div class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-x-hidden"><div class="fixed inset-0 pointer-events-none z-0"><div class="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl kilat-hero-float"></div><div class="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl kilat-hero-pulse"></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"></div></div><header class="relative z-50 kilat-glass border-b border-white/10"><div class="kilat-container"><div class="flex items-center justify-between h-16"><a href="/" class="flex items-center space-x-2 kilat-glow-hover transition-all duration-300"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></a><nav class="hidden md:flex items-center space-x-8"><a href="/" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🛠️ API</a></nav><div class="flex items-center space-x-4"><div class="flex items-center space-x-3"><a href="/login" class="kilat-btn kilat-btn-outline px-4 py-2 text-sm">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary px-4 py-2 text-sm">✨ Register</a></div><button class="md:hidden kilat-btn kilat-btn-outline px-3 py-2"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></div><div class="md:hidden border-t border-white/10 py-4 hidden" id="mobile-menu"><div class="flex flex-col space-y-2"><a href="/" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🛠️ API</a><div class="flex flex-col space-y-2 pt-4 border-t border-white/10"><a href="/login" class="kilat-btn kilat-btn-outline text-center">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary text-center">✨ Register</a></div></div></div></div></header><main class="relative z-10"><section class="py-20"><div class="kilat-container"><div class="text-center mb-16 kilat-fade-in"><h1 class="text-4xl md:text-5xl font-bold text-white mb-6">Kilat.js <span class="kilat-gradient-text">Blog</span></h1><p class="text-xl text-gray-300 max-w-3xl mx-auto">Tutorials, guides, and insights about building lightning-fast web applications with Kilat.js framework and SpeedRun™ Runtime.</p></div><div class="mb-16 kilat-fade-in" style="animation-delay:0.2s"><div class="kilat-card kilat-glass p-8 kilat-glow-hover"><div class="flex items-center space-x-2 mb-4"><span class="px-3 py-1 bg-blue-500/20 text-blue-400 text-sm rounded-full">Featured</span><span class="text-gray-400 text-sm">5 min read</span></div><h2 class="text-3xl font-bold text-white mb-4"><a href="/blog/getting-started" class="hover:kilat-gradient-text transition-all duration-300">Getting Started with Kilat.js</a></h2><p class="text-gray-300 text-lg mb-6 leading-relaxed">Learn how to build lightning-fast applications with Kilat.js framework and SpeedRun™ Runtime.</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4 text-gray-400"><span>By <!-- -->Kilat Team</span><span>•</span><span>1/25/2024</span></div><a href="/blog/getting-started" class="kilat-btn kilat-btn-primary px-6 py-3 kilat-glow-hover">Read More →</a></div></div></div></div></section><section class="pb-20"><div class="kilat-container"><h2 class="text-2xl font-bold text-white mb-8 kilat-fade-in">Latest Posts</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><article class="kilat-card kilat-glass p-6 kilat-glow-hover kilat-fade-in" style="animation-delay:0.4s"><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">routing</span><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">advanced</span></div><h3 class="text-xl font-bold text-white mb-3"><a href="/blog/advanced-routing" class="hover:kilat-gradient-text transition-all duration-300">Advanced Routing in Kilat.js</a></h3><p class="text-gray-300 mb-4 leading-relaxed">Explore the powerful routing system with SSG, SSR, and CSR support for modern web applications.</p><div class="flex items-center justify-between text-sm text-gray-400"><div class="flex items-center space-x-2"><span>Kilat Team</span><span>•</span><span>1/20/2024</span></div><span>8 min read</span></div><div class="mt-4 pt-4 border-t border-white/10"><a href="/blog/advanced-routing" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">Read more →</a></div></article><article class="kilat-card kilat-glass p-6 kilat-glow-hover kilat-fade-in" style="animation-delay:0.5s"><div class="flex flex-wrap gap-2 mb-4"><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">performance</span><span class="px-2 py-1 bg-white/10 text-gray-300 text-xs rounded">optimization</span></div><h3 class="text-xl font-bold text-white mb-3"><a href="/blog/performance-optimization" class="hover:kilat-gradient-text transition-all duration-300">Performance Optimization with SpeedRun™</a></h3><p class="text-gray-300 mb-4 leading-relaxed">Discover how SpeedRun™ Runtime delivers unmatched performance and developer experience.</p><div class="flex items-center justify-between text-sm text-gray-400"><div class="flex items-center space-x-2"><span>Kilat Team</span><span>•</span><span>1/15/2024</span></div><span>6 min read</span></div><div class="mt-4 pt-4 border-t border-white/10"><a href="/blog/performance-optimization" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">Read more →</a></div></article></div></div></section><section class="py-16"><div class="kilat-container"><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in"><h2 class="text-3xl font-bold text-white mb-4">Stay Updated</h2><p class="text-gray-300 mb-8 max-w-2xl mx-auto">Get the latest tutorials, guides, and updates about Kilat.js framework delivered straight to your inbox.</p><form class="max-w-md mx-auto flex gap-4"><input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"/><button type="submit" class="kilat-btn kilat-btn-primary px-6 py-3 kilat-glow-hover">Subscribe</button></form></div></div></section></main><footer class="relative z-10 mt-20 border-t border-white/10 kilat-glass"><div class="kilat-container py-12"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="col-span-1 md:col-span-2"><div class="flex items-center space-x-2 mb-4"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></div><p class="text-gray-400 max-w-md mb-6">Modern fullstack framework with SpeedRun™ Runtime. Built for speed, developer experience, and production-ready applications.</p><div class="flex items-center space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">📧</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐙</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐦</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">💬</span></a></div></div><div><h3 class="text-white font-semibold mb-4">Quick Links</h3><ul class="space-y-2"><li><a href="/" class="text-gray-400 hover:text-white transition-colors">🏠 Home</a></li><li><a href="/about" class="text-gray-400 hover:text-white transition-colors">📄 About</a></li><li><a href="/dashboard" class="text-gray-400 hover:text-white transition-colors">📊 Dashboard</a></li><li><a href="/api/users" class="text-gray-400 hover:text-white transition-colors">🛠️ API</a></li><li><a href="/login" class="text-gray-400 hover:text-white transition-colors">🔑 Login</a></li></ul></div><div><h3 class="text-white font-semibold mb-4">Resources</h3><ul class="space-y-2"><li><a href="#" class="text-gray-400 hover:text-white transition-colors">📚 Documentation</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🚀 Getting Started</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">💡 Examples</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🎓 Tutorials</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">👥 Community</a></li></ul></div></div><div class="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row items-center justify-between"><p class="text-gray-400 text-sm">© 2024 Kilat.js. Built with ❤️ using SpeedRun™ Runtime.</p><div class="flex items-center space-x-6 mt-4 md:mt-0"><span class="text-gray-400 text-sm">Powered by</span><div class="flex items-center space-x-4"><div class="flex items-center space-x-2"><span class="text-yellow-400">🟡</span><span class="text-gray-300 text-sm font-medium">Bun.js</span></div><div class="flex items-center space-x-2"><span class="text-blue-400">⚛️</span><span class="text-gray-300 text-sm font-medium">React 18</span></div><div class="flex items-center space-x-2"><span class="text-purple-400">⚡</span><span class="text-gray-300 text-sm font-medium">TypeScript</span></div></div></div></div><div class="mt-8 text-center"><div class="inline-flex items-center px-4 py-2 kilat-glass rounded-full text-sm text-gray-300"><span class="mr-2">⚡</span>SpeedRun™ Runtime - Lightning Fast Performance<span class="ml-2 text-green-400">●</span></div></div></div></footer></div></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>