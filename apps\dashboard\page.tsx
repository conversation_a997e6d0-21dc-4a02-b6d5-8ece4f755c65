/**
 * 📊 Dashboard Overview Page - Enhanced with KilatCSS & KilatAnim
 */

import React from 'react'

export default function DashboardPage() {
  return (
    <>
      {/* Dashboard Section */}
      <section className="py-8">
        <div className="kilat-container">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-12 kilat-fade-in">
          <div className="flex-1 min-w-0">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
              Dashboard{' '}
              <span className="kilat-gradient-text">Overview</span>
            </h1>
            <p className="text-xl text-gray-300">
              Monitor your Kilat.js application performance and analytics
            </p>
          </div>
          <div className="mt-6 flex gap-4 md:mt-0 md:ml-4">
            <button
              type="button"
              className="kilat-btn kilat-btn-outline px-6 py-3 font-semibold kilat-glow-hover transition-all duration-300"
            >
              <span className="mr-2">📊</span>
              Export
            </button>
            <button
              type="button"
              className="kilat-btn kilat-btn-primary px-6 py-3 font-semibold kilat-glow-hover transition-all duration-300"
            >
              <span className="mr-2">✨</span>
              Create
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-12">
          <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.1s' }}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">👥</span>
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-400">Total Users</p>
                <p className="text-2xl font-bold text-white">1,234</p>
                <p className="text-xs text-green-400">+12% from last month</p>
              </div>
            </div>
          </div>

          <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.2s' }}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">📊</span>
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-400">Page Views</p>
                <p className="text-2xl font-bold text-white">12,345</p>
                <p className="text-xs text-green-400">+8% from last week</p>
              </div>
            </div>
          </div>

          <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.3s' }}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">⚡</span>
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-400">Performance</p>
                <p className="text-2xl font-bold text-white">98.5%</p>
                <p className="text-xs text-green-400">Excellent</p>
              </div>
            </div>
          </div>

          <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover" style={{ animationDelay: '0.4s' }}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">🚀</span>
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-400">Uptime</p>
                <p className="text-2xl font-bold text-white">99.9%</p>
                <p className="text-xs text-green-400">All systems operational</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="kilat-fade-in" style={{ animationDelay: '0.5s' }}>
          <div className="kilat-card kilat-glass p-8">
            <div className="mb-6">
              <h3 className="text-2xl font-bold text-white mb-2">Recent Activity</h3>
              <p className="text-gray-400">
                Latest events and updates from your Kilat.js application.
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-green-400">✅</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-white">
                      New user registration
                    </p>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-500/20 text-green-400">
                      Success
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    User <EMAIL> registered successfully
                  </p>
                  <p className="text-xs text-gray-500 mt-2">2 minutes ago</p>
                </div>
              </div>

              <div className="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-blue-400">🚀</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-white">
                      System update deployed
                    </p>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-500/20 text-blue-400">
                      Info
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    Kilat.js v1.0.0 deployed to production
                  </p>
                  <p className="text-xs text-gray-500 mt-2">1 hour ago</p>
                </div>
              </div>

              <div className="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-purple-400">⚡</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-white">
                      Performance optimization
                    </p>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-500/20 text-purple-400">
                      Enhancement
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    SpeedRun™ Runtime optimizations applied
                  </p>
                  <p className="text-xs text-gray-500 mt-2">3 hours ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-12 kilat-fade-in" style={{ animationDelay: '0.6s' }}>
          <h3 className="text-2xl font-bold text-white mb-6">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a
              href="/about"
              className="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              <div className="text-3xl mb-4">📄</div>
              <h4 className="text-lg font-bold text-white mb-2">Learn More</h4>
              <p className="text-gray-400">Discover Kilat.js features</p>
            </a>

            <a
              href="/"
              className="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              <div className="text-3xl mb-4">🏠</div>
              <h4 className="text-lg font-bold text-white mb-2">Homepage</h4>
              <p className="text-gray-400">Back to main page</p>
            </a>

            <a
              href="/api/users"
              className="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              <div className="text-3xl mb-4">🛠️</div>
              <h4 className="text-lg font-bold text-white mb-2">API Test</h4>
              <p className="text-gray-400">Test API endpoints</p>
            </a>
          </div>
        </div>
        </div>
      </section>
    </>
  )
}
