/**
 * 📊 Dashboard Overview Page - Refined UI with KilatCSS
 */

import React from 'react'

export default function DashboardPage() {
  // Check authentication (in real app, this would come from context/props)
  const isAuthenticated = true // This should be dynamic
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "JD"
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <section className="py-20 sm:py-32">
        <div className="kilat-container text-center">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">🔒</span>
            </div>
            <h1 className="text-3xl font-bold text-white mb-4">Access Denied</h1>
            <p className="text-gray-400 mb-8">You need to be logged in to access the dashboard.</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/login" className="kilat-btn kilat-btn-primary px-6 py-3">
                🔑 Login
              </a>
              <a href="/register" className="kilat-btn kilat-btn-outline px-6 py-3">
                ✨ Register
              </a>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <>
      {/* Dashboard Header */}
      <section className="py-8 border-b border-white/10">
        <div className="kilat-container">
          {/* Welcome Header */}
          <div className="flex items-center justify-between kilat-fade-in">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
                Welcome back, <span className="kilat-gradient-text">{user.name}</span>! 👋
              </h1>
              <p className="text-gray-400">
                Here's what's happening with your Kilat.js applications today.
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="kilat-glass px-4 py-2 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-300">All systems operational</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions Bar */}
      <section className="py-6 border-b border-white/10">
        <div className="kilat-container">
          <div className="flex flex-wrap items-center gap-4 kilat-fade-in" style={{ animationDelay: '0.1s' }}>
            <button className="kilat-btn kilat-btn-primary px-4 py-2 text-sm">
              <span className="mr-2">➕</span>
              New Project
            </button>
            <button className="kilat-btn kilat-btn-outline px-4 py-2 text-sm">
              <span className="mr-2">📊</span>
              Analytics
            </button>
            <button className="kilat-btn kilat-btn-outline px-4 py-2 text-sm">
              <span className="mr-2">⚙️</span>
              Settings
            </button>
            <button className="kilat-btn kilat-btn-outline px-4 py-2 text-sm">
              <span className="mr-2">📤</span>
              Export Data
            </button>
            <div className="ml-auto hidden md:block">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <span>Last updated:</span>
                <span className="text-white">2 minutes ago</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Dashboard Content */}
      <section className="py-8">
        <div className="kilat-container">

          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-12">
            {/* Total Users */}
            <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style={{ animationDelay: '0.1s' }}>
              <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -mr-10 -mt-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                    <span className="text-2xl">👥</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-green-400 font-medium">+12.5%</div>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-400 mb-1">Total Users</p>
                  <p className="text-3xl font-bold text-white">1,234</p>
                  <p className="text-xs text-gray-500 mt-1">vs last month</p>
                </div>
              </div>
            </div>

            {/* Revenue */}
            <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style={{ animationDelay: '0.2s' }}>
              <div className="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -mr-10 -mt-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                    <span className="text-2xl">💰</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-green-400 font-medium">+23.1%</div>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-400 mb-1">Revenue</p>
                  <p className="text-3xl font-bold text-white">$12,345</p>
                  <p className="text-xs text-gray-500 mt-1">vs last month</p>
                </div>
              </div>
            </div>

            {/* Performance Score */}
            <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style={{ animationDelay: '0.3s' }}>
              <div className="absolute top-0 right-0 w-20 h-20 bg-yellow-500/10 rounded-full -mr-10 -mt-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-yellow-500/20 rounded-xl flex items-center justify-center">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-green-400 font-medium">+2.1%</div>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-400 mb-1">Performance</p>
                  <p className="text-3xl font-bold text-white">98.5%</p>
                  <p className="text-xs text-gray-500 mt-1">Lighthouse score</p>
                </div>
              </div>
            </div>

            {/* Active Projects */}
            <div className="kilat-card kilat-glass p-6 kilat-fade-in kilat-glow-hover relative overflow-hidden" style={{ animationDelay: '0.4s' }}>
              <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -mr-10 -mt-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-blue-400 font-medium">+3 new</div>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-400 mb-1">Active Projects</p>
                  <p className="text-3xl font-bold text-white">24</p>
                  <p className="text-xs text-gray-500 mt-1">across all teams</p>
                </div>
              </div>
            </div>
          </div>

        {/* Recent Activity */}
        <div className="kilat-fade-in" style={{ animationDelay: '0.5s' }}>
          <div className="kilat-card kilat-glass p-8">
            <div className="mb-6">
              <h3 className="text-2xl font-bold text-white mb-2">Recent Activity</h3>
              <p className="text-gray-400">
                Latest events and updates from your Kilat.js application.
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-green-400">✅</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-white">
                      New user registration
                    </p>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-500/20 text-green-400">
                      Success
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    User <EMAIL> registered successfully
                  </p>
                  <p className="text-xs text-gray-500 mt-2">2 minutes ago</p>
                </div>
              </div>

              <div className="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-blue-400">🚀</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-white">
                      System update deployed
                    </p>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-500/20 text-blue-400">
                      Info
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    Kilat.js v1.0.0 deployed to production
                  </p>
                  <p className="text-xs text-gray-500 mt-2">1 hour ago</p>
                </div>
              </div>

              <div className="flex items-start space-x-4 p-4 bg-white/5 rounded-lg kilat-glow-hover transition-all duration-300">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-purple-400">⚡</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-white">
                      Performance optimization
                    </p>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-500/20 text-purple-400">
                      Enhancement
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    SpeedRun™ Runtime optimizations applied
                  </p>
                  <p className="text-xs text-gray-500 mt-2">3 hours ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-12 kilat-fade-in" style={{ animationDelay: '0.6s' }}>
          <h3 className="text-2xl font-bold text-white mb-6">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a
              href="/about"
              className="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              <div className="text-3xl mb-4">📄</div>
              <h4 className="text-lg font-bold text-white mb-2">Learn More</h4>
              <p className="text-gray-400">Discover Kilat.js features</p>
            </a>

            <a
              href="/"
              className="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              <div className="text-3xl mb-4">🏠</div>
              <h4 className="text-lg font-bold text-white mb-2">Homepage</h4>
              <p className="text-gray-400">Back to main page</p>
            </a>

            <a
              href="/api/users"
              className="kilat-card kilat-glass p-6 text-center kilat-glow-hover transform hover:scale-105 transition-all duration-300"
            >
              <div className="text-3xl mb-4">🛠️</div>
              <h4 className="text-lg font-bold text-white mb-2">API Test</h4>
              <p className="text-gray-400">Test API endpoints</p>
            </a>
          </div>
        </div>
        </div>
      </section>
    </>
  )
}
