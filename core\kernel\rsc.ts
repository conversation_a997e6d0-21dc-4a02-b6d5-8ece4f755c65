/**
 * ⚛️ React Server Components - RSC Support
 */

import type { KilatContext } from '../../types/config'
import { loadComponent } from './esm-loader'

export interface RSCPayload {
  type: 'component' | 'element' | 'text'
  props?: Record<string, any>
  children?: RSCPayload[]
  key?: string
}

/**
 * Check if component is a server component
 */
export function isServerComponent(componentPath: string): boolean {
  return componentPath.endsWith('.server.tsx') || 
         componentPath.endsWith('.server.ts') ||
         componentPath.endsWith('.server.jsx') ||
         componentPath.endsWith('.server.js')
}

/**
 * Render server component
 */
export async function renderServerComponent(
  componentPath: string,
  props: Record<string, any> = {},
  context: KilatContext
): Promise<RSCPayload> {
  try {
    // Load server component
    const Component = await loadComponent(componentPath)
    
    // Execute server component
    const element = await Component(props)
    
    // Serialize to RSC payload
    return serializeElement(element)
    
  } catch (error) {
    console.error(`❌ Failed to render server component ${componentPath}:`, error)
    
    // Return error component
    return {
      type: 'element',
      props: {
        className: 'kilat-error-boundary',
        children: [
          {
            type: 'text',
            props: { value: `Error loading server component: ${componentPath}` }
          }
        ]
      }
    }
  }
}

/**
 * Serialize React element to RSC payload
 */
function serializeElement(element: any): RSCPayload {
  if (typeof element === 'string' || typeof element === 'number') {
    return {
      type: 'text',
      props: { value: String(element) }
    }
  }
  
  if (React.isValidElement(element)) {
    const { type, props, key } = element
    
    // Handle component
    if (typeof type === 'function') {
      return {
        type: 'component',
        props: serializeProps(props),
        key: key || undefined
      }
    }
    
    // Handle element
    if (typeof type === 'string') {
      return {
        type: 'element',
        props: {
          ...serializeProps(props),
          tagName: type
        },
        key: key || undefined
      }
    }
  }
  
  // Handle arrays
  if (Array.isArray(element)) {
    return {
      type: 'element',
      children: element.map(serializeElement)
    }
  }
  
  // Fallback
  return {
    type: 'text',
    props: { value: '' }
  }
}

/**
 * Serialize props for RSC
 */
function serializeProps(props: any): Record<string, any> {
  if (!props) return {}
  
  const serialized: Record<string, any> = {}
  
  for (const [key, value] of Object.entries(props)) {
    if (key === 'children') {
      if (React.isValidElement(value)) {
        serialized[key] = serializeElement(value)
      } else if (Array.isArray(value)) {
        serialized[key] = value.map(serializeElement)
      } else {
        serialized[key] = value
      }
    } else if (typeof value === 'function') {
      // Skip functions (they can't be serialized)
      continue
    } else if (typeof value === 'object' && value !== null) {
      // Recursively serialize objects
      serialized[key] = serializeProps(value)
    } else {
      serialized[key] = value
    }
  }
  
  return serialized
}

/**
 * Create RSC stream
 */
export function createRSCStream(payload: RSCPayload): ReadableStream {
  const encoder = new TextEncoder()
  
  return new ReadableStream({
    start(controller) {
      // Send RSC payload as JSON
      const data = JSON.stringify(payload)
      controller.enqueue(encoder.encode(data))
      controller.close()
    }
  })
}

/**
 * Parse RSC payload on client
 */
export function parseRSCPayload(data: string): RSCPayload {
  try {
    return JSON.parse(data)
  } catch (error) {
    console.error('❌ Failed to parse RSC payload:', error)
    return {
      type: 'text',
      props: { value: 'Error parsing server component' }
    }
  }
}

/**
 * Render RSC payload to React element
 */
export function renderRSCPayload(payload: RSCPayload): React.ReactElement {
  switch (payload.type) {
    case 'text':
      return React.createElement('span', {}, payload.props?.value || '')
    
    case 'element':
      const { tagName = 'div', children, ...props } = payload.props || {}
      const childElements = payload.children?.map(renderRSCPayload) || []
      
      return React.createElement(
        tagName,
        props,
        ...childElements
      )
    
    case 'component':
      // This would require component registry on client
      return React.createElement('div', {}, 'Server Component')
    
    default:
      return React.createElement('div', {}, 'Unknown RSC type')
  }
}
