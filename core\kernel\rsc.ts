/**
 * ⚛️ React Server Components - Enhanced RSC Support
 */

import React from 'react'
import type { KilatContext } from '../../types/config'
import { loadComponent } from './esm-loader'

export interface RSCPayload {
  type: 'component' | 'element' | 'text' | 'suspense' | 'error'
  props?: Record<string, any>
  children?: RSCPayload[]
  key?: string
  id?: string
  fallback?: RSCPayload
  error?: string
}

export interface RSCManifest {
  components: Record<string, string>
  chunks: Record<string, string[]>
  dependencies: Record<string, string[]>
}

export interface RSCStreamChunk {
  id: string
  type: 'component' | 'data' | 'error' | 'complete'
  payload: any
}

/**
 * Check if component is a server component
 */
export function isServerComponent(componentPath: string): boolean {
  return componentPath.endsWith('.server.tsx') || 
         componentPath.endsWith('.server.ts') ||
         componentPath.endsWith('.server.jsx') ||
         componentPath.endsWith('.server.js')
}

/**
 * Render server component
 */
export async function renderServerComponent(
  componentPath: string,
  props: Record<string, any> = {},
  context: KilatContext
): Promise<RSCPayload> {
  try {
    // Load server component
    const Component = await loadComponent(componentPath)

    // Execute server component
    const element = React.createElement(Component, props)

    // Serialize to RSC payload
    return serializeElement(element)
    
  } catch (error) {
    console.error(`❌ Failed to render server component ${componentPath}:`, error)
    
    // Return error component
    return {
      type: 'element',
      props: {
        className: 'kilat-error-boundary',
        children: [
          {
            type: 'text',
            props: { value: `Error loading server component: ${componentPath}` }
          }
        ]
      }
    }
  }
}

/**
 * Serialize React element to RSC payload
 */
function serializeElement(element: any): RSCPayload {
  if (typeof element === 'string' || typeof element === 'number') {
    return {
      type: 'text',
      props: { value: String(element) }
    }
  }
  
  if (React.isValidElement(element)) {
    const { type, props, key } = element
    
    // Handle component
    if (typeof type === 'function') {
      return {
        type: 'component',
        props: serializeProps(props),
        key: key || undefined
      }
    }
    
    // Handle element
    if (typeof type === 'string') {
      return {
        type: 'element',
        props: {
          ...serializeProps(props),
          tagName: type
        },
        key: key || undefined
      }
    }
  }
  
  // Handle arrays
  if (Array.isArray(element)) {
    return {
      type: 'element',
      children: element.map(serializeElement)
    }
  }
  
  // Fallback
  return {
    type: 'text',
    props: { value: '' }
  }
}

/**
 * Serialize props for RSC
 */
function serializeProps(props: any): Record<string, any> {
  if (!props) return {}
  
  const serialized: Record<string, any> = {}
  
  for (const [key, value] of Object.entries(props)) {
    if (key === 'children') {
      if (React.isValidElement(value)) {
        serialized[key] = serializeElement(value)
      } else if (Array.isArray(value)) {
        serialized[key] = value.map(serializeElement)
      } else {
        serialized[key] = value
      }
    } else if (typeof value === 'function') {
      // Skip functions (they can't be serialized)
      continue
    } else if (typeof value === 'object' && value !== null) {
      // Recursively serialize objects
      serialized[key] = serializeProps(value)
    } else {
      serialized[key] = value
    }
  }
  
  return serialized
}

/**
 * Create RSC stream with progressive loading
 */
export function createRSCStream(payload: RSCPayload, options: { progressive?: boolean } = {}): ReadableStream {
  const encoder = new TextEncoder()

  if (!options.progressive) {
    // Simple non-progressive stream
    return new ReadableStream({
      start(controller) {
        // Send RSC payload as JSON
        const data = JSON.stringify(payload)
        controller.enqueue(encoder.encode(data))
        controller.close()
      }
    })
  }

  // Progressive streaming with chunked data
  return new ReadableStream({
    start(controller) {
      // Send initial shell
      const initialChunk: RSCStreamChunk = {
        id: 'shell',
        type: 'component',
        payload: createShellPayload(payload)
      }

      controller.enqueue(encoder.encode(JSON.stringify(initialChunk) + '\n'))

      // Process and stream components progressively
      streamComponents(payload, controller, encoder)
        .then(() => {
          // Send completion marker
          const completeChunk: RSCStreamChunk = {
            id: 'complete',
            type: 'complete',
            payload: null
          }

          controller.enqueue(encoder.encode(JSON.stringify(completeChunk) + '\n'))
          controller.close()
        })
        .catch(error => {
          // Send error
          const errorChunk: RSCStreamChunk = {
            id: 'error',
            type: 'error',
            payload: { message: error.message, stack: error.stack }
          }

          controller.enqueue(encoder.encode(JSON.stringify(errorChunk) + '\n'))
          controller.close()
        })
    }
  })
}

/**
 * Create shell payload (initial structure without data)
 */
function createShellPayload(payload: RSCPayload): RSCPayload {
  // Replace data-heavy components with placeholders
  const processNode = (node: RSCPayload): RSCPayload => {
    // For suspense, keep structure but use fallback
    if (node.type === 'suspense' && node.fallback) {
      return {
        ...node,
        children: undefined, // Remove children to be streamed later
        id: node.id || generateComponentId()
      }
    }

    // For server components, replace with placeholder
    if (node.type === 'component' && node.props?.['data-server-component']) {
      return {
        type: 'element',
        props: {
          tagName: 'div',
          'data-component-id': node.id || generateComponentId(),
          'data-component-placeholder': true
        }
      }
    }

    // Process children recursively
    if (node.children) {
      return {
        ...node,
        children: node.children.map(processNode)
      }
    }

    return node
  }

  return processNode(payload)
}

/**
 * Stream components progressively
 */
async function streamComponents(
  payload: RSCPayload,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
): Promise<void> {
  const componentQueue: RSCPayload[] = []

  // Find all suspense and server components
  const findComponents = (node: RSCPayload) => {
    if (node.type === 'suspense' ||
        (node.type === 'component' && node.props?.['data-server-component'])) {
      componentQueue.push(node)
    }

    if (node.children) {
      node.children.forEach(findComponents)
    }
  }

  findComponents(payload)

  // Process components with small delays to allow progressive rendering
  for (const component of componentQueue) {
    // Add small delay to simulate async loading
    await new Promise(resolve => setTimeout(resolve, 50))

    const componentChunk: RSCStreamChunk = {
      id: component.id || generateComponentId(),
      type: 'component',
      payload: component
    }

    controller.enqueue(encoder.encode(JSON.stringify(componentChunk) + '\n'))
  }
}

/**
 * Generate unique component ID
 */
function generateComponentId(): string {
  return `rsc-${Math.random().toString(36).substring(2, 9)}`
}

/**
 * Parse RSC payload on client
 */
export function parseRSCPayload(data: string): RSCPayload {
  try {
    return JSON.parse(data)
  } catch (error) {
    console.error('❌ Failed to parse RSC payload:', error)
    return {
      type: 'text',
      props: { value: 'Error parsing server component' }
    }
  }
}

/**
 * Render RSC payload to React element
 */
export function renderRSCPayload(payload: RSCPayload): React.ReactElement {
  switch (payload.type) {
    case 'text':
      return React.createElement('span', {}, payload.props?.value || '')
    
    case 'element':
      const { tagName = 'div', children, ...props } = payload.props || {}
      const childElements = payload.children?.map(renderRSCPayload) || []
      
      return React.createElement(
        tagName,
        props,
        ...childElements
      )
    
    case 'component':
      // This would require component registry on client
      return React.createElement('div', {}, 'Server Component')
    
    default:
      return React.createElement('div', {}, 'Unknown RSC type')
  }
}
