
/**
 * 🖥️ Kilat.js Production Server
 */
const { serve } = require('bun');
const { renderer } = require('../renderer');
const { router } = require('../router');

const config = require('./config.json');

async function handleRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;

  try {
    // Serve static files
    if (pathname.startsWith('/_kilat/')) {
      return serveStatic(pathname);
    }

    // Handle API routes
    if (pathname.startsWith('/api/')) {
      return handleApiRoute(pathname, request);
    }

    // Render page
    const context = {
      request,
      pathname,
      searchParams: url.searchParams
    };

    const result = await renderer.render(pathname, context);
    
    return new Response(result.html, {
      status: result.statusCode || 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        ...result.headers
      }
    });

  } catch (error) {
    console.error('Server error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

function serveStatic(pathname) {
  // Serve static files from dist directory
  const fs = require('fs');
  const path = require('path');
  
  const filePath = path.join(__dirname, '../dist', pathname);
  
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath);
    const ext = path.extname(filePath);
    const contentType = getContentType(ext);
    
    return new Response(content, {
      headers: { 'Content-Type': contentType }
    });
  }
  
  return new Response('Not Found', { status: 404 });
}

function getContentType(ext) {
  const types = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.svg': 'image/svg+xml'
  };
  return types[ext] || 'text/plain';
}

async function handleApiRoute(pathname, request) {
  const routeMatch = router.matchRoute(pathname);
  if (!routeMatch || routeMatch.route.type !== 'api') {
    return new Response('API route not found', { status: 404 });
  }

  try {
    const module = require(routeMatch.route.component);
    const method = request.method.toUpperCase();
    const handler = module[method] || module.default;
    
    if (!handler) {
      return new Response('Method not allowed', { status: 405 });
    }

    return await handler(request);
  } catch (error) {
    console.error('API error:', error);
    return new Response('API Error', { status: 500 });
  }
}

// Start server
const server = serve({
  port: config.port,
  hostname: config.host,
  fetch: handleRequest
});

console.log(`🚀 Kilat.js server running on http://${config.host}:${config.port}`);
