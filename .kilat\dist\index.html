<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kilat.js App</title>
  <meta name="description" content="Built with Kilat.js SpeedRun™ Runtime" />
  
  
  
  <!-- Kilat.js Styles -->
  <link rel="stylesheet" href="/_kilat/styles.css" />
  
  <!-- Critical CSS -->
  <style>
    body { margin: 0; font-family: 'Inter', system-ui, sans-serif; background: #0f172a; color: #f8fafc; }
    .kilat-loading { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
  </style>
</head>
<body>
  <div id="__kilat"><div class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-x-hidden"><div class="fixed inset-0 pointer-events-none z-0"><div class="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl kilat-hero-float"></div><div class="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl kilat-hero-pulse"></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"></div></div><header class="relative z-50 kilat-glass border-b border-white/10"><div class="kilat-container"><div class="flex items-center justify-between h-16"><a href="/" class="flex items-center space-x-2 kilat-glow-hover transition-all duration-300"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></a><nav class="hidden md:flex items-center space-x-8"><a href="/" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors duration-300 kilat-glow-hover px-3 py-2 rounded-lg">🛠️ API</a></nav><div class="flex items-center space-x-4"><div class="flex items-center space-x-3"><a href="/login" class="kilat-btn kilat-btn-outline px-4 py-2 text-sm">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary px-4 py-2 text-sm">✨ Register</a></div><button class="md:hidden kilat-btn kilat-btn-outline px-3 py-2"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></div><div class="md:hidden border-t border-white/10 py-4 hidden" id="mobile-menu"><div class="flex flex-col space-y-2"><a href="/" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🏠 Home</a><a href="/about" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">📄 About</a><a href="/api/users" class="text-gray-300 hover:text-white transition-colors px-3 py-2 rounded-lg">🛠️ API</a><div class="flex flex-col space-y-2 pt-4 border-t border-white/10"><a href="/login" class="kilat-btn kilat-btn-outline text-center">🔑 Login</a><a href="/register" class="kilat-btn kilat-btn-primary text-center">✨ Register</a></div></div></div></div></header><main class="relative z-10"><section class="relative py-20 sm:py-32"><div class="kilat-container"><div class="mx-auto max-w-4xl text-center"><div class="kilat-fade-in mb-8"><div class="inline-flex items-center px-4 py-2 rounded-full text-sm kilat-glass text-gray-300 hover:bg-white/20 transition-all duration-300 kilat-glow-hover"><span class="mr-2">⚡</span>Powered by SpeedRun™ Runtime<a href="/about" class="ml-2 text-blue-400 hover:text-blue-300">Learn more →</a></div></div><div class="kilat-fade-in" style="animation-delay:0.2s"><h1 class="text-5xl md:text-7xl font-bold tracking-tight text-white mb-6">Build<!-- --> <span class="kilat-gradient-text kilat-text-glow">Lightning-Fast</span><br/>Apps with Kilat.js</h1><p class="text-xl md:text-2xl leading-relaxed text-gray-300 max-w-3xl mx-auto mb-10">Modern fullstack framework with SpeedRun™ Runtime, file-based routing, streaming SSR, and built-in optimizations. Zero configuration, maximum performance.</p><div class="flex flex-col sm:flex-row items-center justify-center gap-4 kilat-fade-in" style="animation-delay:0.4s"><a href="/dashboard" class="kilat-btn kilat-btn-primary px-8 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300"><span class="mr-2">🚀</span>Get Started</a><a href="/about" class="kilat-btn kilat-btn-outline px-8 py-4 text-lg font-semibold hover:kilat-glow transition-all duration-300">Learn More<span class="ml-2">→</span></a></div></div></div></div></section><div class="py-24 sm:py-32 relative"><div class="kilat-container"><div class="text-center mb-20 kilat-fade-in"><h2 class="text-4xl md:text-5xl font-bold tracking-tight text-white mb-6">Everything you need to build<!-- --> <span class="kilat-gradient-text">modern apps</span></h2><p class="text-xl leading-8 text-gray-300 max-w-2xl mx-auto">Kilat.js provides all the tools and optimizations you need out of the box.</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto"><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.1s"><div class="text-4xl mb-4">⚡</div><h3 class="text-xl font-bold text-white mb-4">SpeedRun™ Runtime</h3><p class="text-gray-300 leading-relaxed">Lightning-fast runtime powered by Bun.js with automatic optimizations and zero cold starts.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.2s"><div class="text-4xl mb-4">🗂️</div><h3 class="text-xl font-bold text-white mb-4">Apps Mapping</h3><p class="text-gray-300 leading-relaxed">File-based routing system similar to Next.js App Router with layouts and nested routes.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.3s"><div class="text-4xl mb-4">🌊</div><h3 class="text-xl font-bold text-white mb-4">Streaming SSR</h3><p class="text-gray-300 leading-relaxed">Server-side rendering with streaming, React Server Components, and Suspense support.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.4s"><div class="text-4xl mb-4">🎨</div><h3 class="text-xl font-bold text-white mb-4">KilatCSS &amp; KilatAnim</h3><p class="text-gray-300 leading-relaxed">Built-in styling system with Tailwind integration and smooth animations out of the box.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.5s"><div class="text-4xl mb-4">🔌</div><h3 class="text-xl font-bold text-white mb-4">Plugin System</h3><p class="text-gray-300 leading-relaxed">Extensible architecture with built-in plugins and easy third-party integration.</p></div><div class="kilat-card kilat-glass p-8 text-center kilat-fade-in kilat-glow-hover" style="animation-delay:0.6s"><div class="text-4xl mb-4">🚀</div><h3 class="text-xl font-bold text-white mb-4">Production Ready</h3><p class="text-gray-300 leading-relaxed">ISR, caching, error handling, OTA updates, and all production optimizations included.</p></div></div></div></div><section class="py-24 relative"><div class="kilat-container text-center"><div class="kilat-fade-in"><h2 class="text-3xl md:text-4xl font-bold text-white mb-6">Ready to build something amazing?</h2><p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">Join thousands of developers building the next generation of web applications with Kilat.js.</p><a href="/dashboard" class="kilat-btn kilat-btn-primary px-10 py-4 text-lg font-semibold kilat-glow-hover transform hover:scale-105 transition-all duration-300">Start Building Now<span class="ml-2">🚀</span></a></div></div></section></main><footer class="relative z-10 mt-20 border-t border-white/10 kilat-glass"><div class="kilat-container py-12"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="col-span-1 md:col-span-2"><div class="flex items-center space-x-2 mb-4"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><span class="text-white font-bold text-sm">K</span></div><span class="text-xl font-bold kilat-gradient-text">Kilat.js</span></div><p class="text-gray-400 max-w-md mb-6">Modern fullstack framework with SpeedRun™ Runtime. Built for speed, developer experience, and production-ready applications.</p><div class="flex items-center space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">📧</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐙</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">🐦</span></a><a href="#" class="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"><span class="text-gray-300">💬</span></a></div></div><div><h3 class="text-white font-semibold mb-4">Quick Links</h3><ul class="space-y-2"><li><a href="/" class="text-gray-400 hover:text-white transition-colors">🏠 Home</a></li><li><a href="/about" class="text-gray-400 hover:text-white transition-colors">📄 About</a></li><li><a href="/dashboard" class="text-gray-400 hover:text-white transition-colors">📊 Dashboard</a></li><li><a href="/api/users" class="text-gray-400 hover:text-white transition-colors">🛠️ API</a></li><li><a href="/login" class="text-gray-400 hover:text-white transition-colors">🔑 Login</a></li></ul></div><div><h3 class="text-white font-semibold mb-4">Resources</h3><ul class="space-y-2"><li><a href="#" class="text-gray-400 hover:text-white transition-colors">📚 Documentation</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🚀 Getting Started</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">💡 Examples</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">🎓 Tutorials</a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors">👥 Community</a></li></ul></div></div><div class="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row items-center justify-between"><p class="text-gray-400 text-sm">© 2024 Kilat.js. Built with ❤️ using SpeedRun™ Runtime.</p><div class="flex items-center space-x-6 mt-4 md:mt-0"><span class="text-gray-400 text-sm">Powered by</span><div class="flex items-center space-x-4"><div class="flex items-center space-x-2"><span class="text-yellow-400">🟡</span><span class="text-gray-300 text-sm font-medium">Bun.js</span></div><div class="flex items-center space-x-2"><span class="text-blue-400">⚛️</span><span class="text-gray-300 text-sm font-medium">React 18</span></div><div class="flex items-center space-x-2"><span class="text-purple-400">⚡</span><span class="text-gray-300 text-sm font-medium">TypeScript</span></div></div></div></div><div class="mt-8 text-center"><div class="inline-flex items-center px-4 py-2 kilat-glass rounded-full text-sm text-gray-300"><span class="mr-2">⚡</span>SpeedRun™ Runtime - Lightning Fast Performance<span class="ml-2 text-green-400">●</span></div></div></div></footer></div></div>
  
  <!-- Kilat.js Runtime -->
  <script src="/_kilat/runtime.js" defer></script>
  <script src="/_kilat/client.js" defer></script>
  <script src="/_kilat/kilat-anim.js" defer></script>
</body>
</html>