/**
 * 🌊 Streaming SSR - Server-Side Rendering with Streaming
 */

import type { KilatContext } from '../../types/config'
import { generateHtmlDocument } from './html-generator'

export interface StreamOptions {
  onShellReady?: () => void
  onAllReady?: () => void
  onError?: (error: Error) => void
}

/**
 * Create a streaming SSR response
 */
export async function createStreamingResponse(
  element: React.ReactElement,
  context: KilatContext,
  options?: StreamOptions
): Promise<Response> {
  try {
    // Import React DOM server with dynamic import
    const { renderToPipeableStream } = await import('react-dom/server')
    
    let didError = false
    
    const stream = new ReadableStream({
      start(controller) {
        const { pipe, abort } = renderToPipeableStream(element, {
          onShellReady() {
            // Start streaming the shell
            const encoder = new TextEncoder()
            controller.enqueue(encoder.encode(generateHtmlDocument(context, 'start')))
            
            if (options?.onShellReady) {
              options.onShellReady()
            }
          },
          onAllReady() {
            // Finish streaming
            const encoder = new TextEncoder()
            controller.enqueue(encoder.encode(generateHtmlDocument(context, 'end')))
            controller.close()
            
            if (options?.onAllReady) {
              options.onAllReady()
            }
          },
          onError(error: Error) {
            didError = true
            console.error('❌ Streaming render error:', error)
            
            if (options?.onError) {
              options.onError(error)
            }
            
            controller.error(error)
          }
        })
        
        // Pipe to controller
        const writer = new WritableStream({
          write(chunk) {
            controller.enqueue(chunk)
          }
        })
        
        pipe(writer as any)
      }
    })
    
    return new Response(stream, {
      status: didError ? 500 : 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-store, must-revalidate',
      }
    })
    
  } catch (error) {
    console.error('❌ Failed to create streaming response:', error)
    throw error
  }
}

/**
 * Create a static SSR response
 */
export async function createStaticResponse(
  element: React.ReactElement,
  context: KilatContext
): Promise<Response> {
  try {
    // Import React DOM server
    const { renderToString } = await import('react-dom/server')
    
    // Render to string
    const html = renderToString(element)
    
    // Create complete HTML document
    const document = `
      ${generateHtmlDocument(context, 'start')}
      ${html}
      ${generateHtmlDocument(context, 'end')}
    `
    
    return new Response(document, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': context.mode === 'production' 
          ? 'public, max-age=3600, s-maxage=86400' 
          : 'no-store, must-revalidate',
      }
    })
    
  } catch (error) {
    console.error('❌ Static render error:', error)
    throw error
  }
}

/**
 * Create a streaming data response
 */
export async function createStreamingDataResponse<T>(
  dataStream: ReadableStream<T>,
  contentType = 'application/json'
): Promise<Response> {
  return new Response(dataStream, {
    headers: {
      'Content-Type': contentType,
      'Transfer-Encoding': 'chunked',
      'Cache-Control': 'no-store, must-revalidate',
    }
  })
}

/**
 * Create a server-sent events response
 */
export function createSSEResponse(): { response: Response, send: (data: any) => void, close: () => void } {
  const encoder = new TextEncoder()
  const stream = new TransformStream()
  const writer = stream.writable.getWriter()
  
  const send = (data: any) => {
    const message = typeof data === 'string' ? data : JSON.stringify(data)
    writer.write(encoder.encode(`data: ${message}\n\n`))
  }
  
  const close = () => {
    writer.close()
  }
  
  const response = new Response(stream.readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    }
  })
  
  return { response, send, close }
}
