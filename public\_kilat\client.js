/**
 * 🌐 Kilat.js Client - Client-side Features
 */

(function() {
  'use strict';

  // Wait for <PERSON><PERSON> runtime to be ready
  function initClient() {
    if (!window.Kilat || !window.Kilat.initialized) {
      setTimeout(initClient, 50);
      return;
    }

    console.log('🌐 Kilat.js Client initializing...');

    // Initialize advanced theme system
    initAdvancedThemeSystem();

    // Initialize mobile menu
    initMobileMenu();

    // Client-side navigation
    const navigation = {
      init: function() {
        // Handle navigation links
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a[href^="/"]');
          if (link && !link.hasAttribute('target') && !e.ctrlKey && !e.metaKey) {
            // For now, use regular navigation
            // In future versions, implement SPA navigation
          }
        });
      }
    };

    // Form handling
    const forms = {
      init: function() {
        document.addEventListener('submit', function(e) {
          const form = e.target;
          if (form.hasAttribute('data-kilat-form')) {
            e.preventDefault();
            forms.handleSubmit(form);
          }
        });
      },
      
      handleSubmit: function(form) {
        const formData = new FormData(form);
        const action = form.action || window.location.href;
        const method = form.method || 'POST';
        
        // Show loading state
        const submitBtn = form.querySelector('[type="submit"]');
        if (submitBtn) {
          submitBtn.disabled = true;
          submitBtn.textContent = 'Loading...';
        }
        
        fetch(action, {
          method: method,
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          // Handle response
          if (data.success) {
            window.Kilat.emit('form:success', { form, data });
          } else {
            window.Kilat.emit('form:error', { form, data });
          }
        })
        .catch(error => {
          window.Kilat.emit('form:error', { form, error });
        })
        .finally(() => {
          // Reset loading state
          if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = submitBtn.getAttribute('data-original-text') || 'Submit';
          }
        });
      }
    };

    // Lazy loading
    const lazyLoading = {
      init: function() {
        if ('IntersectionObserver' in window) {
          const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target;
                if (img.dataset.src) {
                  img.src = img.dataset.src;
                  img.removeAttribute('data-src');
                  imageObserver.unobserve(img);
                }
              }
            });
          });

          document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
          });
        }
      }
    };

    // Smooth scrolling
    const smoothScroll = {
      init: function() {
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a[href^="#"]');
          if (link) {
            e.preventDefault();
            const target = document.querySelector(link.getAttribute('href'));
            if (target) {
              target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          }
        });
      }
    };

    // Theme switcher
    const themeSwitcher = {
      init: function() {
        // Create theme switcher if it doesn't exist
        if (!document.querySelector('.kilat-theme-switcher')) {
          this.createSwitcher();
        }
        
        // Handle theme buttons
        document.addEventListener('click', function(e) {
          if (e.target.hasAttribute('data-theme')) {
            const theme = e.target.getAttribute('data-theme');
            window.Kilat.modules.theme.set(theme);
          }
        });
      },
      
      createSwitcher: function() {
        const switcher = document.createElement('div');
        switcher.className = 'kilat-theme-switcher fixed top-4 right-4 z-50 flex gap-2';
        switcher.innerHTML = `
          <button data-theme="default" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm" title="Default Theme">🌙</button>
          <button data-theme="cyber" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm" title="Cyber Theme">🤖</button>
          <button data-theme="nusantara" class="kilat-btn kilat-btn-outline px-3 py-2 text-sm" title="Nusantara Theme">🏛️</button>
        `;
        document.body.appendChild(switcher);
      }
    };

    // Performance monitoring
    const performance = {
      init: function() {
        // Monitor Core Web Vitals
        if ('web-vital' in window) {
          // This would integrate with web-vitals library
        }
        
        // Monitor page load time
        window.addEventListener('load', function() {
          setTimeout(() => {
            const perfData = window.performance.getEntriesByType('navigation')[0];
            if (perfData) {
              console.log('📊 Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
              
              // Send to analytics if available
              if (window.__KILAT_ANALYTICS__) {
                window.__KILAT_ANALYTICS__.track('performance', {
                  loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                  domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart
                });
              }
            }
          }, 0);
        });
      }
    };

    // Initialize all modules
    navigation.init();
    forms.init();
    lazyLoading.init();
    smoothScroll.init();
    themeSwitcher.init();
    performance.init();

    console.log('✅ Kilat.js Client initialized');
    window.Kilat.emit('client:ready');
  }

  // Mobile Menu System
  function initMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (!mobileMenuButton || !mobileMenu) return;

    let isMenuOpen = false;

    mobileMenuButton.addEventListener('click', function() {
      isMenuOpen = !isMenuOpen;

      if (isMenuOpen) {
        mobileMenu.classList.remove('opacity-0', 'invisible');
        mobileMenu.classList.add('opacity-100', 'visible');

        // Change hamburger to X
        mobileMenuButton.innerHTML = `
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        `;
      } else {
        mobileMenu.classList.add('opacity-0', 'invisible');
        mobileMenu.classList.remove('opacity-100', 'visible');

        // Change X back to hamburger
        mobileMenuButton.innerHTML = `
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        `;
      }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
      if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target)) {
        if (isMenuOpen) {
          mobileMenuButton.click();
        }
      }
    });

    // Close menu when clicking on a link
    mobileMenu.addEventListener('click', function(e) {
      if (e.target.tagName === 'A') {
        if (isMenuOpen) {
          mobileMenuButton.click();
        }
      }
    });
  }

  // Advanced Theme System
  function initAdvancedThemeSystem() {
    const themes = {
      default: {
        name: 'Default',
        primary: 'from-blue-500 to-purple-500',
        accent: 'from-purple-500 to-pink-500',
        background: '#0f172a',
        surface: 'rgba(255, 255, 255, 0.1)'
      },
      cyber: {
        name: 'Cyber',
        primary: 'from-green-400 to-cyan-500',
        accent: 'from-pink-500 to-red-500',
        background: '#000000',
        surface: 'rgba(0, 255, 0, 0.1)'
      },
      nusantara: {
        name: 'Nusantara',
        primary: 'from-orange-500 to-red-500',
        accent: 'from-yellow-500 to-orange-500',
        background: '#1a0f0a',
        surface: 'rgba(255, 165, 0, 0.1)'
      }
    };

    // Load saved theme
    const savedTheme = localStorage.getItem('kilat-theme') || 'default';
    applyTheme(savedTheme);

    // Setup theme switcher buttons
    document.addEventListener('click', function(e) {
      const themeBtn = e.target.closest('.kilat-theme-btn');
      if (themeBtn) {
        const theme = themeBtn.getAttribute('data-theme');
        if (theme && themes[theme]) {
          applyTheme(theme);
          localStorage.setItem('kilat-theme', theme);

          // Update active state
          document.querySelectorAll('.kilat-theme-btn').forEach(btn => {
            btn.classList.remove('active');
          });
          themeBtn.classList.add('active');
        }
      }
    });

    function applyTheme(themeName) {
      const theme = themes[themeName];
      if (!theme) return;

      const root = document.documentElement;

      // Apply CSS custom properties
      root.style.setProperty('--theme-background', theme.background);
      root.style.setProperty('--theme-surface', theme.surface);

      // Update body background
      document.body.style.background = theme.background;

      // Update gradient classes
      updateGradients(theme);

      // Mark active theme button
      document.querySelectorAll('.kilat-theme-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-theme') === themeName) {
          btn.classList.add('active');
        }
      });

      console.log(`🎨 Applied theme: ${theme.name}`);
    }

    function updateGradients(theme) {
      // Update gradient backgrounds dynamically
      const gradientElements = document.querySelectorAll('.kilat-gradient-text, .bg-gradient-to-r');
      gradientElements.forEach(el => {
        if (el.classList.contains('kilat-gradient-text')) {
          el.style.background = `linear-gradient(45deg, ${theme.primary.replace('from-', '').replace(' to-', ', ')})`;
          el.style.webkitBackgroundClip = 'text';
          el.style.webkitTextFillColor = 'transparent';
        }
      });
    }
  }

  // Start initialization
  initClient();

})();
