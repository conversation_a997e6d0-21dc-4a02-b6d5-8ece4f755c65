/**
 * 🖼️ Static File Server
 */

import { readFile, stat } from 'fs/promises'
import { join, extname } from 'path'
import type { KilatContext } from '../../types/config'

const MIME_TYPES: Record<string, string> = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.mjs': 'application/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.mp4': 'video/mp4',
  '.webm': 'video/webm',
  '.mp3': 'audio/mpeg',
  '.wav': 'audio/wav',
  '.pdf': 'application/pdf',
  '.txt': 'text/plain',
  '.md': 'text/markdown',
}

/**
 * Serve static files
 */
export async function serveStatic(
  pathname: string,
  context: KilatContext
): Promise<Response> {
  try {
    let filePath: string

    // Handle Kilat.js internal files
    if (pathname.startsWith('/_kilat/')) {
      filePath = join(process.cwd(), 'public', pathname)
    } else {
      // Handle public files
      filePath = join(process.cwd(), 'public', pathname.replace('/public/', ''))
    }
    
    // Check if file exists
    try {
      const stats = await stat(filePath)
      if (!stats.isFile()) {
        return new Response('Not Found', { status: 404 })
      }
    } catch {
      return new Response('Not Found', { status: 404 })
    }
    
    // Read file
    const fileBuffer = await readFile(filePath)
    
    // Determine content type
    const ext = extname(filePath).toLowerCase()
    const contentType = MIME_TYPES[ext] || 'application/octet-stream'
    
    // Set cache headers
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Length': fileBuffer.length.toString(),
    })
    
    // Cache static assets in production
    if (context.mode === 'production') {
      if (isStaticAsset(ext)) {
        headers.set('Cache-Control', 'public, max-age=31536000, immutable')
      } else {
        headers.set('Cache-Control', 'public, max-age=3600')
      }
    } else {
      // No cache in development
      headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    }
    
    return new Response(fileBuffer, {
      status: 200,
      headers
    })
    
  } catch (error) {
    console.error('❌ Static file serve error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}

/**
 * Check if file is a static asset that can be cached long-term
 */
function isStaticAsset(ext: string): boolean {
  const staticAssets = [
    '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',
    '.woff', '.woff2', '.ttf', '.eot',
    '.mp4', '.webm', '.mp3', '.wav',
    '.pdf'
  ]
  
  return staticAssets.includes(ext)
}

/**
 * Generate ETag for file
 */
export function generateETag(content: Buffer): string {
  const hash = require('crypto').createHash('md5').update(content).digest('hex')
  return `"${hash}"`
}

/**
 * Check if request has matching ETag
 */
export function checkETag(request: Request, etag: string): boolean {
  const ifNoneMatch = request.headers.get('if-none-match')
  return ifNoneMatch === etag
}
