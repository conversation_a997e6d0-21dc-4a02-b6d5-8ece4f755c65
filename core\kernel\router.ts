/**
 * 🔀 Router - Route Matching and Navigation
 */

import type { AppRoute } from '../../types/config'

export interface RouteMatch {
  route: AppRoute
  params: Record<string, string>
  query: Record<string, string>
  searchParams: URLSearchParams
  metadata?: RouteMetadata
}

export interface RouteMetadata {
  title?: string
  description?: string
  keywords?: string[]
  canonical?: string
  noindex?: boolean
  priority?: number
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
}

export interface RouteGroup {
  name: string
  prefix: string
  middleware?: string[]
  routes: AppRoute[]
}

export interface RouterOptions {
  caseSensitive?: boolean
  trailingSlash?: 'always' | 'never' | 'ignore'
  basePath?: string
  locale?: string
}

/**
 * Match incoming request to route
 */
export function matchRoute(pathname: string, routes: AppRoute[], searchParams?: URLSearchParams): RouteMatch | null {
  // Remove trailing slash (except for root)
  const normalizedPath = pathname === '/' ? '/' : pathname.replace(/\/$/, '')

  for (const route of routes) {
    const match = matchPath(normalizedPath, route.path)
    if (match) {
      return {
        route,
        params: match.params,
        query: {}, // Will be populated by server
        searchParams: searchParams || new URLSearchParams(),
        metadata: route.metadata
      }
    }
  }

  return null
}

/**
 * Match path against route pattern
 */
function matchPath(pathname: string, routePattern: string): { params: Record<string, string> } | null {
  // Exact match
  if (pathname === routePattern) {
    return { params: {} }
  }
  
  // Dynamic route matching
  const pathSegments = pathname.split('/').filter(Boolean)
  const routeSegments = routePattern.split('/').filter(Boolean)
  
  // Different number of segments (unless catch-all)
  if (pathSegments.length !== routeSegments.length && !routePattern.includes('*')) {
    return null
  }
  
  const params: Record<string, string> = {}
  
  for (let i = 0; i < routeSegments.length; i++) {
    const routeSegment = routeSegments[i]
    const pathSegment = pathSegments[i]
    
    // Catch-all route
    if (routeSegment.startsWith('*')) {
      const paramName = routeSegment.slice(1)
      params[paramName] = pathSegments.slice(i).join('/')
      return { params }
    }
    
    // Dynamic parameter
    if (routeSegment.startsWith(':')) {
      const paramName = routeSegment.slice(1)
      params[paramName] = pathSegment
      continue
    }
    
    // Static segment must match exactly
    if (routeSegment !== pathSegment) {
      return null
    }
  }
  
  return { params }
}

/**
 * Generate URL from route and parameters
 */
export function generateUrl(routePattern: string, params: Record<string, string> = {}): string {
  let url = routePattern
  
  // Replace dynamic parameters
  for (const [key, value] of Object.entries(params)) {
    url = url.replace(`:${key}`, encodeURIComponent(value))
    url = url.replace(`*${key}`, value) // Catch-all doesn't need encoding
  }
  
  return url
}

/**
 * Extract parameters from URL
 */
export function extractParams(pathname: string, routePattern: string): Record<string, string> {
  const match = matchPath(pathname, routePattern)
  return match ? match.params : {}
}

/**
 * Check if route is dynamic
 */
export function isDynamicRoute(routePattern: string): boolean {
  return routePattern.includes(':') || routePattern.includes('*')
}

/**
 * Get route priority for sorting
 */
export function getRoutePriority(routePattern: string): number {
  const segments = routePattern.split('/').filter(Boolean)
  let priority = 0
  
  for (const segment of segments) {
    if (segment.startsWith('*')) {
      priority -= 1000 // Catch-all has lowest priority
    } else if (segment.startsWith(':')) {
      priority -= 100 // Dynamic segments have lower priority
    } else {
      priority += 100 // Static segments have higher priority
    }
  }
  
  // Longer routes have higher priority
  priority += segments.length * 10
  
  return priority
}

/**
 * Sort routes by priority
 */
export function sortRoutesByPriority(routes: AppRoute[]): AppRoute[] {
  return [...routes].sort((a, b) => {
    const priorityA = getRoutePriority(a.path)
    const priorityB = getRoutePriority(b.path)
    return priorityB - priorityA
  })
}
