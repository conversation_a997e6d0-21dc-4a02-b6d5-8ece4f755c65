/**
 * 🗂️ Apps Mapping Loader - File-based Routing System
 * Similar to Next.js App Router but with Kilat.js enhancements
 */

import { readdir, stat } from 'fs/promises'
import { existsSync } from 'fs'
import { join, extname, relative } from 'path'
import type { AppRoute } from '../../types/config'

export interface AppsConfig {
  dir: string
  extensions: string[]
  middleware: boolean
  groups: boolean
}

/**
 * Load all routes from apps directory
 */
export async function loadAppsMapping(config: AppsConfig): Promise<AppRoute[]> {
  console.log('🗂️ Loading Apps Mapping...')
  
  const routes: AppRoute[] = []
  const appsDir = config.dir
  
  try {
    await scanDirectory(appsDir, appsDir, routes, config)
    
    // Sort routes by specificity (more specific routes first)
    routes.sort((a, b) => {
      const aSegments = a.path.split('/').length
      const bSegments = b.path.split('/').length
      return bSegments - aSegments
    })
    
    console.log(`✅ Loaded ${routes.length} routes from Apps Mapping`)
    return routes
  } catch (error) {
    console.error('❌ Failed to load Apps Mapping:', error)
    return []
  }
}

/**
 * Recursively scan directory for route files
 */
async function scanDirectory(
  currentDir: string,
  baseDir: string,
  routes: AppRoute[],
  config: AppsConfig
): Promise<void> {
  try {
    const entries = await readdir(currentDir, { withFileTypes: true })
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name)
      
      if (entry.isDirectory()) {
        // Recursively scan subdirectories
        await scanDirectory(fullPath, baseDir, routes, config)
      } else if (entry.isFile()) {
        // Process route files
        await processRouteFile(fullPath, baseDir, routes, config)
      }
    }
  } catch (error) {
    console.warn(`⚠️ Could not scan directory ${currentDir}:`, error)
  }
}

/**
 * Process individual route file
 */
async function processRouteFile(
  filePath: string,
  baseDir: string,
  routes: AppRoute[],
  config: AppsConfig
): Promise<void> {
  const ext = extname(filePath)
  const fileName = filePath.split(/[/\\]/).pop() || ''
  
  // Check if file extension is supported
  if (!config.extensions.includes(ext)) {
    return
  }
  
  // Skip non-route files
  if (!isRouteFile(fileName)) {
    return
  }
  
  const relativePath = relative(baseDir, filePath)
  const routePath = convertFilePathToRoute(relativePath)
  
  const route: AppRoute = {
    path: routePath,
    component: filePath,
  }
  
  // Check for layout file
  const layoutPath = findLayoutFile(filePath, baseDir, config)
  if (layoutPath) {
    route.layout = layoutPath
  }
  
  // Check for middleware
  if (config.middleware) {
    const middlewarePaths = findMiddlewareFiles(filePath, baseDir, config)
    if (middlewarePaths.length > 0) {
      route.middleware = middlewarePaths
    }
  }
  
  // Check for groups
  if (config.groups) {
    const groups = extractGroups(relativePath)
    if (groups.length > 0) {
      route.groups = groups
    }
  }
  
  routes.push(route)
}

/**
 * Check if file is a route file
 */
function isRouteFile(fileName: string): boolean {
  const routeFiles = [
    'page.tsx', 'page.ts', 'page.jsx', 'page.js',
    'route.tsx', 'route.ts', 'route.jsx', 'route.js',
    // Layout files are NOT routes, they are layouts
  ]

  return routeFiles.some(routeFile => fileName === routeFile)
}

/**
 * Convert file path to route path
 */
function convertFilePathToRoute(filePath: string): string {
  let routePath = filePath
    .replace(/\\/g, '/') // Normalize path separators
    .replace(/\.(tsx|ts|jsx|js)$/, '') // Remove extension

  // Handle root page specially
  if (routePath === 'page') {
    return '/'
  }

  // Remove page/route/layout suffixes
  routePath = routePath
    .replace(/\/page$/, '') // Remove /page suffix
    .replace(/\/route$/, '') // Remove /route suffix
    .replace(/\/layout$/, '') // Remove /layout suffix

  // Handle dynamic routes [param]
  routePath = routePath.replace(/\[([^\]]+)\]/g, ':$1')

  // Handle catch-all routes [...param]
  routePath = routePath.replace(/\[\.\.\.([^\]]+)\]/g, '*$1')

  // Ensure route starts with /
  if (!routePath.startsWith('/')) {
    routePath = '/' + routePath
  }

  // Handle empty route (should be root)
  if (routePath === '/' || routePath === '') {
    return '/'
  }

  // Remove trailing slash
  return routePath.replace(/\/$/, '') || '/'
}

/**
 * Find layout file for route
 */
function findLayoutFile(routeFilePath: string, baseDir: string, config: AppsConfig): string | undefined {
  const routeDir = routeFilePath.substring(0, routeFilePath.lastIndexOf('/'))

  for (const ext of config.extensions) {
    const layoutPath = join(routeDir, `layout${ext}`)
    try {
      // Check if layout file exists
      if (existsSync(layoutPath)) {
        return layoutPath
      }
    } catch {
      continue
    }
  }

  return undefined
}

/**
 * Find middleware files for route
 */
function findMiddlewareFiles(routeFilePath: string, baseDir: string, config: AppsConfig): string[] {
  const middlewares: string[] = []
  const routeDir = routeFilePath.substring(0, routeFilePath.lastIndexOf('/'))
  
  for (const ext of config.extensions) {
    const middlewarePath = join(routeDir, `middleware${ext}`)
    try {
      // Check if middleware file exists (simplified check)
      middlewares.push(middlewarePath)
    } catch {
      continue
    }
  }
  
  return middlewares
}

/**
 * Extract groups from file path
 */
function extractGroups(filePath: string): string[] {
  const groups: string[] = []
  const segments = filePath.split('/')
  
  for (const segment of segments) {
    // Group pattern: group.name.routes.ts or group.name.middleware.ts
    const groupMatch = segment.match(/^group\.([^.]+)\.(routes|middleware)\./)
    if (groupMatch) {
      groups.push(groupMatch[1])
    }
  }
  
  return groups
}
